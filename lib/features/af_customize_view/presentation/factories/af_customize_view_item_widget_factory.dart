import 'package:asset_force_mobile_v2/core/extensions/nullable_extensions.dart';
import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/enums/schedule_item_ids.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_asset_count_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_checkbox_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_degital_sign_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_file_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_image_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_label_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_master_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_radio_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_rfid_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_talk_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_text_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_user_select_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 创建条目的工厂类
class ItemWidgetFactory {
  /// 创建条目的工厂方法
  static Iterable<Widget> buildItemWidget(RxAssetItemWrapper entry, {String? assetId}) {
    final controller = Get.find<AfCustomizeViewController>(tag: assetId);
    final builder = _widgetBuilders[entry.itemType];

    if (builder != null) {
      final itemWidget = builder(entry, assetId, controller);
      // 如果是label类型，则不添加分割线
      if (SharedItemTypeEnum.label.equals(entry.itemType)) {
        return [itemWidget];
      }
      return [_buildSplitLine(), itemWidget];
    } else {
      return [];
    }
  }

  static final Map<String, Widget Function(RxAssetItemWrapper, String?, AfCustomizeViewController)> _widgetBuilders = {
    SharedItemTypeEnum.talkList.value: _buildTalkItemWidget,
    SharedItemTypeEnum.label.value: _buildLabelItemWidget,
    SharedItemTypeEnum.file.value: _buildFileItemWidget,
    SharedItemTypeEnum.rfid.value: _buildRfidItemWidget,
    SharedItemTypeEnum.checkbox.value: _buildCheckboxItemWidget,
    SharedItemTypeEnum.radio.value: _buildRadioItemWidget,
    SharedItemTypeEnum.currency.value: _buildNormalItemWidget,
    SharedItemTypeEnum.image.value: _buildImageItemWidget,
    SharedItemTypeEnum.textarea.value: _buildNormalItemWidget,
    SharedItemTypeEnum.map.value: _buildNormalItemWidget,
    SharedItemTypeEnum.email.value: _buildNormalItemWidget,
    SharedItemTypeEnum.input.value: _buildNormalItemWidget,
    SharedItemTypeEnum.date.value: _buildDateItemWidget,
    SharedItemTypeEnum.list.value: _buildNormalItemWidget,
    SharedItemTypeEnum.number.value: _buildNumberItemWidget,
    SharedItemTypeEnum.userSelect.value: _buildUserSelectItemWidget,
    SharedItemTypeEnum.calculate.value: _buildCalculateItemWidget,
    SharedItemTypeEnum.hyperlink.value: _buildNormalItemWidget,
    SharedItemTypeEnum.appurInfoSummary.value: _buildNormalItemWidget,
    SharedItemTypeEnum.master.value: _buildMasterItemWidget,
    SharedItemTypeEnum.groupSelect.value: _buildNormalItemWidget,
    SharedItemTypeEnum.digitalSign.value: _buildDegitalSignItemWidget,
  };

  /// 构建日期项组件
  ///
  /// 用于创建日期类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回一个 [AfCustomizeTextItemView] 组件实例，包含以下属性：
  /// - entry: 资产项数据
  /// - isPreview: 是否为预览模式，使用 Obx 响应 previewFlg 变化
  /// - assetId: 资产ID，用于标识当前资产
  static Widget _buildDateItemWidget(RxAssetItemWrapper entry, String? assetId, AfCustomizeViewController controller) {
    return Obx(() => AfCustomizeTextItemView(entry: entry, isPreview: controller.previewFlg.value, assetId: assetId));
  }

  /// 构建 Master 项组件
  ///
  /// 用于创建 Master 类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回一个 [AfCustomizeMasterItemView] 组件实例，包含以下属性：
  /// - entry: 资产项数据
  /// - isPreview: 是否为预览模式，使用 Obx 响应 previewFlg 变化
  /// - assetId: 资产ID，用于标识当前资产
  static Widget _buildMasterItemWidget(
    RxAssetItemWrapper entry,
    String? assetId,
    AfCustomizeViewController controller,
  ) {
    return Obx(() => AfCustomizeMasterItemView(entry: entry, isPreview: controller.previewFlg.value, assetId: assetId));
  }

  /// 构建数字签名项组件
  ///
  /// 用于创建数字签名类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回一个 [AfCustomizeDigitalSignItemView] 组件实例，包含以下属性：
  /// - entry: 资产项数据
  /// - isPreview: 是否为预览模式，使用 Obx 响应 previewFlg 变化
  /// - assetId: 资产ID，用于标识当前资产
  static Widget _buildDegitalSignItemWidget(
    RxAssetItemWrapper entry,
    String? assetId,
    AfCustomizeViewController controller,
  ) {
    return Obx(
      () => AfCustomizeDigitalSignItemView(entry: entry, isPreview: controller.previewFlg.value, assetId: assetId),
    );
  }

  /// 构建数字项组件
  ///
  /// 用于创建数字类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回说明：
  /// - 当 itemId 为 8 时，返回 [AfCustomizeAssetCountItemView] 组件，用于显示资产数量
  /// - 其他情况返回普通项组件 [_buildNormalItemWidget]
  static Widget _buildNumberItemWidget(
    RxAssetItemWrapper entry,
    String? assetId,
    AfCustomizeViewController controller,
  ) {
    // 资产数量
    if (entry.itemId == 8) {
      return Obx(
        () => AfCustomizeAssetCountItemView(isPreview: controller.previewFlg.value, entry: entry, assetId: assetId),
      );
    }
    // 其他数字项
    return _buildNormalItemWidget(entry, assetId, controller);
  }

  /// 构建 Live Talk 组件
  ///
  /// 用于创建 Live Talk 类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取直播URL
  ///
  /// 返回一个 [AfCustomizeTalkItemView] 组件实例，包含以下属性：
  /// - talkUrl: 直播URL，从控制器中获取
  static Widget _buildTalkItemWidget(RxAssetItemWrapper entry, String? assetId, AfCustomizeViewController controller) {
    return AfCustomizeTalkItemView(talkUrl: controller.liveTalkUrl.value, assetId: assetId);
  }

  /// 构建 Label 标签项组件
  ///
  /// 用于创建标签类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回一个 [AfCustomizeLabelItemView] 组件实例，包含以下属性：
  /// - title: 标签文本内容，从 entry.model.defaultData 获取，为空时使用空字符串
  /// - color: 标签文本颜色，从 entry.model.optionObject 获取
  /// - fontSize: 标签文本字体大小，从 entry.model.optionObject 获取
  /// - fontWeight: 标签文本字体粗细，从 entry.model.optionObject 获取
  /// - lineHeight: 标签文本行高，从 entry.model.optionObject 获取
  static Widget _buildLabelItemWidget(RxAssetItemWrapper entry, String? assetId, AfCustomizeViewController controller) {
    return AfCustomizeLabelItemView(
      title: entry.defaultData?.toString() ?? '',
      color: entry.optionObject?.color,
      fontSize: entry.optionObject?.fontSize,
      fontWeight: entry.optionObject?.fontWeight,
      lineHeight: entry.optionObject?.lineHeight,
    );
  }

  /// 构建文件项组件
  ///
  /// 用于创建文件类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回一个 [AfCustomizeFileItemView] 组件实例
  static Widget _buildFileItemWidget(RxAssetItemWrapper entry, String? assetId, AfCustomizeViewController controller) {
    return Obx(() => AfCustomizeFileItemView(entry: entry, isPreview: controller.previewFlg.value, assetId: assetId));
  }

  /// 构建 RFID 组件
  ///
  /// 用于创建 RFID 类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回一个 [AfCustomizeRfidItemView] 组件实例
  static Widget _buildRfidItemWidget(RxAssetItemWrapper entry, String? assetId, AfCustomizeViewController controller) {
    return Obx(() => AfCustomizeRfidItemView(entry: entry, isPreview: controller.previewFlg.value, assetId: assetId));
  }

  /// 构建 Checkbox 复选框组件
  ///
  /// 用于创建复选框类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回一个 [AfCustomizeCheckboxItemView] 组件实例
  static Widget _buildCheckboxItemWidget(
    RxAssetItemWrapper entry,
    String? assetId,
    AfCustomizeViewController controller,
  ) {
    return Obx(
      () => AfCustomizeCheckboxItemView(entry: entry, isPreview: controller.previewFlg.value, assetId: assetId),
    );
  }

  /// 构建 Radio 单选按钮组件
  ///
  /// 用于创建单选按钮类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回一个 [AfCustomizeRadioItemView] 组件实例
  static Widget _buildRadioItemWidget(RxAssetItemWrapper entry, String? assetId, AfCustomizeViewController controller) {
    return Obx(() => AfCustomizeRadioItemView(entry: entry, isPreview: controller.previewFlg.value, assetId: assetId));
  }

  /// 构建普通项组件
  ///
  /// 用于创建普通文本类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回一个 [AfCustomizeTextItemView] 组件实例
  static Widget _buildNormalItemWidget(
    RxAssetItemWrapper entry,
    String? assetId,
    AfCustomizeViewController controller,
  ) {
    final isScheduleEventType =
        controller.scene == AfCustomizeViewScene.assetSchedule && ScheduleItemIds.eventType.equals(entry.itemId);

    if (isScheduleEventType) {
      return Obx(
        () => Column(
          children: [
            AfCustomizeTextItemView(entry: entry, isPreview: controller.previewFlg.value, assetId: assetId),
            if (controller.eventTypeItems.isNotEmpty)
              ...controller.eventTypeItems
                  .map((eventTypeItem) {
                    final wrapper = RxAssetItemWrapper.fromEventTypeItem(eventTypeItem);

                    // 改进的数据绑定逻辑，避免不匹配的数据被应用到新布局
                    controller.appointment?.let((appointment) {
                      final extraCommonTextObj = appointment.extraCommonTextObj as Map<String, dynamic>?;
                      final reservationTextObj = appointment.reservationTextObj as Map<String, dynamic>?;

                      // 检查当前项目是否属于当前选中的事件类型
                      final currentEventTypeId = appointment.eventTypeId;
                      final itemEventTypeId = _getEventTypeIdForItem(eventTypeItem, controller);

                      // 只有当项目属于当前事件类型时才绑定数据
                      if (currentEventTypeId == itemEventTypeId) {
                        final value =
                            extraCommonTextObj?[wrapper.itemId?.toString()] ??
                            reservationTextObj?[wrapper.itemId?.toString()];
                        // 仅在有值时更新，但不触发onItemChanged（因为这是初始化）
                        if (value != null && value.toString().isNotEmpty) {
                          wrapper.updateDefaultData(value);
                        }
                      }
                      // 如果不属于当前事件类型，确保清空默认数据
                      else {
                        wrapper.updateDefaultData('');
                      }
                    });

                    return ItemWidgetFactory.buildItemWidget(wrapper, assetId: assetId);
                  })
                  .expand((element) => element),
          ],
        ),
      );
    }

    return Obx(() => AfCustomizeTextItemView(entry: entry, isPreview: controller.previewFlg.value, assetId: assetId));
  }

  /// 构建图片项组件
  ///
  /// 用于创建图片类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回一个 [AfCustomizeImageItemView] 组件实例
  static Widget _buildImageItemWidget(RxAssetItemWrapper entry, String? assetId, AfCustomizeViewController controller) {
    return Obx(() => AfCustomizeImageItemView(entry: entry, isPreview: controller.previewFlg.value, assetId: assetId));
  }

  /// 创建计算项的工厂方法
  ///
  /// 用于创建计算类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回一个 [AfCustomizeTextItemView] 组件实例，并设置 isCalculate 为 true
  static Widget _buildCalculateItemWidget(
    RxAssetItemWrapper entry,
    String? assetId,
    AfCustomizeViewController controller,
  ) {
    return Obx(
      () => AfCustomizeTextItemView(
        entry: entry,
        isPreview: controller.previewFlg.value,
        isCalculate: true,
        assetId: assetId,
      ),
    );
  }

  /// 构建担当者选择项组件
  ///
  /// 用于创建担当者选择类型的自定义视图项
  ///
  /// 参数说明：
  /// - [entry]：资产项包装对象，包含项数据和状态
  /// - [assetId]：资产ID，可为空
  /// - [controller]：自定义视图控制器，用于获取预览状态
  ///
  /// 返回一个 [AfCustomizeUserSelectItemView] 组件实例
  static Widget _buildUserSelectItemWidget(
    RxAssetItemWrapper entry,
    String? assetId,
    AfCustomizeViewController controller,
  ) {
    return Obx(
      () => AfCustomizeUserSelectItemView(entry: entry, isPreview: controller.previewFlg.value, assetId: assetId),
    );
  }

  /// 构建分割线组件
  ///
  /// 返回一个高度为0.5，厚度为0.5的分割线组件
  /// 分割线左侧无缩进，右侧缩进5像素
  /// 使用AppTheme.grayColor作为分割线颜色
  static Widget _buildSplitLine() {
    return const Divider(height: 0.5, thickness: 0.5, indent: 0, endIndent: 5, color: AppTheme.grayColor);
  }

  /// 获取事件类型项目所属的事件类型ID
  ///
  /// 通过查找事件类型列表，确定当前项目属于哪个事件类型
  ///
  /// 参数:
  /// * [eventTypeItem] - 事件类型项目
  /// * [controller] - 自定义视图控制器
  ///
  /// 返回值:
  /// * 事件类型ID，如果找不到则返回null
  static int? _getEventTypeIdForItem(EventTypeItem eventTypeItem, AfCustomizeViewController controller) {
    final eventTypeList = controller.scheduleEventList?.eventTypeList;
    if (eventTypeList == null) return null;

    // 遍历所有事件类型，查找包含当前项目的事件类型
    for (final eventType in eventTypeList) {
      if (eventType?.itemList?.any((item) => item?.itemId == eventTypeItem.itemId) == true) {
        return eventType?.itemId;
      }
    }

    return null;
  }
}

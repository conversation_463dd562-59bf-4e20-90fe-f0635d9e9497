import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

mixin BaseAfCustomizeViewMixin {
  // ==================== 布局常量 ====================

  /// 默认标题占位比例
  /// 用于标准标题的 Flex 布局占位比例
  static const int _defaultTitleFlex = 4;

  /// 自定义标题占位比例
  /// 用于自定义标题的 Flex 布局占位比例
  static const int _customTitleFlex = 3;

  /// 默认内容占位比例
  /// 用于内容区域的 Flex 布局占位比例
  static const int _defaultContentFlex = 6;

  // ==================== 尺寸常量 ====================

  /// 默认字体大小
  /// 用于自定义标题的默认字体大小
  static const double _defaultFontSize = 14.0;

  /// 必填标志字体大小
  /// 用于必填标志文本的字体大小
  static const double _requiredMarkFontSize = 9.0;

  /// 默认图标大小
  /// 用于通用图标的默认尺寸
  static const double _defaultIconSize = 16.0;

  /// 错误提示图标大小
  /// 用于错误提示界面的图标尺寸
  static const double _errorIconSize = 40.0;

  /// 箭头图标大小
  /// 用于箭头按钮的图标尺寸
  static const double _arrowIconSize = 10.0;

  /// 自适应图标按钮图标大小
  /// 用于自适应图标按钮的图标尺寸
  static const double _adaptiveIconSize = 18.0;

  /// 计数按钮大小
  /// 用于数量调整按钮的尺寸
  static const double _countButtonSize = 50.0;

  /// 计数按钮字体大小
  /// 用于数量调整按钮文本的字体大小
  static const double _countButtonFontSize = 20.0;

  /// 默认圆角半径
  /// 用于按钮和容器的圆角设置
  static const double _defaultBorderRadius = 8.0;

  /// 必填标志圆角半径
  /// 用于必填标志容器的圆角设置
  static const double _requiredMarkBorderRadius = 20.0;

  /// 小圆角半径
  /// 用于小型组件的圆角设置
  static const double _smallBorderRadius = 4.0;

  // ==================== 边距常量 ====================

  /// 容器上下内边距
  /// 用于基础容器的上下内边距
  static const double _containerVerticalPadding = 10.0;

  /// 容器右边距
  /// 用于基础容器的右边距
  static const double _containerRightPadding = 5.0;

  /// 必填标志水平内边距
  /// 用于必填标志容器的左右内边距
  static const double _requiredMarkHorizontalPadding = 3.0;

  /// 必填标志垂直内边距
  /// 用于必填标志容器的上下内边距
  static const double _requiredMarkVerticalPadding = 1.0;

  /// 默认水平间距
  /// 用于组件之间的水平间距
  static const double _defaultHorizontalSpacing = 5.0;

  /// 错误信息底部边距
  /// 用于错误信息与其他内容之间的间距
  static const double _errorMessageBottomMargin = 10.0;

  // ==================== 行数常量 ====================

  /// 标题最大行数
  /// 用于标题文本的最大显示行数
  static const int _titleMaxLines = 2;

  // ==================== 分割线常量 ====================

  /// 分割线高度
  /// 用于分割线组件的高度设置
  static const double _dividerHeight = 0.5;

  /// 分割线厚度
  /// 用于分割线组件的厚度设置
  static const double _dividerThickness = 0.5;

  /// 粗分割线厚度
  /// 用于粗分割线组件的厚度设置
  static const double _thickDividerThickness = 1.0;

  /// 分割线起始缩进
  /// 用于分割线组件的起始缩进
  static const double _dividerIndent = 0.0;

  /// 分割线结束缩进
  /// 用于分割线组件的结束缩进
  static const double _dividerEndIndent = 5.0;

  // ==================== 字符串常量 ====================

  /// 必填标记文本
  /// 用于必填项的显示文本
  static const String _requiredMarkText = '必須';
  bool get isPreview;

  /// 基础容器样式
  EdgeInsetsGeometry get defaultPadding => const EdgeInsets.only(
    top: _containerVerticalPadding,
    bottom: _containerVerticalPadding,
    right: _containerRightPadding,
  );

  /// 构建必填项标志
  Widget buildIsRequired(bool isRequired) {
    return isRequired
        ? Container(
            padding: const EdgeInsets.symmetric(
              horizontal: _requiredMarkHorizontalPadding,
              vertical: _requiredMarkVerticalPadding,
            ),
            decoration: BoxDecoration(
              color: AppTheme.tipIconColor,
              borderRadius: BorderRadius.circular(_requiredMarkBorderRadius),
            ),
            child: const Text(
              _requiredMarkText,
              style: TextStyle(color: AppTheme.whiteColor, fontSize: _requiredMarkFontSize),
            ),
          )
        : const SizedBox.shrink();
  }

  /// 构建标题部分
  Widget buildTitle(String title, bool isRequired) {
    return Expanded(
      flex: _defaultTitleFlex,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Flexible(
            child: Text(
              title,
              style: const TextStyle(color: AppTheme.grayColor),
              maxLines: _titleMaxLines,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          buildIsRequired(isRequired),
        ],
      ),
    );
  }

  /// 构建标题部分
  Widget buildCustomTitle(String title, {bool isRequired = false, double fontSize = _defaultFontSize}) {
    return Expanded(
      flex: _customTitleFlex,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Flexible(
            child: Text(
              title,
              style: TextStyle(color: AppTheme.grayColor, fontSize: fontSize),
              maxLines: _titleMaxLines,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (isRequired) buildIsRequired(isRequired),
        ],
      ),
    );
  }

  /// 构建基础容器
  Widget buildBaseContainer({
    required List<Widget> children,
    EdgeInsetsGeometry? padding = const EdgeInsets.only(
      top: _containerVerticalPadding,
      bottom: _containerVerticalPadding,
      right: _containerRightPadding,
    ),
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.start,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
  }) {
    return Container(
      color: AppTheme.transparentColor,
      padding: padding ?? defaultPadding,
      child: Row(mainAxisAlignment: mainAxisAlignment, crossAxisAlignment: crossAxisAlignment, children: children),
    );
  }

  /// 构建错误提示
  Widget buildErrorWidget({required double size, Color? backgroundColor, double iconSize = _errorIconSize}) {
    return Container(
      color: backgroundColor ?? Colors.grey[200],
      child: Icon(Icons.error_outline, size: iconSize, color: AppTheme.grayColor),
    );
  }

  /// 构建Cupertino加载提示
  Widget buildCupertinoLoadingWidget({required double size, Color? backgroundColor}) {
    return Container(
      color: backgroundColor ?? Colors.grey[200],
      child: const Center(child: CupertinoActivityIndicator()),
    );
  }

  /// 构建加载提示
  Widget buildLoadingWidget({required double size, Color? backgroundColor}) {
    return Container(
      color: backgroundColor ?? Colors.grey[200],
      child: const Center(child: CircularProgressIndicator(color: AppTheme.grayColor)),
    );
  }

  /// 构建右侧内容容器
  Widget buildContentContainer({
    required List<Widget> children,
    int flex = _defaultContentFlex,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.start,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
  }) {
    return Expanded(
      flex: flex,
      child: Row(crossAxisAlignment: crossAxisAlignment, mainAxisAlignment: mainAxisAlignment, children: children),
    );
  }

  /// 构建水平间隔
  Widget buildHorizontalSpacing([double width = _defaultHorizontalSpacing]) => SizedBox(width: width);

  /// 构建可点击组件
  Widget buildTappableWidget({required Widget child, required VoidCallback? onTap, bool enabled = true}) {
    return GestureDetector(onTap: enabled ? onTap : null, child: child);
  }

  /// 构建垂直内容容器
  Widget buildVerticalContentContainer({
    required List<Widget> children,
    int flex = _defaultContentFlex,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.start,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
  }) {
    return Expanded(
      flex: flex,
      child: Column(crossAxisAlignment: crossAxisAlignment, mainAxisAlignment: mainAxisAlignment, children: children),
    );
  }

  /// 构建选项文本
  Widget buildOptionText(String text, {bool enabled = true, TextStyle? style}) {
    return Text(
      text,
      style: (style ?? const TextStyle()).copyWith(color: enabled ? AppTheme.black87Color : AppTheme.grayColor),
    );
  }

  /// 构建图标
  Widget buildIcon(IconData icon, {double size = _defaultIconSize, Color? color}) {
    return Icon(icon, size: size, color: color ?? AppTheme.black87Color);
  }

  /// 构建文本内容
  Widget buildText(
    String? text, {
    int? maxLines,
    bool softWrap = true,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextStyle? style,
  }) {
    return Text(
      text ?? '',
      maxLines: maxLines,
      softWrap: softWrap,
      overflow: overflow,
      style: style ?? const TextStyle(color: AppTheme.black87Color),
    );
  }

  /// 构建内容行
  Widget buildContentRow({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.spaceBetween,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
  }) {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  /// 构建分割线
  Widget buildDivider() {
    return const Divider(
      height: _dividerHeight,
      thickness: _dividerThickness,
      indent: _dividerIndent,
      endIndent: _dividerEndIndent,
      color: AppTheme.grayColor,
    );
  }

  /// 构建添加添附文件按钮
  Widget buildAddFileBtn({required Function()? onAddPressed, bool isEditAble = true}) {
    return buildTappableWidget(
      child: buildContentRow(children: [buildIcon(Icons.add), buildHorizontalSpacing(), buildText('添加文件')]),
      onTap: onAddPressed,
      enabled: isEditAble,
    );
  }

  /// 构建带箭头的按钮
  Widget buildArrowButton({
    required String text,
    required VoidCallback? onPressed,
    Color buttonColor = AppTheme.darkBlueColor,
    double arrowSize = _arrowIconSize,
    double radius = _defaultBorderRadius,
    bool trailingIcon = true,
    FontWeight? fontWeight,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(
        right: _defaultHorizontalSpacing,
        top: _defaultHorizontalSpacing,
        bottom: _defaultHorizontalSpacing,
      ),
      child: TextButton(
        onPressed: onPressed,
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(AppTheme.transparentColor),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(radius),
              side: BorderSide(color: buttonColor),
            ),
          ),
        ),
        child: SizedBox(
          width: double.infinity,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Text(
                text,
                style: TextStyle(color: buttonColor, fontWeight: fontWeight ?? FontWeight.bold),
              ),
              if (trailingIcon)
                Positioned(
                  right: 0,
                  child: Icon(Icons.arrow_forward_ios, size: arrowSize, color: buttonColor),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建带图标的按钮（支持左侧图标，宽度自适应）
  Widget buildAdaptiveIconButton({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    Color buttonColor = AppTheme.darkBlueColor,
    double iconSize = _adaptiveIconSize,
    double radius = _arrowIconSize,
    bool trailingIcon = false,
    double arrowSize = _arrowIconSize,
    FontWeight? fontWeight,
    EdgeInsets padding = const EdgeInsets.only(right: _defaultHorizontalSpacing),
  }) {
    return Padding(
      padding: padding,
      child: TextButton(
        onPressed: onPressed,
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(AppTheme.transparentColor),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(radius),
              side: BorderSide(color: buttonColor),
            ),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(icon, size: iconSize, color: buttonColor),
              buildHorizontalSpacing(_defaultHorizontalSpacing),
            ],
            Expanded(
              child: Text(
                text,
                maxLines: 2,
                style: TextStyle(color: buttonColor, fontWeight: fontWeight ?? FontWeight.bold),
              ),
            ),
            if (trailingIcon) ...[
              buildHorizontalSpacing(_defaultHorizontalSpacing),
              Icon(Icons.arrow_forward_ios, size: arrowSize, color: buttonColor),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建数量调整按钮
  Widget buildCountButton({
    String text = '',
    IconData icon = Icons.add,
    required VoidCallback? onPressed,
    double size = _countButtonSize,
    double radius = _defaultBorderRadius,
  }) {
    Widget content = Text(
      text,
      style: TextStyle(fontSize: _countButtonFontSize, color: onPressed == null ? Colors.grey : AppTheme.darkBlueColor),
    );
    if (text.isNotEmpty) {
      content = Text(
        text,
        style: TextStyle(
          fontSize: _countButtonFontSize,
          color: onPressed == null ? Colors.grey : AppTheme.darkBlueColor,
        ),
      );
    } else {
      content = Icon(icon, color: AppTheme.darkBlueColor);
    }
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(radius),
        border: Border.all(color: AppTheme.darkBlueColor),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(_smallBorderRadius),
          child: Center(child: content),
        ),
      ),
    );
  }

  Widget buildSplitLine() {
    return const Divider(
      height: _dividerHeight,
      thickness: _thickDividerThickness,
      indent: _dividerIndent,
      endIndent: _dividerEndIndent,
      color: AppTheme.grayColor,
    );
  }

  /// 构建尾部图标
  Widget buildTrailingIcon({
    IconData? middleIcon,
    IconData trailingIcon = Icons.arrow_forward_ios,
    bool isEditAble = true,
  }) {
    return buildContentRow(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (middleIcon != null) buildIcon(middleIcon),
        buildIcon(isEditAble ? trailingIcon : Icons.lock_outline),
      ],
    );
  }

  /// 构建错误信息
  /// 构建错误信息
  ///
  /// 当 [errorMsg] 不为空时，显示一个包含错误信息的容器
  /// 容器样式:
  /// - 文字颜色为红色 (AppTheme.afCustomizeErrorColor)
  /// - 字体大小为 _defaultFontSize
  /// - 底部边距为 _errorMessageBottomMargin
  /// - 宽度占满父容器
  ///
  /// 当 [errorMsg] 为空时，返回一个空的 SizedBox
  Widget buildErrorMsg(String errorMsg) {
    return errorMsg.isNotEmpty
        ? Container(
            child: Text(
              errorMsg,
              style: const TextStyle(color: AppTheme.afCustomizeErrorColor, fontSize: _defaultFontSize),
            ),
            margin: const EdgeInsets.only(bottom: _errorMessageBottomMargin),
            width: double.infinity,
          )
        : const SizedBox.shrink();
  }
}

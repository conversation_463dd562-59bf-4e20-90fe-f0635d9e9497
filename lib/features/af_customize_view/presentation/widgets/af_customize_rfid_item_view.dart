import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_rfid_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_tile.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view_entry.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// RFID 项视图
class AfCustomizeRfidItemView extends GetWidget<AfCustomizeRfidItemController>
    with BaseAfCustomizeViewMixin, BaseAfCustomizeViewEntryMixin {
  @override
  final RxAssetItemWrapper entry;
  @override
  final bool isPreview;
  @override
  final String? assetId;

  AfCustomizeRfidItemView({super.key, required this.entry, required this.isPreview, this.assetId});

  @override
  String get tag => assetId ?? '';

  @override
  Widget build(BuildContext context) {
    controller.initData(entry);
    return AfCustomizeTile(
      title: controller.title.value,
      isRequired: controller.isRequired.value,
      isEditAble: controller.isEditAble.value,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildText(controller),
          const SizedBox(height: 10),
          Obx(() {
            return buildAdaptiveIconButton(
              text: controller.scanButtonText,
              icon: Icons.nfc,
              radius: 5,
              onPressed: () {
                controller.handleRfidScanBtnClick();
              },
            );
          }),
          buildHorizontalSpacing(),
          Obx(() {
            return buildAdaptiveIconButton(
              text: controller.actionButtonText,
              icon: controller.actionIcon,
              buttonColor: controller.actionButtonColor,
              radius: 5,
              onPressed: () {
                controller.actionButtonHandler();
              },
            );
          }),
        ],
      ),
    );
  }

  /// RFID表示内容区域
  Widget _buildText(AfCustomizeRfidItemController controller) {
    if (controller.rfidValue.value != '') {
      return Obx(() {
        return buildTappableWidget(
          child: Row(
            children: [
              Expanded(child: buildText(controller.rfidValue.value, overflow: TextOverflow.visible)),
              buildIcon(Icons.keyboard_arrow_right),
            ],
          ),
          onTap: () {
            controller.handleManualAdd();
          },
        );
      });
    } else {
      return const SizedBox.shrink();
    }
  }
}

import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/factories/af_customize_view_item_widget_factory.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_label_item_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_relation_item_view.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_customize_logic_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/states/asset_relation_state.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/widgets/expandable_section.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum AfCustomizeViewScene {
  /// 资产详细
  assetDetail,

  /// 资产详细编辑
  assetDetailEdit,

  /// 处理设定
  processSetting,

  /// Schedule
  assetSchedule,

  /// 资产新建
  assetNewCreate,

  /// 履历情报
  assetHistory,

  /// 履历情报编辑
  assetHistoryEdit,

  myPage,
}

/// 自定义视图组件
///
/// 用于展示资产详情页面的自定义视图部分
///
/// [instance] - 资产实例ID,用于标识具体资产
/// [assetDict] - 资产数据字典,key为section名称,value为该section下的资产项列表
/// [liveUrl] - 即时通讯URL,用于实现实时通讯功能
/// [scene] - 视图场景,用于区分不同使用场景(详情/编辑/流程设置)
/// [relation] - 资产关联状态,管理资产间的关联关系
/// [assetTypeList] - 资产类型列表,用于选择关联资产类型
/// [needNavigationBar] - 是否需要底部导航栏，tabbar
/// [needSection] - 是否需要section
/// [isShowMasterDefaultValue] - 是否显示Master默认值
/// [isPreview] - 是否为预览模式
class AfCustomizeView extends GetWidget<AfCustomizeViewController> {
  /// 资产实例ID
  final String? instance;

  /// 资产数据字典
  /// 
  /// key为section名称,value为该section下的资产项列表
  final Map<String, List<dynamic>> assetDict;

  /// 即时通讯URL
  final String liveUrl;

  /// 视图场景
  final AfCustomizeViewScene scene;

  /// 资产关联状态
  final AssetRelationState? relation;

  /// 资产类型列表
  final List<AssetTypeListModel>? assetTypeList;

  /// 是否需要底部导航栏，tabbar
  ///
  /// 主要用于关联资产页面跳转时控制底部 bar
  final bool? needNavigationBar;

  /// 是否需要section
  final bool needSection;

  /// 是否进入时就需要校验
  final bool needEnterValidate;

  /// 日程事件列表
  final AppointmentListResponse? scheduleEventList;

  /// 预约列表
  final Appointment? appointment;

  /// 是否为预览模式
  /// 预览模式下所有条目不可编辑，保存按钮不可见
  final bool? isPreview;

  /// 是否显示Master默认值
  final bool isShowMasterDefaultValue;

  /// CustomizeLogic
  final GetCustomizeLogicResult? customizeLogicJs;

  /// 构造函数
  AfCustomizeView({
    super.key,
    this.instance,
    required this.assetDict,
    this.liveUrl = '',
    required this.scene,
    this.relation,
    this.assetTypeList,
    this.needNavigationBar,
    this.needSection = true,
    this.needEnterValidate = true,
    this.scheduleEventList,
    this.appointment,
    this.isPreview,
    this.isShowMasterDefaultValue = false,
    this.customizeLogicJs,
  });

  @override
  String? get tag => instance;

  @override
  Widget build(BuildContext context) {
    // 设置预览模式，如果 isPreview 为 true，则表示不可编辑和保存
    if (isPreview != null) {
      LogUtil.d('AfCustomizeView: setting previewFlg to $isPreview');
      controller.previewFlg.value = isPreview!;
      LogUtil.d('AfCustomizeView: previewFlg.value is now ${controller.previewFlg.value}');
    }

    LogUtil.d('AfCustomizeView - assetDict keys: ${assetDict.keys.toList()}');
    LogUtil.d('AfCustomizeView - assetDict total items: ${assetDict.values.fold(0, (sum, list) => sum + list.length)}');

    // 更新控制器中的资产数据
    controller.setAppointment(appointment);
    controller.setScene(scene);
    controller.updateMaster(isShowMasterDefaultValue);
    controller.setNeedEnterValidate(needEnterValidate);
    controller.setNeedNavigationBar(needNavigationBar);
    controller.updateAssetDict(assetDict);
    controller.updateScheduleEventList(scheduleEventList);
    controller.setCustomizeLogicJs(customizeLogicJs);
    controller.liveTalkUrl.value = liveUrl;

    // 直接构建内容，不使用 Obx，因为我们已经在外层有 Obx 了
    if (needSection == true) {
      LogUtil.d('AfCustomizeView - using section widgets');
      return Column(children: _buildSectionWidgets());
    } else {
      final items = controller.assetDict.values.expand((e) => e).toList();
      LogUtil.d('AfCustomizeView - using item widgets, items count: ${items.length}');
      final List<Widget> list = _buildItemWidgets(items);
      LogUtil.d('AfCustomizeView - built ${list.length} widgets');
      return Column(children: list);
    }
  }

  /// 构建所有 section 的 Widget 列表
  List<Widget> _buildSectionWidgets() {
    final widgets = <Widget>[];
    final keys = controller.assetDict.keys;

    // 构建所有 section
    for (int i = 0; i < keys.length; i++) {
      final key = keys.elementAt(i);
      final isFirstSection = (key == keys.first);

      // 添加可展开的 section
      widgets.add(_buildExpandableSection(key, isFirstSection, controller));

      // 在最后一个 section 后添加关联资产视图
      final isLastSection = (key == keys.last);
      if (isLastSection && _shouldAddRelationView()) {
        widgets.add(_buildRelationView(controller));
      }
    }

    return widgets;
  }

  /// 构建可展开的 section
  Widget _buildExpandableSection(String key, bool isFirstSection, AfCustomizeViewController controller) {
    return ExpandableSection(
      title: key,
      hasPaddingTop: isFirstSection,
      subTitle: key == 'トーク' ? '+ トークルーム作成' : null,
      instance: instance,
      child: _buildContainer(controller, key),
    );
  }

  /// 判断是否需要添加关联资产视图
  bool _shouldAddRelationView() {
    return relation != null && assetTypeList != null;
  }

  /// 构建关联资产视图
  Widget _buildRelationView(AfCustomizeViewController controller) {
    return AfCustomizeRelationItemView(
      entry: RxAssetItemWrapper.fromAssetItem(AssetItemListModel()),
      isPreview: false,
      assetId: instance,
      relation: relation!,
      assetTypeList: assetTypeList!,
      afController: controller,
    );
  }

  /// 构建内容Container
  ///
  /// [controller] - 自定义视图控制器
  /// [title] - section标题
  Widget _buildContainer(AfCustomizeViewController controller, String title) {
    final items = controller.assetDict[title] ?? [];
    final List<Widget> list = _buildItemWidgets(items);

    return Container(
      width: double.infinity,
      color: AppTheme.transparentColor,
      child: SingleChildScrollView(
        child: SizedBox(
          width: double.infinity,
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: list),
        ),
      ),
    );
  }

  /// 构建资产项目的Widget列表
  ///
  /// [entries] 资产项目包装器列表
  /// 返回构建好的Widget列表
  List<Widget> _buildItemWidgets(List<RxAssetItemWrapper>? entries) {
    if (entries == null || entries.isEmpty) {
      return [];
    }

    final widgets = entries
        .expand((entry) => ItemWidgetFactory.buildItemWidget(entry, assetId: instance))
        .whereType<Widget>() // 过滤掉可能的 null
        .toList();

    if (widgets.isEmpty) {
      return [];
    }

    return widgets.firstOrNull is AfCustomizeLabelItemView ? widgets : (widgets..removeAt(0));
  }
}

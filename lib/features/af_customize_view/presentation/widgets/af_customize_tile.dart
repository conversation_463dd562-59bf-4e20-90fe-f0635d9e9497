import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:flutter/material.dart';

/// 自定义条目视图
/// 所有AF 的条目都需要使用这个
///
/// ！！！checkbox、radio、画像 因为高度计算问题，不需要使用这个组件，请使用各自实现的组件
class AfCustomizeTile extends StatelessWidget {
  // ==================== 布局常量 ====================

  /// 默认标题占位比例
  /// 用于 Flex 布局中标题部分的占位比例
  static const int _defaultTitleFlex = 4;

  /// 默认内容占位比例
  /// 用于 Flex 布局中内容部分的占位比例
  static const int _defaultContentFlex = 6;

  // ==================== 尺寸常量 ====================

  /// 标题字体大小
  /// 用于条目标题的字体大小
  static const double _titleFontSize = 14.5;

  /// 错误信息字体大小
  /// 用于错误提示文本的字体大小
  static const double _errorMessageFontSize = 14.0;

  /// 中间图标大小
  /// 用于条目中间位置的图标尺寸
  static const double _middleIconSize = 16.0;

  /// 尾部图标大小
  /// 用于条目尾部位置的图标尺寸
  static const double _trailingIconSize = 18.0;

  /// 必填标志圆角半径
  /// 用于必填标志容器的圆角设置
  static const double _requiredMarkBorderRadius = 20.0;

  /// 必填标志字体大小
  /// 用于必填标志文本的字体大小
  static const double _requiredMarkFontSize = 9.0;

  // ==================== 边距常量 ====================

  /// 必填标志水平内边距
  /// 用于必填标志容器的左右内边距
  static const double _requiredMarkHorizontalPadding = 3.0;

  /// 必填标志垂直内边距
  /// 用于必填标志容器的上下内边距
  static const double _requiredMarkVerticalPadding = 1.0;

  /// 标题右边距
  /// 用于标题与内容之间的间距
  static const double _titleRightPadding = 5.0;

  /// 图标间距
  /// 用于图标与其他元素之间的间距
  static const double _iconSpacing = 4.0;

  /// 容器上下内边距
  /// 用于条目容器的上下内边距
  static const double _containerVerticalPadding = 10.0;

  /// 容器右边距
  /// 用于条目容器的右边距
  static const double _containerRightPadding = 5.0;

  /// 错误信息上边距
  /// 用于错误信息与主内容之间的间距
  static const double _errorMessageTopMargin = 5.0;

  // ==================== 行数常量 ====================

  /// 标题最大行数
  /// 用于标题文本的最大显示行数
  static const int _titleMaxLines = 2;

  // ==================== 字符串常量 ====================

  /// 默认必填标记文本
  /// 用于必填项的默认显示文本
  static const String _defaultRequiredMark = '必須';

  /// 标题
  final String title;

  /// 是否必填
  final bool isRequired;

  /// 内容Widget
  final Widget content;

  /// 内边距
  final EdgeInsetsGeometry? padding;

  /// 交叉轴对齐方式
  final CrossAxisAlignment crossAxisAlignment;

  /// 主轴对齐方式
  final MainAxisAlignment mainAxisAlignment;

  /// 是否可编辑
  final bool isEditAble;

  /// 尾图标
  final IconData? trailingIcon;

  /// 中间图标
  final IconData? middleIcon;

  /// 标题占位 flex
  final int titleFlex;

  /// 内容占位 flex
  final int contentFlex;

  /// 错误信息
  ///
  /// 当输入内容有误时显示的错误提示文本
  /// 为空字符串时不显示错误提示
  final String errorMsg;

  /// 中间图标点击事件回调
  final VoidCallback? onMiddleIconTap;

  /// 尾部图标点击事件回调
  final VoidCallback? onTrailingIconTap;

  /// 必填项标志
  ///
  /// 当需要在标题后面追加必填项标志时使用
  /// 使用此字段时时，原有的红色边框的必填 icon 将不显示
  final String requiredMark;

  const AfCustomizeTile({
    super.key,
    required this.title,
    required this.content,
    this.isRequired = false,
    this.requiredMark = _defaultRequiredMark,
    this.padding,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.isEditAble = true,
    this.trailingIcon,
    this.middleIcon,
    this.titleFlex = _defaultTitleFlex,
    this.contentFlex = _defaultContentFlex,
    this.errorMsg = '',
    this.onMiddleIconTap,
    this.onTrailingIconTap,
  });

  /// 构建必填项标志
  Widget _buildIsRequired() {
    return isRequired ? _buildRequiredMark() : const SizedBox.shrink();
  }

  _buildRequiredMark() {
    if (requiredMark != _defaultRequiredMark) {
      return const SizedBox.shrink();
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(
          horizontal: _requiredMarkHorizontalPadding,
          vertical: _requiredMarkVerticalPadding,
        ),
        decoration: BoxDecoration(
          color: AppTheme.tipIconColor,
          borderRadius: BorderRadius.circular(_requiredMarkBorderRadius),
        ),
        child: Text(
          requiredMark,
          style: const TextStyle(color: AppTheme.whiteColor, fontSize: _requiredMarkFontSize),
        ),
      );
    }
  }

  /// 构建标题
  Widget _buildTitle() {
    return Expanded(
      flex: titleFlex,
      child: Padding(
        padding: const EdgeInsets.only(right: _titleRightPadding),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                _getTitle(),
                style: const TextStyle(color: AppTheme.grayColor, fontSize: _titleFontSize),
                maxLines: _titleMaxLines,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            _buildIsRequired(),
          ],
        ),
      ),
    );
  }

  /// 获取标题
  String _getTitle() {
    return isRequired && requiredMark != _defaultRequiredMark ? '$title$requiredMark' : title;
  }

  /// 构建内容容器
  Widget _buildContentContainer(Widget child) {
    return Expanded(
      flex: contentFlex,
      child: Container(
        alignment: Alignment.centerLeft,
        child: Row(
          children: [
            Expanded(child: child),
            if (middleIcon != null) ...[
              const SizedBox(width: _iconSpacing),
              GestureDetector(
                onTap: onMiddleIconTap,
                child: Icon(middleIcon, size: _middleIconSize, color: AppTheme.grayColor),
              ),
            ],
            if (trailingIcon != null) ...[
              const SizedBox(width: _iconSpacing),
              GestureDetector(
                onTap: onTrailingIconTap,
                child: Icon(trailingIcon, size: _trailingIconSize, color: AppTheme.black87Color),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建条目
  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppTheme.transparentColor,
      padding:
          padding ??
          const EdgeInsets.only(
            top: _containerVerticalPadding,
            bottom: _containerVerticalPadding,
            right: _containerRightPadding,
          ),
      child: Column(
        children: [
          IntrinsicHeight(
            child: Row(
              mainAxisAlignment: mainAxisAlignment,
              crossAxisAlignment: crossAxisAlignment,
              children: [_buildTitle(), _buildContentContainer(content)],
            ),
          ),
          _buildErrorMsg(),
        ],
      ),
    );
  }

  /// 构建错误信息
  /// 构建错误信息
  ///
  /// 当 [errorMsg] 不为空时，显示一个包含错误信息的容器
  /// 容器样式:
  /// - 文字颜色为红色 (AppTheme.afCustomizeErrorColor)
  /// - 字体大小为 _errorMessageFontSize
  /// - 上边距为 _errorMessageTopMargin
  /// - 宽度占满父容器
  ///
  /// 当 [errorMsg] 为空时，返回一个空的 SizedBox
  Widget _buildErrorMsg() {
    return errorMsg.isNotEmpty
        ? Container(
            child: Text(
              errorMsg,
              style: const TextStyle(color: AppTheme.afCustomizeErrorColor, fontSize: _errorMessageFontSize),
            ),
            margin: const EdgeInsets.only(top: _errorMessageTopMargin),
            width: double.infinity,
          )
        : const SizedBox.shrink();
  }
}

import 'package:flutter/material.dart';

class AfCustomizeLabelItemView extends StatelessWidget {
  final String title;
  final String? color;
  final String? fontSize;
  final String? fontWeight;
  final String? lineHeight;

  const AfCustomizeLabelItemView({
    super.key,
    required this.title,
    this.color,
    this.fontSize,
    this.fontWeight,
    this.lineHeight,
  });

  @override
  Widget build(BuildContext context) {
    // 解析颜色
    final Color textColor = color != null ? Color(int.parse(color!.replaceAll('#', '0xFF'))) : Colors.black;

    // 解析字体大小
    final double textSize = fontSize != null ? double.parse(fontSize!.replaceAll('px', '')) : 14;

    // 解析字体粗细
    final FontWeight weight = fontWeight == 'bold' ? FontWeight.bold : FontWeight.normal;

    // 解析行高
    final double height = lineHeight != null ? double.parse(lineHeight!.replaceAll('%', '')) / 100 : 1.5;

    final TextStyle textStyle = TextStyle(color: textColor, fontSize: textSize, fontWeight: weight, height: height);
    return Container(
      alignment: Alignment.centerLeft,
      height: 50,
      child: Text(title, style: textStyle, overflow: TextOverflow.ellipsis),
    );
  }
}

import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_talk_item_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AfCustomizeTalkItemView extends GetWidget<AfCustomizeTalkItemController> {
  final String talkUrl;

  final String? assetId;

  const AfCustomizeTalkItemView({super.key, required this.talkUrl, this.assetId});

  @override
  String? get tag => assetId;

  @override
  Widget build(BuildContext context) {
    controller.updateTalkUrl(talkUrl);
    return Container(
      width: double.infinity,
      height: 325,
      margin: const EdgeInsets.only(top: 10, bottom: 10, right: 5, left: 0),
      decoration: BoxDecoration(border: Border.all(color: AppTheme.darkBlueColor)),
      child: Obx(() {
        // 使用Obx监听WebView初始化状态，避免重复创建视图
        if (!controller.isWebViewInitialized.value && talkUrl.isEmpty) {
          return const Center(child: Text('Loading...'));
        }
        return WebViewWidget(controller: controller.webViewController);
      }),
    );
  }
}

import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_master_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_tile.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view_entry.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Master项目视图组件
///
/// 用于显示和管理Master类型的自定义项目,包含以下功能:
/// - 显示Master项目标题和内容
/// - 支持点击交互
/// - 支持预览和编辑模式
/// - 支持必填项标记
/// - 支持删除操作
/// - 支持多个子项目的展示和管理
class AfCustomizeMasterItemView extends GetWidget<AfCustomizeMasterItemController>
    with BaseAfCustomizeViewMixin, BaseAfCustomizeViewEntryMixin {
  /// 水平内边距
  static const double _horizontalPadding = 10.0;

  /// 内容内边距
  static const double _contentPadding = 8.0;

  /// 标题字体大小
  static const double _titleFontSize = 14.0;

  /// 标题占比
  static const int _titleFlex = 31;

  /// 内容占比
  static const int _contentFlex = 56;

  /// 默认内边距
  static const EdgeInsets _defaultPadding = EdgeInsets.only(left: 0.0, right: 5.0, top: 12.0, bottom: 12.0);

  /// 资产项包装对象
  @override
  final RxAssetItemWrapper entry;

  /// 是否为预览模式
  @override
  final bool isPreview;

  /// 资产ID
  @override
  final String? assetId;

  /// 构造函数
  ///
  /// [entry] - 资产项包装对象
  /// [isPreview] - 是否为预览模式
  /// [assetId] - 资产ID
  AfCustomizeMasterItemView({super.key, required this.entry, required this.isPreview, this.assetId});

  @override
  String get tag => assetId ?? '';

  @override
  Widget build(BuildContext context) {
    controller.initData(entry);
    controller.updateMasterData();

    return buildTappableWidget(
      enabled: controller.isEditAble.value,
      onTap: null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [_buildHeaderTile(), _buildMasterItemsList()],
      ),
    );
  }

  /// 构建头部瓦片
  Widget _buildHeaderTile() {
    return _buildCustomizeTile(
      title: controller.title.value,
      trailingIcon: controller.deleteIcon,
      content: const SizedBox(),
      padding: _defaultPadding,
      onTrailingIconTap: controller.onDeleteItem,
    );
  }

  /// 构建Master项目列表
  Widget _buildMasterItemsList() {
    return Obx(
      () => Column(
        children: List.generate(
          controller.rxMasterDisplayItems.length,
          (index) => _MasterItemContent(
            item: controller.rxMasterDisplayItems[index],
            controller: controller,
            isLastItem: index == controller.rxMasterDisplayItems.length - 1,
            parentView: this,
            index: index,
          ),
        ),
      ),
    );
  }

  /// 构建自定义瓦片组件
  ///
  /// [title] - 标题文本
  /// [trailingIcon] - 尾部图标
  /// [content] - 内容组件
  /// [padding] - 内边距
  /// [titleFlex] - 标题弹性系数
  /// [contentFlex] - 内容弹性系数
  /// [crossAxisAlignment] - 交叉轴对齐方式
  /// [onTrailingIconTap] - 尾部图标点击事件
  Widget _buildCustomizeTile({
    required String title,
    required IconData trailingIcon,
    required Widget content,
    required EdgeInsets padding,
    int titleFlex = _titleFlex,
    int contentFlex = _contentFlex,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.start,
    VoidCallback? onTrailingIconTap,
  }) {
    return AfCustomizeTile(
      title: title,
      isRequired: controller.isRequired.value,
      isEditAble: controller.isEditAble.value,
      trailingIcon: trailingIcon,
      content: content,
      padding: padding,
      titleFlex: titleFlex,
      contentFlex: contentFlex,
      crossAxisAlignment: crossAxisAlignment,
      onTrailingIconTap: onTrailingIconTap,
    );
  }
}

/// Master项目内容组件
///
/// 用于显示单个Master项目的内容,包括标题、值和分隔线
///
/// 参数说明:
/// - [item]: Master显示项目模型,包含项目名称等信息
/// - [controller]: Master项目控制器,用于处理点击事件和获取显示值
/// - [isLastItem]: 是否为最后一个项目,决定是否显示分隔线
/// - [parentView]: 父视图,用于构建可点击组件
/// - [index]: 项目索引
class _MasterItemContent extends StatelessWidget {
  /// Master显示项目模型
  final MasterDisplayItemModel item;

  /// Master项目控制器
  final AfCustomizeMasterItemController controller;

  /// 是否为最后一个项目
  final bool isLastItem;

  /// 父视图
  final BaseAfCustomizeViewMixin parentView;

  /// 项目索引
  final int index;

  /// 构造函数
  const _MasterItemContent({
    required this.controller,
    required this.item,
    required this.parentView,
    required this.index,
    this.isLastItem = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AfCustomizeMasterItemView._horizontalPadding),
      child: Column(
        children: [
          parentView.buildTappableWidget(
            enabled: controller.isEditAble.value,
            onTap: () => controller.onMasterItemTap(item, index),
            child: AfCustomizeTile(
              title: item.itemDisplayName ?? item.itemName ?? '',
              isRequired: false, // MasterItem隐藏必填项标记
              isEditAble: controller.isEditAble.value,
              trailingIcon: controller.arrowIcon,
              content: Text(
                controller.showMasterValue(item),
                style: const TextStyle(fontSize: AfCustomizeMasterItemView._titleFontSize),
              ),
              padding: const EdgeInsets.symmetric(vertical: AfCustomizeMasterItemView._contentPadding, horizontal: 5),
              titleFlex: AfCustomizeMasterItemView._titleFlex,
              contentFlex: AfCustomizeMasterItemView._contentFlex,
              crossAxisAlignment: CrossAxisAlignment.center,
            ),
          ),
          if (!isLastItem) const Divider(height: 1),
        ],
      ),
    );
  }
}

import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/relation_asset_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_relation_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view_entry.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/states/asset_relation_state.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/widgets/expandable_section.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 关联资产视图组件
///
/// 用于显示和管理关联资产列表的视图组件。
///
/// [entry] - 资产项目包装器,包含资产相关数据
/// [isPreview] - 是否为预览模式,默认为false
/// [assetId] - 资产ID,可选参数
class AfCustomizeRelationItemView extends GetWidget<AfCustomizeRelationItemController>
    with BaseAfCustomizeViewMixin, BaseAfCustomizeViewEntryMixin {
  /// 项目间距常量
  static const _kItemSpacing = EdgeInsets.only(right: 8);

  /// 文本样式常量
  ///
  /// 使用14号字体大小和黑色(87%不透明度)
  static const _kTextStyle = TextStyle(fontSize: 14, color: AppTheme.black87Color);

  /// 关联资产状态
  final AssetRelationState relation;

  /// 关联资产列表，数据类型不一致，此处使用不到，但因为继承父类所以需要声明
  @override
  final RxAssetItemWrapper entry;

  /// 是否为预览模式
  @override
  final bool isPreview;

  /// 资产ID
  @override
  final String? assetId;

  /// 资产类型列表
  final List<AssetTypeListModel> assetTypeList;

  /// 自定义视图控制器
  final AfCustomizeViewController afController;

  const AfCustomizeRelationItemView({
    super.key,
    required this.entry,
    this.isPreview = false,
    this.assetId,
    required this.relation,
    required this.assetTypeList,
    required this.afController,
  });

  @override
  String? get tag => assetId;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.relatedAssets.isEmpty) {
        controller.initRelation(relation, assetTypeList);
      }
      return _buildContent(controller);
    });
  }

  /// 构建主要内容
  ///
  /// [controller] - 关联资产控制器
  Widget _buildContent(AfCustomizeRelationItemController controller) {
    return ExpandableSection(
      title: '関連資産',
      hasPaddingTop: false,
      instance: assetId,
      child: Column(
        children: [
          buildArrowButton(text: '新規登録', onPressed: controller.onRelationAddBtnClick),
          _buildAssetsList(controller),
        ],
      ),
    );
  }

  /// 构建资产列表
  ///
  /// [controller] - 关联资产控制器
  /// 如果没有关联资产则返回空组件
  Widget _buildAssetsList(AfCustomizeRelationItemController controller) {
    return Obx(() {
      if (controller.relatedAssets.isEmpty) return const SizedBox.shrink();

      return Column(
        children: [
          _CustomDivider(),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.relatedAssets.length,
            separatorBuilder: (_, __) => _CustomDivider(),
            itemBuilder: (_, index) => _RelatedAssetItem(
              asset: controller.relatedAssets[index],
              onTap: () => controller.onRelationAssetListItemTap(controller.relatedAssets[index]),
            ),
          ),
        ],
      );
    });
  }
}

class _CustomDivider extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: AfCustomizeRelationItemView._kItemSpacing,
      child: const Divider(endIndent: 0, height: 0.5, thickness: 0.5, color: AppTheme.black87Color),
    );
  }
}

/// 关联资产项组件
///
/// 用于显示单个关联资产的列表项
///
/// [asset] - 关联资产对象
/// [onTap] - 点击事件回调
class _RelatedAssetItem extends StatelessWidget {
  final RelationAssetModel asset;
  final VoidCallback onTap;

  const _RelatedAssetItem({required this.asset, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 4,
              child: Text(
                asset.assetTypeName,
                style: AfCustomizeRelationItemView._kTextStyle,
                softWrap: true,
                overflow: TextOverflow.visible,
              ),
            ),
            Expanded(flex: 6, child: Text('${asset.count}件', style: AfCustomizeRelationItemView._kTextStyle)),
            const Icon(Icons.keyboard_arrow_right, size: 20, color: AppTheme.black87Color),
          ],
        ),
      ),
    );
  }
}

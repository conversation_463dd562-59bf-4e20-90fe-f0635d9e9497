import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_radio_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view_entry.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AfCustomizeRadioItemView extends GetWidget<AfCustomizeRadioItemController>
    with BaseAfCustomizeViewMixin, BaseAfCustomizeViewEntryMixin {
  @override
  final RxAssetItemWrapper entry;
  @override
  final bool isPreview;
  @override
  final String? assetId;

  AfCustomizeRadioItemView({super.key, required this.entry, required this.isPreview, this.assetId});

  @override
  String get tag => assetId ?? '';

  @override
  Widget build(BuildContext context) {
    controller.initData(entry);

    return Obx(() {
      return Column(
        children: [
          buildBaseContainer(
            padding: entry.isShowMessage.value ? const EdgeInsets.only(top: 10, bottom: 0, right: 5) : null,
            children: [
              buildTitle(controller.title.value, controller.isRequired.value),
              Expanded(
                flex: 6,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: controller.optionItems.map((option) {
                    return GestureDetector(
                      onTap: controller.isEditAble.value
                          ? () {
                              final value = option;
                              controller.updateRadioValue(controller.itemId, value);
                            }
                          : null,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Radio<String>(
                              value: option,
                              groupValue: controller.radioValues[controller.itemId],
                              toggleable: true,
                              onChanged: controller.isEditAble.value
                                  ? (String? newValue) {
                                      final value = newValue ?? '';
                                      controller.updateRadioValue(controller.itemId, value);
                                    }
                                  : null,
                              activeColor: AppTheme.darkBlueColor,
                              visualDensity: const VisualDensity(
                                horizontal: VisualDensity.minimumDensity,
                                vertical: VisualDensity.minimumDensity,
                              ),
                              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            const SizedBox(width: 4),
                            Expanded(child: buildOptionText(option, enabled: controller.isEditAble.value)),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
          buildErrorMsg(entry.showMessage.value),
        ],
      );
    });
  }
}

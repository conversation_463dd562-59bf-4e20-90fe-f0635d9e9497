import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_asset_count_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_tile.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view_entry.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 资产数量项目视图
/// 用于显示和编辑资产数量的自定义视图组件
/// - [entry] : 资产项目数据模型
/// - [parentController] : 父级控制器
class AfCustomizeAssetCountItemView extends GetWidget<AfCustomizeAssetCountItemController>
    with BaseAfCustomizeViewMixin, BaseAfCustomizeViewEntryMixin {
  @override
  final RxAssetItemWrapper entry;
  @override
  final bool isPreview;
  @override
  final String? assetId;

  @override
  String get tag => assetId ?? '';

  AfCustomizeAssetCountItemView({super.key, required this.entry, required this.isPreview, this.assetId});

  @override
  Widget build(BuildContext context) {
    controller.initData(entry);

    return Column(
      children: [
        Obx(
          () => AfCustomizeTile(
            title: controller.title.value,
            isRequired: controller.isRequired.value,
            isEditAble: controller.isEditAble.value,
            trailingIcon: Icons.keyboard_arrow_right,
            content: const SizedBox.shrink(),
          ),
        ),
        _buildContent(controller),
      ],
    );
  }

  Container _buildContent(AfCustomizeAssetCountItemController controller) {
    return Container(
      height: 50,
      margin: const EdgeInsets.only(bottom: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          buildCountButton(icon: Icons.remove, onPressed: controller.isEditAble.value ? controller.decrement : null),
          Expanded(
            child: Container(
              height: 50,
              margin: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: AppTheme.whiteColor,
                border: Border.all(color: AppTheme.inputBorderColor, width: 1),
              ),
              child: Center(
                child: TextField(
                  controller: controller.textController,
                  enabled: controller.isEditAble.value,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 20, color: AppTheme.black87Color),
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.zero,
                    border: InputBorder.none,
                    isDense: true,
                  ),
                  onChanged: controller.updateCount,
                ),
              ),
            ),
          ),
          buildCountButton(icon: Icons.add, onPressed: controller.isEditAble.value ? controller.increment : null),
        ],
      ),
    );
  }
}

import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_text_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_tile.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view_entry.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// AfCustomizeTextItemView 是一个自定义文本项目视图组件
///
/// 主要功能:
/// - 显示和编辑文本内容
/// - 支持单行和多行文本输入
/// - 支持货币类型显示
/// - 支持超链接样式
/// - 支持必填校验
///
/// 属性说明:
/// - [isCalculate] - 是否为计算类型项目
/// - [entry] - 响应式资产项目包装器对象
/// - [isPreview] - 是否为预览模式
/// - [assetId] - 资产ID
///
/// 使用示例:
/// ```dart
/// AfCustomizeTextItemView(
///   entry: rxAssetItemWrapper,
///   isPreview: false,
///   assetId: '123',
/// )
/// ```
class AfCustomizeTextItemView extends GetWidget<AfCustomizeTextItemController>
    with BaseAfCustomizeViewMixin, BaseAfCustomizeViewEntryMixin {
  // ==================== 文本样式常量 ====================

  /// 基础字体大小
  /// 用于文本显示和输入框的统一字体大小
  static const double _baseFontSize = 14.0;

  /// 行高倍数
  /// 用于确保展示模式和编辑模式的行高一致性
  static const double _lineHeightMultiplier = 1.2;

  // ==================== 尺寸常量 ====================

  /// 保存按钮图标大小
  /// 用于编辑模式下的确认保存图标
  static const double _saveIconSize = 16.0;

  /// 保存按钮容器宽度
  /// 用于编辑模式下保存按钮的容器宽度和最小尺寸约束
  static const double _saveButtonWidth = 28.0;

  // ==================== 行数常量 ====================

  /// 单行文本行数
  /// 用于单行文本显示和输入的行数限制
  static const int _singleLineCount = 1;

  /// 多行文本最大行数
  /// 用于多行文本显示和输入的最大行数限制
  static const int _multiLineMaxCount = 10;

  // ==================== 边距常量 ====================

  /// 多行文本时的顶部对齐边距
  /// 用于多行文本编辑时保存按钮与文本顶部对齐的微调
  static const double _multiLineTopPadding = 2.0;

  final bool isCalculate;
  @override
  final RxAssetItemWrapper entry;
  @override
  final bool isPreview;
  @override
  final String? assetId;

  AfCustomizeTextItemView({
    super.key,
    required this.entry,
    this.isCalculate = false,
    required this.isPreview,
    this.assetId,
  });

  @override
  String get tag => assetId ?? '';

  /// 计算基于应用级字体缩放的动态文本高度
  ///
  /// 此方法确保文本容器能够适应应用级字体缩放设置，避免文本被裁切。
  /// 由于应用级字体缩放（FontScaleService）已经通过 MediaQuery 全局处理了字体大小，
  /// 这里只需要获取实际的缩放后尺寸来计算容器高度。
  ///
  /// 参数说明：
  /// - [context] 构建上下文，用于获取应用级设置的字体缩放因子
  ///
  /// 返回值：
  /// - 根据应用级字体缩放因子调整后的文本高度
  ///
  /// 计算逻辑：
  /// 1. 获取应用级设置的字体缩放因子（通过 MediaQuery.textScalerOf 获取）
  /// 2. 计算缩放后的实际字体大小
  /// 3. 根据行高倍数计算容器所需高度
  double _calculateDynamicTextHeight(BuildContext context) {
    final textScaler = MediaQuery.textScalerOf(context);
    final scaledFontSize = textScaler.scale(_baseFontSize);
    final dynamicHeight = (scaledFontSize * _lineHeightMultiplier);

    // 返回计算后的动态高度，应用级缩放已确保合理的缩放范围
    return dynamicHeight;
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.d(
      'entry-itemType: ${entry.itemType},entry-defaultData: ${entry.defaultData},entry-itemName: ${entry.itemName}',
    );
    controller.initData(entry);
    controller.isCalculate.value = isCalculate;

    // 定义统一的文本样式，确保展示模式和编辑模式完全一致
    // 字体大小使用基础值，应用级字体缩放（FontScaleService）会通过 MediaQuery 自动处理缩放
    const baseTextStyle = TextStyle(
      color: AppTheme.black87Color,
      fontSize: _baseFontSize, // 基础字体大小，应用级缩放会自动处理
      height: _lineHeightMultiplier, // 明确指定行高倍数，确保一致性
    );

    // 超链接时设置颜色，继承基础样式
    final style = SharedItemTypeEnum.hyperlink.equals(entry.itemType)
        ? baseTextStyle.copyWith(color: AppTheme.hyperLinkColor, decoration: TextDecoration.underline)
        : baseTextStyle;

    return Obx(() {
      return AfCustomizeTile(
        title: controller.title.value,
        isRequired: controller.isRequired.value,
        isEditAble: controller.isEditAble.value,
        trailingIcon: controller.trailingIcon,
        middleIcon: controller.middleIcon,
        errorMsg: entry.showMessage.value,
        content: Obx(
          () => controller.isEditing.value ? _buildTextField(context, style) : _buildDisplayContent(context, style),
        ),
      );
    });
  }

  /// 构建展示内容组件
  ///
  /// 高度一致性设计说明：
  /// - 单行文本：使用动态高度约束，根据应用级字体缩放调整，确保与编辑模式高度完全一致
  /// - 多行文本：使用最小高度约束，允许内容自适应高度
  /// - 通过 isMultiLine 条件判断采用不同的高度控制策略
  /// - 保持字体样式和行间距在两种模式下完全一致
  /// - 支持应用级字体缩放（FontScaleService），确保文本不被裁切
  ///
  /// 点击事件响应优化：
  /// - 使用 HitTestBehavior.opaque 确保透明区域也能响应点击事件
  /// - 解决文本内容较短时空白区域无法点击的问题
  ///
  /// 参数说明：
  /// - [context] 构建上下文，用于处理点击事件和获取应用级字体缩放因子
  /// - [style] 统一的文本样式，确保与编辑模式一致
  ///
  /// 返回值：
  /// - 包含适当高度约束的展示内容组件
  Widget _buildDisplayContent(BuildContext context, TextStyle style) {
    // 计算基于系统字体缩放因子的动态文本高度
    final dynamicTextHeight = _calculateDynamicTextHeight(context);

    // 使用 GestureDetector 包装 Container 以处理点击事件
    // 点击事件用于触发编辑模式或执行其他交互操作
    // behavior: HitTestBehavior.opaque 确保透明区域也能响应点击事件
    return GestureDetector(
      behavior: HitTestBehavior.opaque, // 确保透明区域也能响应点击事件
      onTap: () => controller.handleTap(context),
      child: Container(
        // 根据是否多行采用不同的高度控制策略
        constraints: controller.isMultiLine
            ? BoxConstraints(
                minHeight: dynamicTextHeight, // 多行：只设置最小高度，允许内容扩展
              )
            : BoxConstraints(
                minHeight: dynamicTextHeight,
                maxHeight: dynamicTextHeight, // 单行：使用动态高度，支持字体缩放
              ),
        // 单行时明确设置高度，多行时让内容自适应
        height: controller.isMultiLine ? null : dynamicTextHeight,
        // 确保内容对齐方式一致
        alignment: controller.isMultiLine ? Alignment.topLeft : Alignment.centerLeft,
        child: SharedItemTypeEnum.currency.equals(entry.itemType)
            ? _buildCurrencyDisplay(style)
            : _buildNormalDisplay(style),
      ),
    );
  }

  /// 构建货币类型显示内容
  ///
  /// 货币类型需要显示货币符号和数值两部分
  /// 根据是否多行采用不同的溢出处理策略
  ///
  /// 参数说明：
  /// - [style] 文本样式，已包含统一的字体配置
  ///
  /// 返回值：
  /// - 包含货币符号和数值的 Row 组件
  Widget _buildCurrencyDisplay(TextStyle style) {
    return Row(
      // 多行时使用顶部对齐，单行时使用居中对齐
      crossAxisAlignment: controller.isMultiLine ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      children: [
        // 货币符号部分 - 固定单行显示
        buildText(
          controller.entry.currencyType,
          style: style, // 使用统一的样式
          maxLines: _singleLineCount, // 货币符号固定单行显示
          overflow: TextOverflow.ellipsis,
        ),
        // 货币数值部分
        Expanded(
          child: buildText(
            controller.entry.valueForShow.value,
            maxLines: controller.isMultiLine ? _multiLineMaxCount : _singleLineCount, // 根据是否多行设置最大行数
            // 多行时允许换行显示，单行时使用省略号
            overflow: controller.isMultiLine ? TextOverflow.visible : TextOverflow.ellipsis,
            style: style, // 使用统一的样式
          ),
        ),
      ],
    );
  }

  /// 构建普通文本显示内容
  ///
  /// 用于非货币类型的文本显示
  /// 根据 isMultiLine 属性决定是否支持多行显示和溢出处理策略
  ///
  /// 参数说明：
  /// - [style] 文本样式，已包含统一的字体配置
  ///
  /// 返回值：
  /// - 文本显示组件
  Widget _buildNormalDisplay(TextStyle style) {
    return buildText(
      controller.entry.valueForShow.value,
      maxLines: controller.isMultiLine ? _multiLineMaxCount : _singleLineCount, // 根据是否多行设置最大行数
      // 多行时允许换行显示，单行时使用省略号
      overflow: controller.isMultiLine ? TextOverflow.visible : TextOverflow.ellipsis,
      style: style, // 使用统一的样式
    );
  }

  /// 构建文本输入框组件
  ///
  /// 高度一致性设计说明：
  /// - 单行文本：使用动态高度约束，根据应用级字体缩放调整，确保与展示模式高度完全一致
  /// - 多行文本：使用最小高度约束，允许 TextField 根据内容自适应高度
  /// - TextField 样式与展示文本样式完全匹配
  /// - 通过条件判断的高度控制策略确保两种模式下的一致性
  /// - IconButton 根据文本类型调整位置和对齐方式
  /// - 支持应用级字体缩放（FontScaleService），确保输入框高度适应缩放后的字体
  ///
  /// 组件结构:
  /// - 外层 Container: 根据是否多行设置不同的高度约束
  /// - Row 布局: 包含文本输入框和保存按钮
  /// - TextField:
  ///   - 使用与展示模式完全相同的文本样式
  ///   - 精确控制内边距和装饰
  ///   - 根据 controller.isMultiLine 设置多行输入参数
  /// - IconButton:
  ///   - 单行时垂直居中，多行时顶部对齐
  ///
  /// 参数说明：
  /// - [context] 构建上下文，用于获取应用级字体缩放因子
  /// - [style] 与展示模式完全一致的文本样式
  Widget _buildTextField(BuildContext context, TextStyle style) {
    // 计算基于系统字体缩放因子的动态文本高度
    final dynamicTextHeight = _calculateDynamicTextHeight(context);

    return Container(
      // 根据是否多行采用不同的高度控制策略
      constraints: controller.isMultiLine
          ? BoxConstraints(
              minHeight: dynamicTextHeight, // 多行：只设置最小高度，允许内容扩展
            )
          : BoxConstraints(
              minHeight: dynamicTextHeight,
              maxHeight: dynamicTextHeight, // 单行：使用动态高度，支持字体缩放
            ),
      child: IntrinsicHeight(
        // 使用 IntrinsicHeight 确保多行时 Row 中的所有子组件高度一致
        child: Row(
          // 多行时使用顶部对齐，单行时使用居中对齐
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Container(
                // 单行时设置动态高度，多行时让内容自适应
                height: controller.isMultiLine ? null : dynamicTextHeight,
                alignment: controller.isMultiLine ? Alignment.topLeft : Alignment.centerLeft,
                child: TextField(
                  controller: controller.textEditingController,
                  focusNode: controller.focusNode,
                  // 使用与展示模式完全相同的文本样式
                  style: style,
                  maxLines: controller.isMultiLine ? _multiLineMaxCount : _singleLineCount, // 多行时最多10行
                  minLines: _singleLineCount, // 最少一行
                  // 设置文本对齐方式与展示模式一致
                  textAlignVertical: controller.isMultiLine ? TextAlignVertical.top : TextAlignVertical.center,
                  decoration: const InputDecoration(
                    // 完全移除所有内边距和装饰
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                    border: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    // 确保没有额外的装饰影响高度
                    isCollapsed: true,
                    // 移除所有默认的约束
                    constraints: BoxConstraints(),
                  ),
                  onSubmitted: (value) {
                    controller.endEditing();
                  },
                ),
              ),
            ),
            // 保存按钮 - 根据文本类型调整位置
            Container(
              width: _saveButtonWidth,
              // 单行时使用动态高度，多行时使用最小高度
              height: controller.isMultiLine ? null : dynamicTextHeight,
              constraints: controller.isMultiLine ? BoxConstraints(minHeight: dynamicTextHeight) : null,
              alignment: Alignment.center,
              // 多行时添加顶部边距，与文本顶部对齐
              padding: controller.isMultiLine ? const EdgeInsets.only(top: _multiLineTopPadding) : EdgeInsets.zero,
              child: IconButton(
                // 完全移除内边距
                padding: EdgeInsets.zero,
                // 移除最小尺寸约束
                constraints: const BoxConstraints(minWidth: _saveButtonWidth, minHeight: _saveButtonWidth),
                onPressed: controller.endEditing,
                icon: const Icon(
                  Icons.check,
                  color: AppTheme.darkBlueColor,
                  size: _saveIconSize, // 使用更小的图标以适应紧凑布局
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

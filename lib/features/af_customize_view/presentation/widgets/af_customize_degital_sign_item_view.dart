import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_digital_sign_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_tile.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view_entry.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 数字签名项目视图
/// 用于显示数字签名的自定义视图组件
/// - [entry] : 资产项目数据模型
/// - [parentController] : 父级控制器
/// - [_contentFlex] : 内容区域的弹性布局比例
class AfCustomizeDigitalSignItemView extends GetWidget<AfCustomizeDigitalSignItemController>
    with BaseAfCustomizeViewMixin, BaseAfCustomizeViewEntryMixin {
  // ==================== 文本样式常量 ====================

  /// 基础字体大小
  /// 用于文本显示和输入框的统一字体大小
  static const double _baseFontSize = 14.0;

  /// 行高倍数
  /// 用于确保展示模式和编辑模式的行高一致性
  static const double _lineHeightMultiplier = 1.2;

  @override
  final RxAssetItemWrapper entry;
  @override
  final bool isPreview;
  @override
  final String? assetId;

  @override
  String get tag => assetId ?? '';

  AfCustomizeDigitalSignItemView({super.key, required this.entry, required this.isPreview, this.assetId});

  @override
  Widget build(BuildContext context) {
    controller.initData(entry);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Obx(() {
          return AfCustomizeTile(
            title: controller.title.value,
            isRequired: controller.isRequired.value,
            isEditAble: controller.isEditAble.value,
            content: _buildText(),
          );
        }),
        _buildImageContainer(),
      ],
    );
  }

  /// DegitalSign图片容器
  Widget _buildImageContainer() {
    if (entry.defaultData == null || entry.defaultData == '') {
      return const SizedBox.shrink();
    }

    return Container(child: _buildImage());
  }

  /// DegitalSign图片异步区域
  FutureBuilder<String> _buildImage() {
    return FutureBuilder<String>(
      future: controller.getImageUrl(entry.defaultData == null || entry.defaultData == '' ? [] : entry.defaultData),
      builder: (context, snapshot) {
        return controller.buildImageWidget(snapshot);
      },
    );
  }

  /// DegitalSign图片
  Widget _buildText() {
    // 定义统一的文本样式，确保展示模式和编辑模式完全一致
    // 字体大小使用基础值，应用级字体缩放（FontScaleService）会通过 MediaQuery 自动处理缩放
    const baseTextStyle = TextStyle(
      color: AppTheme.black87Color,
      fontSize: _baseFontSize, // 基础字体大小，应用级缩放会自动处理
      height: _lineHeightMultiplier, // 明确指定行高倍数，确保一致性
    );

    return Obx(() {
      return Row(
        children: [
          Expanded(
            child: buildText(controller.info.value, overflow: TextOverflow.visible, style: baseTextStyle),
          ),
          buildIcon(Icons.keyboard_arrow_right),
        ],
      );
    });
  }
}

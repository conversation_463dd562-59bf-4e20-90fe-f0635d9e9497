import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_user_select_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_tile.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view_entry.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 担当者选择条目视图
class AfCustomizeUserSelectItemView extends GetWidget<AfCustomizeUserSelectItemController>
    with BaseAfCustomizeViewMixin, BaseAfCustomizeViewEntryMixin {
  @override
  final RxAssetItemWrapper entry;
  @override
  final bool isPreview;
  @override
  final String? assetId;

  AfCustomizeUserSelectItemView({super.key, required this.entry, required this.isPreview, this.assetId});

  @override
  String get tag => assetId ?? '';

  @override
  Widget build(BuildContext context) {
    controller.initData(entry);

    return AfCustomizeTile(
      title: controller.title.value,
      trailingIcon: Icons.keyboard_arrow_right,
      content: Obx(
        () => buildTappableWidget(
          child: buildText(controller.displayValue, overflow: TextOverflow.ellipsis),
          onTap: controller.isEditAble.value ? controller.onUserSelect : null,
        ),
      ),
    );
  }
}

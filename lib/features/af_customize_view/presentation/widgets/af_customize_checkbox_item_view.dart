import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_checkbox_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_tile.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view_entry.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 复选框項目
class AfCustomizeCheckboxItemView extends GetWidget<AfCustomizeCheckboxItemController>
    with BaseAfCustomizeViewMixin, BaseAfCustomizeViewEntryMixin {
  @override
  final RxAssetItemWrapper entry;
  @override
  final bool isPreview;
  @override
  final String? assetId;

  @override
  String get tag => assetId ?? '';

  AfCustomizeCheckboxItemView({super.key, required this.entry, required this.isPreview, this.assetId});

  @override
  Widget build(BuildContext context) {
    controller.initData(entry);
    controller.setCheckboxValues();

    return Obx(() {
      return AfCustomizeTile(
        title: controller.title.value,
        isRequired: controller.isRequired.value,
        isEditAble: controller.isEditAble.value,
        content: _buildContent(controller),
      );
    });
  }

  Column _buildContent(AfCustomizeCheckboxItemController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: controller.getMainAxisAlignment(),
      children: controller.options.asMap().entries.map((entry) {
        final option = entry.value;
        final isLastItem = entry.key == controller.options.length - 1;

        return Padding(
          padding: EdgeInsets.only(bottom: isLastItem || controller.options.length == 1 ? 0 : 8.0),
          child: Row(
            children: [
              SizedBox(
                height: 24,
                width: 24,
                child: Obx(
                  () => Checkbox(
                    value: controller.checkboxValues.contains(option),
                    onChanged: controller.isEditAble.value
                        ? (value) => controller.onCheckChanged(option, value ?? false)
                        : null,
                    activeColor: AppTheme.darkBlueColor,
                    checkColor: AppTheme.whiteColor,
                    side: const BorderSide(width: 1, color: AppTheme.grayColor),
                  ),
                ),
              ),
              const SizedBox(width: 4),
              Obx(() {
                return _buildScheduleAlertSettingText(entry);
              }),
              Expanded(
                child: buildOptionText(controller.isMultiSelect ? option : '', enabled: controller.isEditAble.value),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// アラート設定画面へ
  Widget _buildScheduleAlertSettingText(MapEntry<int, String> entry) {
    if (controller.isScheduleAlertSetting.value && controller.checkboxValues.contains(entry.value)) {
      return GestureDetector(
        onTap: controller.handleAlertSettings,
        child: Text('アラート設定画面へ', style: TextStyle(fontSize: 14, color: Colors.red[600])),
      );
    } else {
      return const SizedBox.shrink();
    }
  }
}

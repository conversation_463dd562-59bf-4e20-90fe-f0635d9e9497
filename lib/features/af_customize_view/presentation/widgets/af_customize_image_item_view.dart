import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_image_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_image_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view_entry.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AfCustomizeImageItemView extends GetWidget<AfCustomizeImageItemController>
    with BaseAfCustomizeViewMixin, BaseAfCustomizeViewEntryMixin {
  /// 图片容器的圆角半径
  static const double containerBorderRadius = 3.0;

  /// 图片内容的圆角半径
  static const double contentBorderRadius = 2.0;

  /// 默认边框宽度
  static const double defaultBorderWidth = 1.0;

  /// 添加按钮图标大小
  static const double addButtonIconSize = 20.0;

  /// Grid布局的flex值
  static const int gridFlexValue = 6;

  /// 图片之间的间距
  final double spacing;

  /// 每行显示的图片数量
  final int crossAxisCount;

  /// 添加按钮点击事件回调
  final VoidCallback? onAddPressed;

  @override
  final RxAssetItemWrapper entry;
  @override
  final bool isPreview;
  @override
  final String? assetId;

  AfCustomizeImageItemView({
    super.key,
    required this.entry,
    this.spacing = AfCustomizeImageItemController.defaultSpacing,
    this.crossAxisCount = AfCustomizeImageItemController.defaultCrossAxisCount,
    this.onAddPressed,
    required this.isPreview,
    this.assetId,
  });

  @override
  String get tag => assetId ?? '';

  @override
  Widget build(BuildContext context) {
    LogUtil.d('🖼️ 图片组件 build 被调用 - itemType: ${entry.itemType}, itemName: ${entry.itemName}');
    controller.initData(entry);
    controller.setLayoutParams(spacing: spacing, crossAxisCount: crossAxisCount);
    controller.setImageUrls(controller.initImageUrls());
    final itemWidth = controller.calculateItemWidth(context);
    return buildBaseContainer(
      children: [
        buildTitle(controller.title.value, controller.isRequired.value),
        buildHorizontalSpacing(),
        Expanded(
          flex: gridFlexValue,
          child: Obx(() {
            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                mainAxisSpacing: spacing,
                crossAxisSpacing: spacing,
                childAspectRatio: 1,
              ),
              itemCount: controller.imageUrls.length + 1,
              itemBuilder: (context, index) {
                if (index == controller.imageUrls.length) {
                  return _buildAddButton(itemWidth, controller);
                }
                return Obx(() => _buildImageItem(controller.imageUrls[index], itemWidth, controller, index));
              },
            );
          }),
        ),
      ],
    );
  }

  /// 构建图片Item
  ///
  /// 优化的图片项构建方法，避免重复加载和缓存失效问题
  ///
  /// 主要改进：
  /// - 使用稳定的缓存键避免重复加载
  /// - 优化 FutureBuilder 的使用，减少不必要的网络请求
  /// - 改进响应式状态管理，确保UI及时更新但不过度重建
  Widget _buildImageItem(RxImageModel imageObj, double size, AfCustomizeImageItemController controller, int index) {
    LogUtil.d('🖼️ _buildImageItem 被调用 - index: $index, imageUrl: ${imageObj.url.value}');

    // 使用原始URL作为稳定的标识符，避免因turl变化导致的重建
    final String stableKey = imageObj.url.value;

    return InkWell(
      onTap: () {
        controller.onImageItemPressed(entry, index);
      },
      child: Obx(() {
        // 监听必要的响应式变量
        entry.valueForShow.value;
        imageObj.isRetrying.value;

        return _buildImageContainer(
          color: controller.getImageBorderColor(imageObj),
          borderWidth: controller.getImageBorderWidth(imageObj),
          radius: controller.getImageBorderRadius(imageObj),
          size: size,
          child: _buildOptimizedImageContent(imageObj, size, controller.isHomeImage(imageObj), controller, stableKey),
        );
      }),
    );
  }

  /// 构建优化的图片内容
  ///
  /// 新的图片内容构建方法，专注于避免重复加载和缓存失效
  ///
  /// 主要优化：
  /// - 使用稳定的缓存键策略
  /// - 智能的URL获取逻辑，避免不必要的网络请求
  /// - 改进的错误处理和重试机制
  /// - 更好的缓存管理，确保图片不会重复下载
  ///
  /// [imageObj] 图片模型对象，包含URL和重试状态
  /// [size] 图片显示尺寸
  /// [isHomeImage] 是否为首页图片
  /// [controller] 图片控制器，用于调用重试方法
  /// [stableKey] 稳定的缓存键，基于原始URL
  Widget _buildOptimizedImageContent(
    RxImageModel imageObj,
    double size,
    bool isHomeImage,
    AfCustomizeImageItemController controller,
    String stableKey,
  ) {
    // 检查是否正在重试，优先显示重试状态
    if (imageObj.isRetrying.value) {
      return _buildImageContentContainer(
        isHomeImage: isHomeImage,
        size: size,
        child: buildCupertinoLoadingWidget(size: size),
      );
    }

    // 智能获取显示URL：优先使用已缓存的turl
    final String displayUrl = imageObj.getDisplayUrl();

    // 如果没有临时URL，通过控制器异步获取但不阻塞当前渲染
    if (imageObj.turl.value.isEmpty && imageObj.url.value.isNotEmpty && !imageObj.isRetrying.value) {
      controller.asyncGetImageUrl(imageObj);
    }

    // 如果原始URL为空，显示错误占位符
    if (imageObj.url.value.isEmpty) {
      return _buildImageContentContainer(
        isHomeImage: isHomeImage,
        size: size,
        child: buildErrorWidget(size: size, backgroundColor: AppTheme.whiteColor),
      );
    }

    // 如果没有临时URL但有原始URL，显示加载指示器等待异步获取
    if (displayUrl.isEmpty) {
      return _buildImageContentContainer(
        isHomeImage: isHomeImage,
        size: size,
        child: buildCupertinoLoadingWidget(size: size),
      );
    }

    // 构建优化的CachedNetworkImage
    return _buildImageContentContainer(
      isHomeImage: isHomeImage,
      size: size,
      child: CachedNetworkImage(
        // 使用原始URL作为稳定的key，避免因turl变化导致的重建
        key: Key(stableKey),
        // 使用原始URL作为缓存键，确保相同图片使用同一缓存
        cacheKey: stableKey,
        // 使用实际的显示URL进行加载
        imageUrl: displayUrl,
        fit: BoxFit.contain,
        // 启用旧图片保持，在URL变化时保持显示
        useOldImageOnUrlChange: true,
        // 占位符
        placeholder: (_, __) => buildCupertinoLoadingWidget(size: size),
        // 优化的错误处理：通过控制器处理重试逻辑
        errorWidget: (context, url, error) {
          // 通过控制器处理重试逻辑，符合Clean Architecture原则
          final Widget errorWidget = controller.handleImageLoadError(imageObj, size);
          return errorWidget;
        },
        // 错误监听器
        errorListener: (exception) {
          LogUtil.e('CachedNetworkImage错误监听器触发: $exception');
        },
      ),
    );
  }

  /// 构建图片内容容器
  ///
  /// 统一的图片内容容器构建方法，处理首页图片的特殊样式
  ///
  /// [isHomeImage] 是否为首页图片
  /// [size] 容器尺寸
  /// [child] 子组件
  Widget _buildImageContentContainer({required bool isHomeImage, required double size, required Widget child}) {
    return Container(
      decoration: isHomeImage ? BoxDecoration(border: Border.all(color: AppTheme.whiteColor, width: 1)) : null,
      child: Container(color: AppTheme.whiteColor, child: child),
    );
  }

  /// 构建图片容器
  Widget _buildImageContainer({
    required double size,
    required Widget child,
    Color color = AppTheme.placeholderColor,
    double borderWidth = defaultBorderWidth,
    double radius = defaultBorderWidth,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: color, width: borderWidth),
        borderRadius: BorderRadius.circular(containerBorderRadius),
      ),
      child: ClipRRect(borderRadius: BorderRadius.circular(contentBorderRadius), child: child),
    );
  }

  /// 构建添加按钮
  Widget _buildAddButton(double size, AfCustomizeImageItemController controller) {
    return _buildImageContainer(
      size: size,
      child: InkWell(
        onTap: controller.onAddPressed,
        child: Container(
          width: size,
          height: size,
          color: AppTheme.whiteColor,
          child: const Center(
            child: Icon(Icons.add, size: addButtonIconSize, color: AppTheme.grayColor),
          ),
        ),
      ),
    );
  }
}

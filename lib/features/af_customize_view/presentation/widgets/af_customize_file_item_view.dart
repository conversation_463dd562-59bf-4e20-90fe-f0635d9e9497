import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/data/models/file_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_file_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/base_af_customize_view_entry.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 添付ファイルアイテム
class AfCustomizeFileItemView extends GetWidget<AfCustomizeFileItemController>
    with BaseAfCustomizeViewMixin, BaseAfCustomizeViewEntryMixin {
  /// === 布局常量 ===
  /// list布局的flex值
  static const int listFlexValue = 6;

  @override
  final RxAssetItemWrapper entry;
  @override
  final bool isPreview;
  @override
  final String? assetId;

  AfCustomizeFileItemView({super.key, required this.entry, required this.isPreview, this.assetId});

  @override
  String get tag => assetId ?? '';

  @override
  Widget build(BuildContext context) {
    List<dynamic>? files = [];
    if (entry.defaultData != '') {
      files = entry.defaultData as List<dynamic>?;
    } else {
      files = [];
    }
    controller.initData(entry);
    controller.initFiles(files);

    return Obx(() {
      // 履历情报情况
      if (controller.isHistory.value) {
        return _buildHistoryContainer();
      }

      // 正常情况
      return _buildNormalContainer();
    });
  }

  /// 履历情报专用容器
  Widget _buildHistoryContainer() {
    return buildBaseContainer(
      children: [
        Obx(() => buildTitle(controller.title.value, controller.isRequired.value)),
        buildHorizontalSpacing(),
        Expanded(flex: listFlexValue, child: _buildNormalContainer()),
      ],
    );
  }

  /// 构建通用容器
  Widget _buildNormalContainer() {
    return Column(
      children: [
        if (!(entry.isReadOnly.value)) _buildAddFileButton(),
        Obx(
          () => ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.files.length,
            itemBuilder: (context, index) {
              final file = controller.files[index];
              return Column(children: [buildDivider(), _buildFileItem(file, () => controller.onDeleteFile(index))]);
            },
          ),
        ),
      ],
    );
  }

  /// 构建添加文件按钮
  Widget _buildAddFileButton() {
    return buildArrowButton(text: 'ファイルを追加', onPressed: controller.onFileAddBtnClick);
  }

  /// 构建文件item
  Widget _buildFileItem(FileModel file, VoidCallback onDelete) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10),
      color: AppTheme.transparentColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => controller.onFileItemTap(file.fileName ?? ''),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(file.fileName ?? ''),
                    const SizedBox(height: 5),
                    Text(file.uploadDate ?? '', style: const TextStyle(fontSize: 13)),
                  ],
                ),
              ),
            ),
          ),
          InkWell(
            onTap: onDelete,
            child: Container(
              width: 40,
              height: 40,
              padding: const EdgeInsets.only(right: 3),
              alignment: Alignment.centerRight,
              child: const Icon(Icons.delete_outline, size: 18, color: AppTheme.black87Color),
            ),
          ),
        ],
      ),
    );
  }
}

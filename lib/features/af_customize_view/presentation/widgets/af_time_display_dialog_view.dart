import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/button_state.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/enums/date_type_enum.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_time_display_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

/// 时间选择对话框视图
class AfTimeDisplayDialogView extends GetView<AfTimeDisplayController> {
  const AfTimeDisplayDialogView({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: Container(
            decoration: const BoxDecoration(
              color: AppTheme.whiteColor,
              borderRadius: BorderRadius.all(Radius.circular(5)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // "清除"按钮
                Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton(
                    onPressed: () {
                      // 确保年月选择器已关闭
                      if (controller.isTimePickerOpen.value) {
                        controller.switchDateModal(false);
                      }
                      final result = controller.getTimeDate(ButtonState.clear);
                      Get.back(result: result);
                    },
                    child: const Text(
                      'クリア',
                      style: TextStyle(
                        color: AppTheme.timePickerPrimaryColor,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                // 日期选择器
                _buildCustomCalendarPicker(context),
                // 时间显示 - 只在date-time模式下显示
                if (controller.dateType.isDateTime)
                  Container(
                    height: 40,
                    color: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('時間', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
                        Obx(
                          () => GestureDetector(
                            key: controller.timeDisplayKey,
                            onTap: () {
                              _showTimePickerWheel(context);
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 7),
                              decoration: BoxDecoration(
                                color: const Color(0xffedeef0),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                '${controller.selectedDate.value?.hour.toString().padLeft(2, '0') ?? '00'}:${controller.selectedDate.value?.minute.toString().padLeft(2, '0') ?? '00'}',
                                style: const TextStyle(fontSize: 14, color: AppTheme.black87Color),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 10),
                // 分隔线
                const Divider(height: 1, color: AppTheme.timePickerDividerColor),
                // 底部按钮区域
                SizedBox(
                  height: 50,
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            // 确保年月选择器已关闭
                            if (controller.isTimePickerOpen.value) {
                              controller.switchDateModal(false);
                            }
                            final result = controller.getTimeDate(ButtonState.cancel);
                            Get.back(result: result);
                          },
                          child: const Text(
                            'キャンセル',
                            style: TextStyle(
                              color: AppTheme.timePickerCancelColor,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      Container(width: 1, color: AppTheme.grayColor, height: double.infinity),
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            // 确保年月选择器已关闭
                            if (controller.isTimePickerOpen.value) {
                              controller.switchDateModal(false);
                            }
                            final result = controller.getTimeDate(ButtonState.today);
                            Get.back(result: result);
                          },
                          child: const Text(
                            '今日',
                            style: TextStyle(
                              color: AppTheme.timePickerTodayColor,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      Container(width: 1, color: AppTheme.grayColor, height: double.infinity),
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            // 确保年月选择器已关闭
                            if (controller.isTimePickerOpen.value) {
                              controller.switchDateModal(false);
                            }
                            final result = controller.getTimeDate(ButtonState.finished);
                            Get.back(result: result);
                          },
                          child: const Text(
                            '完了',
                            style: TextStyle(
                              color: AppTheme.timePickerConfirmColor,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 显示时间选择器滚轮
  void _showTimePickerWheel(BuildContext context) {
    // 使用控制器中的方法显示时间选择器
    controller.showTimePickerPopup(context);
  }

  // 构建自定义日历选择器
  Widget _buildCustomCalendarPicker(BuildContext context) {
    // 创建一个持久的年月选择器状态变量，改用controller中的变量
    final showYearMonthPicker = controller.isTimePickerOpen;

    return Obx(() {
      // 获取当前选中日期或默认为今天
      final DateTime selectedDate = controller.selectedDate.value ?? DateTime.now();
      // 获取当前显示的月份或默认为选中日期的月份
      final DateTime currentDate = controller.currentDisplayMonth.value ?? selectedDate;
      // 获取当月第一天和天数
      final DateTime firstDayOfMonth = DateTime(currentDate.year, currentDate.month, 1);
      final int daysInMonth = DateTime(currentDate.year, currentDate.month + 1, 0).day;
      // 计算当月第一天是星期几（0是星期日，6是星期六）
      final int firstWeekdayOfMonth = firstDayOfMonth.weekday % 7;

      // 计算需要显示的行数
      final int totalDays = firstWeekdayOfMonth + daysInMonth;
      final int totalWeeks = (totalDays / 7).ceil();

      // 最小和最大日期
      final DateTime minDate = controller.minDate != null
          ? DateFormat('yyyy-MM-dd').parse(controller.minDate!)
          : DateTime(1900);
      final DateTime maxDate = controller.maxDate != null
          ? DateFormat('yyyy-MM-dd').parse(controller.maxDate!)
          : DateTime(DateTime.now().year + 10, 12, 31);

      return Stack(
        children: [
          GestureDetector(
            // 添加水平滑动切换月份
            onHorizontalDragEnd: (details) {
              if (details.primaryVelocity != null) {
                if (details.primaryVelocity! > 0) {
                  // 向右滑动，切换到上个月
                  controller.previousMonth();
                } else if (details.primaryVelocity! < 0) {
                  // 向左滑动，切换到下个月
                  controller.nextMonth();
                }
              }
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 年月标题和左右切换按钮
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 年月标题和下拉箭头
                      InkWell(
                        onTap: () {
                          // 切换年月选择器的显示状态
                          controller.switchDateModal(!showYearMonthPicker.value);
                        },
                        child: Row(
                          children: [
                            Text(
                              '${currentDate.year}年${currentDate.month}月',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: showYearMonthPicker.value
                                    ? AppTheme.timePickerPrimaryColor
                                    : AppTheme.darkBlueColor,
                              ),
                            ),
                            Icon(
                              showYearMonthPicker.value ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                              color: showYearMonthPicker.value
                                  ? AppTheme.timePickerPrimaryColor
                                  : AppTheme.darkBlueColor,
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: [
                          // 使用Material按钮确保点击区域
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: controller.previousMonth,
                              customBorder: const CircleBorder(),
                              child: const Padding(
                                padding: EdgeInsets.all(12.0),
                                child: Icon(Icons.chevron_left, color: AppTheme.darkBlueColor),
                              ),
                            ),
                          ),
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: controller.nextMonth,
                              customBorder: const CircleBorder(),
                              child: const Padding(
                                padding: EdgeInsets.all(12.0),
                                child: Icon(Icons.chevron_right, color: AppTheme.darkBlueColor),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // 星期标签
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Text(
                        '日',
                        style: TextStyle(
                          fontWeight: FontWeight.normal,
                          color: AppTheme.timePickerSundayColor,
                          fontSize: 12,
                        ),
                      ),
                      Text('月', style: TextStyle(fontWeight: FontWeight.normal, fontSize: 12)),
                      Text('火', style: TextStyle(fontWeight: FontWeight.normal, fontSize: 12)),
                      Text('水', style: TextStyle(fontWeight: FontWeight.normal, fontSize: 12)),
                      Text('木', style: TextStyle(fontWeight: FontWeight.normal, fontSize: 12)),
                      Text('金', style: TextStyle(fontWeight: FontWeight.normal, fontSize: 12)),
                      Text(
                        '土',
                        style: TextStyle(
                          fontWeight: FontWeight.normal,
                          color: AppTheme.timePickerSaturdayColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),

                const Divider(height: 8, thickness: 0.5),

                // 日历网格
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 7,
                    childAspectRatio: 1.5,
                    crossAxisSpacing: 4,
                    mainAxisSpacing: 4,
                  ),
                  itemCount: 7 * totalWeeks,
                  itemBuilder: (context, index) {
                    // 计算日期
                    final int weekday = index % 7;
                    final int day = index - firstWeekdayOfMonth + 1;

                    // 判断是否在当月范围内
                    if (day < 1 || day > daysInMonth) {
                      return Container();
                    }

                    final DateTime date = DateTime(currentDate.year, currentDate.month, day);

                    // 检查日期是否在可选范围内
                    final bool isSelectable =
                        (date.isAfter(minDate) || controller.isSameDay(date, minDate)) &&
                        (date.isBefore(maxDate) || controller.isSameDay(date, maxDate));

                    // 检查是否为当前选中的年月日（完全匹配）
                    final bool isFullySelected =
                        controller.selectedDate.value != null &&
                        date.year == controller.selectedDate.value!.year &&
                        date.month == controller.selectedDate.value!.month &&
                        date.day == controller.selectedDate.value!.day;

                    // 根据星期几设置文本颜色
                    Color textColor;
                    if (!isSelectable) {
                      textColor = AppTheme.timePickerDisabledTextColor;
                    } else if (weekday == 0) {
                      textColor = AppTheme.timePickerSundayColor;
                    } else if (weekday == 6) {
                      textColor = AppTheme.timePickerSaturdayColor;
                    } else {
                      textColor = AppTheme.timePickerNormalTextColor;
                    }

                    return GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: isSelectable
                          ? () {
                              if (controller.dateType.isDateTime) {
                                final newDate = DateTime(
                                  date.year,
                                  date.month,
                                  date.day,
                                  controller.selectedDate.value?.hour ?? 0,
                                  controller.selectedDate.value?.minute ?? 0,
                                );
                                controller.onNameChange(newDate);
                              } else {
                                controller.onNameChange(date);
                              }
                            }
                          : null,
                      child: Container(
                        decoration: BoxDecoration(
                          color: isFullySelected ? AppTheme.timePickerDateActiveColor : Colors.transparent,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Center(
                          child: Text(
                            day.toString(),
                            style: TextStyle(
                              color: isFullySelected ? AppTheme.timePickerSelectedWeekdayColor : textColor,
                              fontWeight: isFullySelected ? FontWeight.bold : FontWeight.normal,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          // 年月选择器覆盖层
          Obx(
            () => showYearMonthPicker.value
                ? Positioned(
                    top: 42.0, // 进一步上调选择器位置
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Container(
                      color: Colors.white,
                      child: Column(
                        mainAxisSize: MainAxisSize.min, // 使用最小空间
                        crossAxisAlignment: CrossAxisAlignment.end, // 右对齐
                        children: [
                          // 添加关闭按钮行，改为单独的按钮而非整行容器
                          GestureDetector(
                            onTap: () => controller.switchDateModal(false),
                            child: const Padding(
                              padding: EdgeInsets.only(right: 8.0, top: 2.0, bottom: 0.0),
                              child: Icon(Icons.close, size: 18),
                            ),
                          ),
                          // 年月选择器主体
                          Expanded(child: controller.buildYearMonthPicker(context)),
                        ],
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      );
    });
  }
}

/// 时间显示视图组件
class AfTimeDisplayView extends GetView<AfTimeDisplayController> {
  final String? initialDate;
  final String? maxDate;
  final String? minDate;
  final DateType dateType;
  final Function(ButtonState valueState, Map<String, String> valueTime, String? formattedDate) onTimeSelected;

  AfTimeDisplayView({
    super.key,
    this.initialDate,
    this.maxDate,
    this.minDate,
    this.dateType = DateType.date,
    required this.onTimeSelected,
  }) {
    controller.initialize(initialDate: initialDate, maxDate: maxDate, minDate: minDate, dateType: dateType);
  }

  void _showDatePicker(BuildContext context) {
    // 使用控制器中的方法显示日期选择器
    controller.showDatePicker(context, onTimeSelected);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(onTap: () => _showDatePicker(context), child: Obx(() => _buildDisplayContent()));
  }

  // 提取显示内容到单独方法
  Widget _buildDisplayContent() {
    if (controller.dateExample.isEmpty) {
      return Container(width: 20, height: 20, color: Colors.transparent);
    } else {
      return _buildDateDisplay();
    }
  }

  Widget _buildDateDisplay() {
    if (controller.isEmptyValue()) {
      return const Text('<空白>');
    } else if (controller.isNormalDate()) {
      return Text(controller.dateExample.value);
    } else if (controller.isNowValue()) {
      return const Text('現在');
    } else if (controller.isTodayValue()) {
      return const Text('今日');
    }
    return const SizedBox();
  }
}

/// 时间选择器对话框
class AfTimeDisplayDialog {
  /// 显示时间选择对话框
  static Future<Map<String, dynamic>?> show({
    required BuildContext context,
    String? initialDate,
    String? maxDate,
    String? minDate,
    DateType dateType = DateType.date,
  }) async {
    final controller = Get.find<AfTimeDisplayController>();
    controller.initialize(initialDate: initialDate, maxDate: maxDate, minDate: minDate, dateType: dateType);

    // 获取屏幕尺寸
    final size = MediaQuery.of(context).size;

    // 计算对话框的约束尺寸，基于屏幕比例
    final maxDialogHeight = size.height * 0.8; // 最大高度为屏幕高度的80%
    final maxDialogWidth = size.width * 0.9; // 最大宽度为屏幕宽度的90%

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          backgroundColor: AppTheme.timePickerDialogBackgroundColor,
          child: ConstrainedBox(
            constraints: BoxConstraints(maxHeight: maxDialogHeight, maxWidth: maxDialogWidth),
            child: const AfTimeDisplayDialogView(),
          ),
        );
      },
    );

    return result;
  }
}

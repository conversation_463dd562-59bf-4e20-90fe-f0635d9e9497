import 'dart:convert';

import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/number_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_master_default_value_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/processors/base_item_processor.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_calculate_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:get/get.dart';

/// Master类型处理器
/// Master类型处理器
///
/// 主要功能:
/// - 处理Master类型的资产项目数据
/// - 限制显示项目数量为5条
/// - 格式化显示项目的值
/// - 设置百分比和单位
///
/// 主要方法:
/// - process(): 处理Master类型项目
///   1. 检查是否显示默认值
///   2. 限制显示项目数量为5条
///   3. 处理显示项目的值格式化
///   4. 根据项目类型进行不同处理
///
/// - setItemPercentageUnitValue(): 设置百分比和单位
///   1. 处理计算项目的小数点配置
///   2. 设置百分比显示
///   3. 设置千分位格式
///   4. 设置单位显示
class MasterProcessor extends ItemProcessor {
  GetMasterDefaultValueUseCase? getMasterDefaultValueUseCase;

  MasterProcessor() {
    getMasterDefaultValueUseCase = Get.find<GetMasterDefaultValueUseCase>();
  }

  @override
  void process(ProcessorParams param) {
    final RxAssetItemWrapper item = param.item;

    if (param.isShowMasterDefaultValue &&
        param.scene == AfCustomizeViewScene.assetHistory &&
        item.defaultData is! Map) {
      if (item.masterId == null || item.masterId == '') {
        return;
      }
      final Map<int, dynamic> display = {};
      // 如果item.masterid正常存在
      item.optionObject?.masterDisplayItems?.forEach((element) async {
        if (getMasterDefaultValueUseCase != null) {
          final masterDefaultValue = await getMasterDefaultValueUseCase!(
            GetMasterDefaultValueParams(
              masterTypeId: item.optionObject?.masterTypeId,
              masterId: int.tryParse(item.masterId),
              itemName: element.itemName,
            ),
          );
          LogUtil.d('masterDefaultValue: $masterDefaultValue');
          final itemId = element.itemId;
          if (masterDefaultValue != '' && itemId != null) {
            display[itemId] = masterDefaultValue.masterListDefaultValue;
          }
        }
      });
      item.updateDefaultData({'masterId': item.masterId, 'display': display});
    }

    // 只展示 5 条
    final List<MasterDisplayItemModel> allMasterDisplayItems =
        item.optionObject?.masterDisplayItems ?? <MasterDisplayItemModel>[];

    // 过滤掉没有有效名称的项目
    final List<MasterDisplayItemModel> masterDisplayItems = allMasterDisplayItems
        .where(
          (masterItem) =>
              (masterItem.itemDisplayName != null && masterItem.itemDisplayName!.isNotEmpty) ||
              (masterItem.itemName != null && masterItem.itemName!.isNotEmpty),
        )
        .toList();

    // 限制最多显示5条
    if (masterDisplayItems.length > 5) {
      masterDisplayItems.removeRange(5, masterDisplayItems.length);
    }

    // display 对象
    final defaultData = item.defaultData;
    final tempDisplay = defaultData is Map<String, dynamic> ? defaultData['display'] : null;

    for (var masterDisplayItem in masterDisplayItems) {
      // 显示项目配置内容
      final String? option = masterDisplayItem.option;
      final itemOption = option == null || option.isEmpty ? {} : jsonDecode(option);

      if (tempDisplay is String) {
        masterDisplayItem.itemValue = tempDisplay;
        continue;
      } else if (tempDisplay is Map<String, dynamic>) {
        masterDisplayItem.itemValue = tempDisplay[masterDisplayItem.itemId.toString()] ?? '';
        continue;
      }
      // 表示内容
      final tempItemValue = tempDisplay?['${masterDisplayItem.itemId}'];

      // 如果显示内容为空，则设置为空
      if (defaultData == null ||
          (defaultData is Map && defaultData.isEmpty) ||
          tempDisplay == null ||
          (tempDisplay is Map && tempDisplay.isEmpty) ||
          tempItemValue == null ||
          tempItemValue.toString().isEmpty) {
        masterDisplayItem.itemValue = '';
        continue;
      }

      // 数字类型
      if (SharedItemTypeEnum.number.equals(masterDisplayItem.itemType)) {
        final numberCommaDecimalPoint = int.parse(itemOption['numberCommaDecimalPoint'] ?? '0');
        final numberDecimalPoint = int.parse(itemOption['numberDecimalPoint'] ?? '0');
        if (tempItemValue != null) {
          final String safeItemValue = tempItemValue is String ? tempItemValue : tempItemValue.toString();
          masterDisplayItem.itemValue = NumberUtils.formatNumberWithCommaAndDecimal(
            numberCommaDecimalPoint,
            numberDecimalPoint,
            safeItemValue,
            true,
          );
          setItemPercentageUnitValue(item);
        }
      } else {
        // 非数字其他类型时，如果 option 为空，则直接设置原始数据
        if (option != null && option.isNotEmpty) {
          masterDisplayItem.itemValue = tempItemValue;
        }
      }

      // 如果不存在数据则赋值空字符串
      masterDisplayItem.itemValue ??= '';
    }
  }

  /// 为数据设置百分比及unit
  ///
  /// 参数:
  /// * [item] - 需要设置的资产项目
  /// * [isCalculate] - 是否为计算项目,默认为false
  ///
  /// 功能:
  /// 1. 检查项目数据和配置是否有效
  /// 2. 处理计算项目的特殊配置
  /// 3. 根据百分比标志设置百分比显示
  /// 4. 根据千分位设置格式化显示
  /// 5. 根据单位标志设置单位显示
  void setItemPercentageUnitValue(RxAssetItemWrapper item, {bool isCalculate = false}) {
    final tempOptionObj = item.optionObject;
    if (item.defaultData == null || tempOptionObj == null) {
      return;
    }

    if (isCalculate) {
      if (SharedCalculateValueItemEnum.digital.equals(tempOptionObj.calculateType)) {
        return;
      }
    }

    int decimalPoint = int.parse(tempOptionObj.numberDecimalPoint ?? '0');
    int commaDecimalPoint = int.parse(tempOptionObj.numberCommaDecimalPoint ?? '0');
    final String percentage = tempOptionObj.percentage ?? '0';
    final String unitFlg = tempOptionObj.unitFlg ?? '';

    // 为计算项目设置单独的百分比及unit
    if (isCalculate) {
      // 处理 calculateDecimalPoint
      final calculateDecimalPoint = tempOptionObj.calculateDecimalPoint;
      decimalPoint = switch (calculateDecimalPoint) {
        null => 0,
        final int value => value,
        final String value => value.isEmpty ? 0 : int.tryParse(value) ?? 0,
        _ => 0,
      };

      // 处理 commaDecimalPoint
      commaDecimalPoint = int.parse(tempOptionObj.calculateCommaDecimalPoint ?? '0');
    }

    if (percentage == '1') {
      item.percentage = NumberUtils.formatPercentage(item.defaultData, decimal: decimalPoint);
    } else if (commaDecimalPoint != 0) {
      item.itemValue = NumberUtils.formatNumber(item.defaultData, commaDecimalPoint, decimalPoint);
    }

    if (unitFlg == '1') {
      item.unit = item.optionObject?.unit ?? '';
    }
  }
}

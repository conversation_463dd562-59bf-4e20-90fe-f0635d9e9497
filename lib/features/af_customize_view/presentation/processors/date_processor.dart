import 'package:asset_force_mobile_v2/core/utils/datetime_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/processors/base_item_processor.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';

/// 日期处理器
class DateProcessor implements ItemProcessor {
  @override
  bool isValidData(String? data) {
    if (data == null || data.isEmpty) return false;
    return !data.contains(',');
  }

  @override
  void process(ProcessorParams param) {
    if (param.item.defaultData == null || param.item.itemValue.toString().contains('assetItemName:')) return;

    final defaultData = param.item.defaultData.toString();

    if (defaultData == 'currentUserInput') {
      param.item.updateDefaultData('');
      return;
    }

    final bool isDateTime = param.item.optionObject?.dateType == 'dateTime';
    final String dateInfo = isDateTime
        ? DateTimeUtils.getDateTimeFromDateType(defaultData)
        : DateTimeUtils.getDateFromDateType(defaultData);
    final String format = isDateTime ? 'yyyy/MM/dd HH:mm' : 'yyyy/MM/dd';

    if (DateTime.tryParse(dateInfo) != null) {
      param.item.updateDefaultData(dateInfo);
    }

    _processSpecialDateValues(param.item, defaultData, dateInfo, format, param.scene);
  }

  void _processSpecialDateValues(
    RxAssetItemWrapper item,
    String defaultData,
    String dateInfo,
    String format,
    AfCustomizeViewScene scene,
  ) {
    if (defaultData.isEmpty) return;

    final bool isDetailScene =
        scene == AfCustomizeViewScene.assetDetailEdit ||
        scene == AfCustomizeViewScene.assetDetail ||
        scene == AfCustomizeViewScene.assetNewCreate ||
        scene == AfCustomizeViewScene.assetHistory ||
        scene == AfCustomizeViewScene.assetHistoryEdit;

    if (['now', '<now>'].contains(defaultData)) {
      item.updateDefaultData(
        isDetailScene ? DateTimeUtils.getFormatDateHasTime(date: DateTime.now(), format: format) : '現在',
      );
      item.valueForShowModel = '現在';
    } else if (['today', '<today>'].contains(defaultData)) {
      item.updateDefaultData(
        isDetailScene ? DateTimeUtils.getFormatDateHasTime(date: DateTime.now(), format: format) : '今日',
      );
      item.valueForShowModel = '今日';
    } else if (defaultData.startsWith('after_')) {
      item.updateDefaultData(dateInfo);
    }
  }
}

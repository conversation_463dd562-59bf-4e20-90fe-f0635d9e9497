import 'package:asset_force_mobile_v2/core/utils/number_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/processors/base_item_processor.dart';

/// 货币处理器
class CurrencyProcessor implements ItemProcessor {
  @override
  bool isValidData(String? data) {
    if (data == null || data.isEmpty) return false;
    return !data.contains(',');
  }

  @override
  void process(ProcessorParams param) {
    final defaultData = param.item.defaultData?.toString();
    if (isValidData(defaultData)) {
      final safeDefaultData = defaultData ?? '';
      param.item.updateDefaultData(
        NumberUtils.formatNumberWithCommaAndDecimal(
          3,
          int.parse(param.item.optionObject?.currencyDecimalPoint ?? '0'),
          NumberUtils.convertScientificNotationToDecimal(safeDefaultData),
          true,
        ),
      );
    }
  }
}

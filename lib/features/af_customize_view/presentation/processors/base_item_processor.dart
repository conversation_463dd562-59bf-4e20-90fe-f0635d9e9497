import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';

/// 资产项目处理器基类
abstract class ItemProcessor {
  /// 处理资产项目
  void process(ProcessorParams param);

  /// 检查数据是否有效且不包含逗号
  bool isValidData(String? data) {
    if (data == null || data.isEmpty) return false;
    return !data.contains(',');
  }
}

class ProcessorParams {
  final RxAssetItemWrapper item;
  final AfCustomizeViewScene scene;
  final bool isShowMasterDefaultValue;
  final Map<String, List<RxAssetItemWrapper>> assetDict;

  ProcessorParams({
    required this.item,
    required this.scene,
    required this.assetDict,
    this.isShowMasterDefaultValue = false,
  });
}

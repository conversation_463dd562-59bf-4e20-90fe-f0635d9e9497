import 'package:asset_force_mobile_v2/core/utils/datetime_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/number_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/processors/base_item_processor.dart';
import 'package:collection/collection.dart';

/// 数字类型处理器
class NumberItemProcessor implements ItemProcessor {
  @override
  bool isValidData(String? data) {
    if (data == null || data.isEmpty) return false;
    return !data.contains(',');
  }

  @override
  void process(ProcessorParams param) {
    final item = param.item;
    final defaultData = item.defaultData?.toString();
    final assetDict = param.assetDict;

    // 处理经过日数
    if (item.itemName == 'passedTime') {
      _processPassedTime(item, assetDict);
      return;
    }

    // 处理普通数字
    if (item.defaultData != '個別に設定されています' &&
        !item.itemValue.toString().contains('assetItemName:') &&
        defaultData != null &&
        isValidData(defaultData) &&
        NumberUtils.isNumeric(defaultData)) {
      _formatNumber(item);
    }
  }

  void _processPassedTime(RxAssetItemWrapper item, Map<String, List<RxAssetItemWrapper>> assetDict) {
    if (item.defaultData == null || item.defaultData.toString().isEmpty) {
      final createdDateItem = assetDict.values
          .expand((list) => list)
          .firstWhereOrNull((item) => item.itemType == 'createdDate');

      if (createdDateItem != null) {
        item.updateDefaultData(
          DateTimeUtils.getDaysDifference(startDate: createdDateItem.defaultData, endDate: DateTime.now()),
        );
      }
    }
  }

  void _formatNumber(RxAssetItemWrapper item) {
    final numberCommaDecimalPoint = int.parse(item.optionObject?.numberCommaDecimalPoint ?? '0');
    final numberDecimalPoint = int.parse(item.optionObject?.numberDecimalPoint ?? '0');
    item.updateDefaultData(
      NumberUtils.formatNumberWithCommaAndDecimal(
        numberCommaDecimalPoint,
        numberDecimalPoint,
        item.defaultData.toString(),
        true,
      ),
    );
    _setItemPercentageUnitValue(item);
  }

  /// 为数据设置百分比及unit
  void _setItemPercentageUnitValue(RxAssetItemWrapper item, {bool isCalculate = false}) {
    if (item.defaultData == null || item.optionObject == null) {
      return;
    }

    if (isCalculate) {
      if (item.optionObject?.calculateType == 'digital') {
        return;
      }
    }

    int decimalPoint = int.parse(item.optionObject?.numberDecimalPoint ?? '0');
    int commaDecimalPoint = int.parse(item.optionObject?.numberCommaDecimalPoint ?? '0');
    final String percentage = item.optionObject?.percentage ?? '0';
    final String unitFlg = item.optionObject?.unitFlg ?? '';

    // 为计算项目设置单独的百分比及unit
    if (isCalculate) {
      // 处理 calculateDecimalPoint
      final calculateDecimalPoint = item.optionObject?.calculateDecimalPoint;
      decimalPoint = switch (calculateDecimalPoint) {
        null => 0,
        final int value => value,
        final String value => value.isEmpty ? 0 : int.tryParse(value) ?? 0,
        _ => 0,
      };

      // 处理 commaDecimalPoint
      commaDecimalPoint = int.parse(item.optionObject?.calculateCommaDecimalPoint ?? '0');
    }

    if (percentage == '1') {
      item.percentage = NumberUtils.formatPercentage(item.defaultData, decimal: decimalPoint);
    } else if (commaDecimalPoint != 0) {
      item.itemValue = NumberUtils.formatNumber(item.defaultData, commaDecimalPoint, decimalPoint);
    }

    if (unitFlg == '1') {
      item.unit = item.optionObject?.unit ?? '';
    }
  }
}

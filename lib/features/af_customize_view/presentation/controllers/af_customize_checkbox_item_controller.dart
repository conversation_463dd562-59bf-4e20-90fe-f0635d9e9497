import 'dart:convert';

import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/enums/schedule_item_ids.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/base_af_customize_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/asset_alert/data/models/asset_alert_arguments.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Checkbox 项控制器
class AfCustomizeCheckboxItemController extends BaseAfCustomizeController {
  RxList<String> checkboxValues = <String>[].obs;

  final RxBool isScheduleAlertSetting = false.obs;

  final NavigationService navigatorService;

  AfCustomizeCheckboxItemController({required super.assetId, required this.navigatorService});

  /// 是否多选模式
  bool get isMultiSelect => entry.optionObject?.checkboxMultiFlg == '1';

  /// 获取选项列表
  List<String> get options {
    // 如果是多选模式，使用 checkboxOptions
    if (isMultiSelect) {
      return entry.optionObject?.checkboxOptions ?? [];
    }
    // 单选模式，只有一个选项
    return ['1'];
  }

  @override
  void updateValue(RxAssetItemWrapper tempEntry) {
    super.updateValue(tempEntry);

    // 初始化数据
    isScheduleAlertSetting.value =
        AfCustomizeViewScene.assetSchedule == afCustomizeViewController.scene &&
        ScheduleItemIds.alertSetting.equals(entry.itemId);
  }

  /// 获取 Checkbox 是否选中
  bool isChecked(String option) {
    final defaultData = entry.defaultData;
    if (defaultData == null) return false;

    final isMultiSelect = entry.optionObject?.checkboxMultiFlg == '1';
    if (!isMultiSelect) {
      return defaultData.toString() == '1';
    }

    // 处理多选模式
    if (defaultData is List) {
      return defaultData.map((e) => e.toString()).contains(option);
    }

    return false;
  }

  /// 当 Checkbox 状态改变时调用的回调函数。
  void onCheckChanged(String option, bool isChecked) {
    if (isMultiSelect) {
      if (isChecked) {
        if (!checkboxValues.contains(option)) {
          checkboxValues.add(option);
        }
      } else {
        checkboxValues.remove(option);
      }
    } else {
      checkboxValues.value = isChecked ? ['1'] : [];
    }

    // 更新 entry 的 defaultData
    if (isMultiSelect) {
      updateDefaultData(checkboxValues.toList());
    } else {
      updateDefaultData(checkboxValues.isEmpty ? '0' : '1');
    }
  }

  /// 设置 Checkbox 值。
  ///
  /// 该函数根据条目的默认数据设置 Checkbox 的值。
  /// 如果条目的默认数据为空，则不进行任何操作。
  ///
  /// @param itemId 条目的 ID。
  /// @param defaultData 条目的默认数据。
  /// @return 设置后的 Checkbox 值。
  /// @throws Exception 如果条目的默认数据为空。
  void setCheckboxValues() {
    checkboxValues.clear();

    var defaultData = entry.defaultData;
    if (defaultData == null) return;

    if (isMultiSelect) {
      if (defaultData is String) {
        defaultData = jsonDecode(defaultData);
      }

      if (defaultData is List) {
        checkboxValues.addAll(defaultData.map((e) => e.toString()));
      }
    } else {
      if (defaultData.toString() == '1') {
        checkboxValues.add('1');
      }
    }
  }

  @override
  void onClose() {
    checkboxValues.close();
    super.onClose();
  }

  /// schedule AlertSetting
  handleAlertSettings() {
    final arguments = AssetAlertArguments(
      scheduleEventList: afCustomizeViewController.scheduleEventList,
      appointment: afCustomizeViewController.appointment,
      // 预览模式下保存按钮不可见，非预览模式下保存按钮可见
      isSaveButtonVisible: !afCustomizeViewController.previewFlg.value,
    );

    navigatorService.navigateTo(AutoRoutes.assetAlert, arguments: arguments);
  }

  /// 获取主轴对齐方式
  ///
  MainAxisAlignment getMainAxisAlignment() {
    if (options.length <= 1) {
      return MainAxisAlignment.center;
    } else {
      return MainAxisAlignment.start;
    }
  }
}

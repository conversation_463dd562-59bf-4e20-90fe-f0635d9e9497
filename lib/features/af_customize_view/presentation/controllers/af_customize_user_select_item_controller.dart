import 'dart:convert';

import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/data/models/user_select_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/base_af_customize_controller.dart';
import 'package:asset_force_mobile_v2/features/list_selector/domain/models/list_selector_params.dart';
import 'package:get/get.dart';

/// 自定义视图用户选择项控制器
///
/// 负责管理自定义视图中的用户选择项的状态和业务逻辑
class AfCustomizeUserSelectItemController extends BaseAfCustomizeController {
  /// 导航服务
  final NavigationService navigationService;

  /// 选中的用户值
  final Rx<UserSelectModel?> selectedUser = Rx<UserSelectModel?>(null);

  AfCustomizeUserSelectItemController({required super.assetId, required this.navigationService});

  @override
  void initData(RxAssetItemWrapper tempEntry) {
    super.initData(tempEntry);
    _initializeSelectedUser();
  }

  /// 初始化选中的用户
  void _initializeSelectedUser() {
    if (entry.defaultData == null || entry.defaultData == '') return;

    try {
      final Map<String, dynamic> jsonData = entry.defaultData is String
          ? jsonDecode(entry.defaultData.toString())
          : entry.defaultData as Map<String, dynamic>;
      selectedUser.value = UserSelectModel.fromJson(jsonData);
    } catch (e) {
      LogUtil.e('Error parsing user data: $e');
    }
  }

  /// 获取显示的用户名
  String get displayValue => selectedUser.value?.userName.toString() ?? '';

  /// 处理用户选择
  Future<void> onUserSelect() async {
    final result = await navigationService.navigateTo(
      AutoRoutes.userGroupSelector,
      arguments: ListSelectorParams(title: entry.itemDisplayName ?? '', items: entry.optionObject?.data ?? []),
    );

    if (result != null) {
      if (result is UserSelectModel) {
        selectedUser.value = result;
      }
      updateDefaultData(result);
    }
  }

  /// 更新选中的用户
  void updateSelectedUser(UserSelectModel user) {
    selectedUser.value = user;
    updateDefaultData(jsonEncode(user.toJson()));
    update();
  }

  @override
  void onClose() {
    selectedUser.close();
    super.onClose();
  }
}

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 自定义视图基础控制器
///
/// 为所有自定义视图项目控制器提供通用功能和接口
/// 包含基础的数据管理、状态控制和事件处理逻辑
abstract class BaseAfCustomizeController extends BaseController {
  /// 资产ID
  final String? assetId;

  /// 条目包装器
  late RxAssetItemWrapper entry;

  /// 条目标题
  final title = ''.obs;

  /// 是否必填
  final isRequired = false.obs;

  /// 是否可编辑
  final isEditAble = true.obs;

  /// Worker 列表，用于管理响应式监听器
  final List<Worker> _workers = [];

  BaseAfCustomizeController({this.assetId});

  /// 动态获取 AfCustomizeViewController
  AfCustomizeViewController get afCustomizeViewController => Get.find<AfCustomizeViewController>(tag: assetId ?? '');

  @override
  void onInit() {
    super.onInit();
    // 监听 AfCustomizeViewController 的 previewFlg 变化
    try {
      _workers.add(ever(afCustomizeViewController.previewFlg, (_) => _updateIsEditAble()));
    } catch (e) {
      // 如果 AfCustomizeViewController 还没有初始化，忽略错误
    }
  }

  /// 初始化数据
  ///
  /// 参数:
  /// * [entry] - 资产项包装器，包含项目数据和状态
  void initData(RxAssetItemWrapper entry) {
    updateValue(entry);
  }

  void updateValue(RxAssetItemWrapper tempEntry) {
    entry = tempEntry;
    title.value = entry.itemDisplayName ?? '';
    isRequired.value = checkItemRequired();
    _updateIsEditAble();

    // 监听响应式只读状态变化
    _workers.add(ever(entry.isReadOnly, (_) => _updateIsEditAble()));
  }

  /// 更新可编辑状态
  void _updateIsEditAble() {
    isEditAble.value = checkItemEditable();
  }

  /// 获取trailingIcon
  IconData get trailingIcon {
    if (!isEditAble.value) {
      return Icons.lock_outline;
    }

    if (SharedItemTypeEnum.hyperlink.equals(entry.itemType)) {
      return Icons.edit;
    }

    if (SharedItemTypeEnum.date.equals(entry.itemType)) {
      return Icons.keyboard_arrow_down;
    }

    return Icons.keyboard_arrow_right;
  }

  /// 获取middleIcon
  IconData? get middleIcon {
    if (SharedItemTypeEnum.map.equals(entry.itemType)) {
      return Icons.location_on;
    }
    return null;
  }

  /// 获取是否多行
  bool get isMultiLine {
    return SharedItemTypeEnum.textarea.equals(entry.itemType);
  }

  /// 检查是否可编辑
  bool checkItemEditable() {
    // 不可编辑类型
    // 使用列表包含判断，更加简洁优雅
    final nonEditableTypes = [
      SharedItemTypeEnum.calculate, // 计算类型
      SharedItemTypeEnum.appurInfoSummary, // 履历信息合计
    ];
    if (nonEditableTypes.any((type) => type.equals(entry.itemType))) {
      return false;
    }

    // 检查只读条目，如果是只读条目的情况不可编辑
    if (checkItemReadOnly()) {
      return false;
    }

    // 系统项目不允许编辑
    if (entry.sysSetFlg == '1') {
      return false;
    }

    //  如果是预览模式，则不可编辑
    if (afCustomizeViewController.previewFlg.value) {
      return false;
    }

    // 检查是否可编辑权限
    return entry.isEditPermissions == '1';
  }

  /// 检查是否可编辑权限
  bool checkItemVisible() {
    // 检查是否可编辑权限
    return entry.isEditPermissions == '1';
  }

  /// 检查是否只读
  bool checkItemReadOnly() {
    return entry.isReadOnly.value;
  }

  /// 检查是否必填
  bool checkItemRequired() {
    return entry.isRequired ?? false;
  }

  /// 更新默认数据
  ///
  /// 更新项目的默认数据，触发重新计算和变更通知
  /// 始终立即执行自定义 JavaScript 逻辑
  ///
  /// 参数:
  /// * [defaultData] - 要更新的默认数据
  void updateDefaultData(Object defaultData) {
    entry.updateDefaultData(defaultData);
    recalculateItems();

    // 始终立即执行 JavaScript 逻辑
    _executeCustomizeLogicJsImmediately();

    afCustomizeViewController.onItemChanged(entry);
  }

  /// 立即执行自定义逻辑 JavaScript
  ///
  /// 用于需要实时响应的场景，如关键字段变更时
  /// 使用 Future.microtask 避免阻塞当前操作
  void _executeCustomizeLogicJsImmediately() {
    Future.microtask(() async {
      try {
        LogUtil.d('执行自定义逻辑 JavaScript:base_af_customize_controller');
        await afCustomizeViewController.executeCustomizeLogicJs();
      } catch (e) {
        LogUtil.d('执行自定义逻辑 JavaScript 失败: $e');
        // 静默处理错误，避免影响用户操作
        // 错误日志已在 executeCustomizeLogicJs 中记录
      }
    });
  }

  /// 重新计算计算项目
  void recalculateItems() {
    afCustomizeViewController.recalculateItems(entry.itemName ?? '');
  }

  @override
  void onClose() {
    // 释放所有 Worker
    for (final worker in _workers) {
      worker.dispose();
    }
    _workers.clear();
    super.onClose();
  }
}

import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/base_af_customize_controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AfCustomizeDigitalSignItemController extends BaseAfCustomizeController {
  final GetTurlUseCase _getTurlUseCase = Get.find();

  final RxString info = ''.obs;

  AfCustomizeDigitalSignItemController({required super.assetId});

  Future<String> getImageUrl(List<dynamic> imageObj) {
    final mapImageObj = imageObj.cast<Map<String, dynamic>>();
    if (mapImageObj.isEmpty) {
      return Future.value('');
    }

    final String? turl = mapImageObj.firstOrNull?['turl'] as String?;
    final String? url = mapImageObj.firstOrNull?['url'] as String?;

    return turl != null && turl.isNotEmpty ? Future.value(turl) : getTurl(url ?? '');
  }

  @override
  void initData(RxAssetItemWrapper assetItemWrapper) {
    super.initData(assetItemWrapper);
    info.value = (entry.defaultData == null || entry.defaultData == '') ? '未設定' : '';
  }

  /// 获取图片临时URL
  /// - [url] : 原始图片URL
  Future<String> getTurl(String url) async {
    try {
      return await _getTurlUseCase(url);
    } catch (e) {
      // 如果获取临时URL失败，返回原始URL
      return url;
    }
  }

  /// 图片构建逻辑
  Widget buildImageWidget(AsyncSnapshot<String> snapshot) {
    // 如果是加载中、没有数据、数据为空，则不显示图片
    if (snapshot.connectionState == ConnectionState.waiting || !snapshot.hasData || snapshot.data!.isEmpty) {
      return const SizedBox.shrink();
    }

    return CachedNetworkImage(
      fit: BoxFit.contain,
      imageUrl: snapshot.data ?? '',
      errorWidget: (context, url, error) => const SizedBox(
        height: 120,
        width: double.infinity,
        child: Center(child: Icon(Icons.error)),
      ),
    );
  }
}

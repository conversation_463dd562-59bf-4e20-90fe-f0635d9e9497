import 'dart:convert';

import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/master_display_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/data/models/master_display_option_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/base_af_customize_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/models/master_select_params.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/models/master_select_result.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Master项目控制器
///
/// 用于处理Master类型自定义视图项的业务逻辑
class AfCustomizeMasterItemController extends BaseAfCustomizeController {
  /// 导航服务
  final NavigationService navigationService;

  /// 场景
  late final AfCustomizeViewScene scene;

  /// 获取选项对象
  OptionObjModel get optionObject => entry.optionObject ?? OptionObjModel();

  /// Master显示项列表
  final rxMasterDisplayItems = <MasterDisplayItemModel>[].obs;

  /// 获取Master显示项列表
  List<MasterDisplayItemModel> get _masterDisplayItems {
    final allItems = optionObject.masterDisplayItems ?? [];
    // 过滤掉没有有效名称的项目
    final validItems = allItems
        .where(
          (masterItem) =>
              (masterItem.itemDisplayName != null && masterItem.itemDisplayName!.isNotEmpty) ||
              (masterItem.itemName != null && masterItem.itemName!.isNotEmpty),
        )
        .toList();

    // 限制最多显示5个项目
    return validItems.length > 5 ? validItems.take(5).toList() : validItems;
  }

  /// 构造函数
  ///
  /// [navigationService] - 导航服务
  AfCustomizeMasterItemController({required super.assetId, required this.navigationService}) {
    scene = afCustomizeViewController.scene;
  }

  /// 更新主数据显示内容
  ///
  /// 参数:
  /// * [result] - 主数据选择结果,包含选中的主数据ID和显示值
  ///
  /// 处理流程:
  /// 1. 如果有选择结果:
  ///   - 更新 entry 的默认数据
  ///   - 遍历主数据显示项列表
  ///   - 根据 itemId 更新每个显示项的值
  /// 2. 更新可观察的主数据显示列表
  void updateMasterData({MasterSelectResult? result = null}) {
    if (result != null) {
      // 主数据变更会立即执行 JavaScript，因为可能影响其他字段的显示和计算
      updateDefaultData(result.toJson());
      for (var i = 0; i < _masterDisplayItems.length; i++) {
        final item = _masterDisplayItems[i];
        var displayValue = result.display[item.itemId.toString()];
        if (displayValue != null) {
          debugPrint('displayValue: $displayValue');
          if (displayValue is List || displayValue is Map) {
            displayValue = jsonEncode(displayValue);
          }
          item.itemValue = displayValue.toString();
        }
      }
    }

    rxMasterDisplayItems.value = _masterDisplayItems;
    rxMasterDisplayItems.refresh();
  }

  /// Master项目点击事件处理
  ///
  /// [item] - 被点击的Master显示项
  /// [index] - 被点击项的索引
  Future<void> onMasterItemTap(MasterDisplayItemModel item, int index) async {
    final currentSelectedList = <String>[];
    final displayValuesMap = entry.defaultData['display'];
    final masterDisplayItems = _masterDisplayItems; // 使用限制数量后的列表
    // 遍历主数据显示项列表,收集已选择的值
    if (displayValuesMap != null && masterDisplayItems.isNotEmpty) {
      for (var i = 0; i < masterDisplayItems.length; i++) {
        final item = masterDisplayItems[i];
        if (displayValuesMap is String) {
          currentSelectedList.add(displayValuesMap);
          continue;
        }
        final displayValue = displayValuesMap[item.itemId.toString()];
        if (displayValue != null) {
          currentSelectedList.add(displayValue.toString());
        }
      }
    }

    // 确保 masterDisplayItems 中的每个项都有有效的显示名称
    // 创建副本以避免修改原始数据
    final safeDisplayItems = masterDisplayItems
        .map(
          (item) => MasterDisplayItemModel(
            itemId: item.itemId,
            itemName: item.itemName,
            itemDisplayName: item.itemDisplayName,
            itemType: item.itemType,
            option: item.option,
            itemValue: item.itemValue,
          ),
        )
        .toList();

    // 确保显示名称的有效性
    MasterDisplayUtils.ensureValidDisplayNames(safeDisplayItems);

    final MasterSelectResult? result = await navigationService.navigateTo(
      AutoRoutes.masterSelect,
      arguments: MasterSelectParams(
        title: item.itemDisplayName ?? '',
        masterTypeId: optionObject.masterTypeId?.toString() ?? '',
        level: index,
        masterDisplayList: safeDisplayItems,
        currentSelectedList: currentSelectedList,
        scene: scene,
      ),
    );
    if (result != null) {
      updateMasterData(result: result);
    }
  }

  /// 删除项目事件处理
  void onDeleteItem() {
    // 清空默认数据
    updateDefaultData({});
    // 清空显示项目的值
    for (var item in rxMasterDisplayItems) {
      item.itemValue = '';
    }
    // 刷新显示
    rxMasterDisplayItems.refresh();
  }

  /// 显示Master值
  ///
  /// [masterDisplayItem] - Master显示项
  ///
  /// 返回格式化后的显示值
  String showMasterValue(MasterDisplayItemModel masterDisplayItem) {
    final optionStr = jsonDecode(masterDisplayItem.option ?? '{}');
    final masterDisplayOptionModel = MasterDisplayOptionModel.fromJson(optionStr);
    final itemValue = masterDisplayItem.itemValue;

    if (itemValue == null) {
      return ':';
    }

    // Checkbox类型处理
    if (SharedItemTypeEnum.checkbox.equals(masterDisplayItem.itemType)) {
      // 单选框处理
      if (masterDisplayOptionModel.checkboxMultiFlg == '0') {
        return itemValue == '1' ? ': あり' : ': なし';
      }
      // 多选框处理
      if (itemValue == null || itemValue == '') {
        return '';
      }
      final itemValues = itemValue is String ? jsonDecode(itemValue) : itemValue;
      return itemValues?.join(',') ?? '';
    }

    return ': ${itemValue.toString().replaceAll('\n', ' ')}';
  }

  /// 删除图标
  IconData get deleteIcon => Icons.delete_outline;

  /// 箭头图标
  IconData get arrowIcon => Icons.keyboard_arrow_right_outlined;
}

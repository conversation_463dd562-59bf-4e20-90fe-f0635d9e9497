import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/base_af_customize_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AfCustomizeAssetCountItemController extends BaseAfCustomizeController {
  /// 资产数量
  final RxString count = '1'.obs;

  /// 文本编辑控制器
  late final TextEditingController textController;

  AfCustomizeAssetCountItemController({required super.assetId}) {
    textController = TextEditingController(text: count.value);

    // 监听数量变化，同步到文本控制器和外部数据
    ever(count, (String value) {
      final currentSelection = textController.selection;
      textController.text = value;
      // 保持光标位置
      textController.selection = currentSelection;
      // 通知外部数据更新
      updateDefaultData(value);
    });
  }

  @override
  void onClose() {
    textController.dispose();
    super.onClose();
  }

  /// 增加数量
  void increment() {
    final currentCount = int.tryParse(count.value) ?? 1;
    count.value = (currentCount + 1).toString();
  }

  /// 减少数量
  void decrement() {
    final currentCount = int.tryParse(count.value) ?? 1;
    if (currentCount > 1) {
      count.value = (currentCount - 1).toString();
    }
  }

  /// 更新数量
  void updateCount(String value) {
    if (value.isEmpty) {
      count.value = '1';
      return;
    }
    final newCount = int.tryParse(value);
    if (newCount != null && newCount > 0) {
      count.value = newCount.toString();
    }
  }
}

import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/base_af_customize_controller.dart';
import 'package:get/get.dart';

/// Radio项控制器
class AfCustomizeRadioItemController extends BaseAfCustomizeController {
  /// Radio选中状态Map
  /// key: itemId, value: 选中的值
  final RxMap<String, String> radioValues = <String, String>{}.obs;
  String itemId = '';
  List<String> optionItems = [];
  String? initialValue;

  AfCustomizeRadioItemController({required super.assetId});

  @override
  void initData(RxAssetItemWrapper tempEntry) {
    super.initData(tempEntry);
    itemId = entry.itemId ?? '';
    optionItems = entry.optionObject?.radioOptions ?? [];
    initialValue = entry.defaultData?.toString();

    setRadioValue(itemId, initialValue);
  }

  /// 设置Radio值
  void setRadioValue(String itemId, String? value) {
    radioValues[itemId] = value ?? '';
  }

  /// 更新Radio值
  void updateRadioValue(String itemId, String value) {
    radioValues[itemId] = value;
  }

  /// 清理Radio值
  void clearRadioValue(String itemId) {
    radioValues.remove(itemId);
  }

  @override
  void onClose() {
    radioValues.clear();
    super.onClose();
  }
}

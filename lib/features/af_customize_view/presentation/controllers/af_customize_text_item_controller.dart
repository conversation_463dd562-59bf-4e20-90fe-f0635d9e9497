import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/check_validate_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/base_af_customize_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/handlers/item_handler_factory.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/handlers/item_handler_strategy.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/handlers/text_item_handler.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 自定义文本项目控制器
///
/// 使用策略模式重构，将不同类型项目的处理逻辑分离到对应的策略类中
/// 主控制器只负责管理生命周期和委托具体操作给策略类
class AfCustomizeTextItemController extends BaseAfCustomizeController {
  /// 是否为计算项目
  final isCalculate = false.obs;

  /// 是否正在编辑
  final isEditing = false.obs;

  /// 文本编辑控制器
  late final TextEditingController textEditingController;

  /// 焦点节点
  late final FocusNode focusNode;

  /// 导航服务
  final NavigationService navigationService;

  /// 当前项目的处理策略
  late ItemHandlerStrategy _itemHandler;

  /// 校验服务
  final CheckValidateUseCase _checkValidateUseCase = Get.find<CheckValidateUseCase>();

  /// 构造函数
  ///
  /// [assetId] 资产ID，用于获取对应的 AfCustomizeViewController
  /// [navigationService] 导航服务
  AfCustomizeTextItemController({required super.assetId, required this.navigationService}) {
    textEditingController = TextEditingController();
    focusNode = FocusNode();
    focusNode.addListener(_handleFocusChange);
  }

  void _handleFocusChange() {
    // 安全检查：确保FocusNode没有被释放
    try {
      if (!focusNode.hasFocus) {
        endEditing();
      }
    } catch (e) {
      // FocusNode已被释放，忽略此次事件
    }
  }

  @override
  void onClose() {
    // 清理处理策略的资源
    _itemHandler.dispose();

    // 安全地释放资源
    try {
      focusNode.removeListener(_handleFocusChange);
      focusNode.dispose();
    } catch (e) {
      // FocusNode可能已经被释放，忽略错误
    }

    try {
      textEditingController.dispose();
    } catch (e) {
      // TextEditingController可能已经被释放，忽略错误
    }

    super.onClose();
  }

  @override
  void initData(RxAssetItemWrapper entry) {
    super.initData(entry);
    isCalculate.value = SharedItemTypeEnum.calculate.equals(entry.itemType);

    // 创建对应类型的处理策略
    _itemHandler = ItemHandlerFactory.createHandler(
      entry: entry,
      viewController: afCustomizeViewController,
      navigationService: navigationService,
      updateDefaultData: updateDefaultData,
      textEditingController: textEditingController,
      focusNode: focusNode,
      onEditingComplete: _validateEntry,
    );

    // 初始化处理策略
    _itemHandler.initData();

    // 设置显示值
    entry.valueForShow.value = _itemHandler.getDisplayValue();
    textEditingController.text = entry.valueForShow.value;
  }

  /// 处理点击事件
  ///
  /// 委托给对应的处理策略
  void handleTap(BuildContext context) {
    if (!isEditAble.value) return;

    if (_shouldShowPicker()) {
      _itemHandler.handleTap(context);
    } else {
      startEditing();
    }
  }

  bool _shouldShowPicker() {
    return SharedItemTypeEnum.date.equals(entry.itemType) ||
        SharedItemTypeEnum.map.equals(entry.itemType) ||
        SharedItemTypeEnum.list.equals(entry.itemType);
  }

  /// 开始编辑
  ///
  /// 如果是文本类型项目，则启动编辑模式
  void startEditing() {
    if (!isEditAble.value) return;
    isEditing.value = true;

    // 如果是文本类型，调用其专有方法
    if (_itemHandler is TextItemHandler) {
      (_itemHandler as TextItemHandler).startEditing();
    }
  }

  /// 结束编辑
  ///
  /// 委托给对应的处理策略
  Future<void> endEditing() async {
    if (!isEditing.value) return;
    isEditing.value = false;

    // 如果是文本类型，调用其专有方法
    if (_itemHandler is TextItemHandler) {
      (_itemHandler as TextItemHandler).endEditing();
    } else {
      _validateEntry();
    }
  }

  /// 验证项目
  Future<void> _validateEntry() async {
    final result = await _checkValidateUseCase(
      CheckValidateParams(entry: entry, scene: afCustomizeViewController.scene),
    );

    if (!result.showAlert) {
      entry.clearMessage();
    }
  }

  /// 更新默认数据
  ///
  /// 更新项目的defaultData和显示值
  @override
  void updateDefaultData(Object value) {
    // 调用基类方法，确保触发appointment同步
    super.updateDefaultData(value);

    // 更新显示值和文本控制器
    entry.valueForShow.value = _itemHandler.getDisplayValue();

    // 安全地更新TextEditingController，防止访问已释放的资源
    try {
      textEditingController.text = entry.valueForShow.value;
    } catch (e) {
      // TextEditingController已被释放，忽略此次更新
      // 这种情况通常发生在控制器生命周期结束时
    }
  }
}

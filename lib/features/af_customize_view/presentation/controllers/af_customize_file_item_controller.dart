import 'package:asset_force_mobile_v2/core/constant/file_type_constant.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/core/utils/datetime_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/picker_helper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/data/models/file_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/download_file_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/upload_file_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/base_af_customize_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AfCustomizeFileItemController extends BaseAfCustomizeController {
  final files = <FileModel>[].obs;
  final UploadFileUseCase uploadFileUseCase;
  final DownloadFileUseCase downloadFileUseCase;
  final DialogService dialogService;
  final GetTurlUseCase getTurlUseCase;

  /// 当前正在下载的文件索引
  final RxInt downloadingIndex = RxInt(-1);

  /// 下载进度
  final RxDouble downloadProgress = RxDouble(0.0);

  /// 上传进度
  final RxDouble uploadProgress = RxDouble(0.0);

  /// 是否正在上传
  final RxBool isUploading = RxBool(false);

  /// 上次更新进度的时间
  DateTime? _lastProgressUpdate;

  /// 是否是履历情报
  ///
  /// 履历情报中页面效果与资产详情页不同，左侧额外显示label
  final RxBool isHistory = false.obs;

  /// 进度更新的最小时间间隔（毫秒）
  static const int _progressUpdateInterval = 100;

  AfCustomizeFileItemController({
    required super.assetId,
    required this.uploadFileUseCase,
    required this.downloadFileUseCase,
    required this.dialogService,
    required this.getTurlUseCase,
  });

  @override
  void onInit() {
    super.onInit();
    initHistoryStatus();
  }

  /// 初始化履历情报状态
  void initHistoryStatus() {
    final afCustomizeViewController = Get.find<AfCustomizeViewController>(tag: assetId);
    isHistory.value =
        afCustomizeViewController.scene == AfCustomizeViewScene.assetHistory ||
        afCustomizeViewController.scene == AfCustomizeViewScene.assetHistoryEdit;
  }

  /// 显示上传进度对话框
  void _showUploadDialog() {
    Get.bottomSheet(
      SafeArea(
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.all(16),
          width: double.infinity,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 第一行: 显示标题
              const Text('アップロード中', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
              const SizedBox(height: 20),

              // 第二行: 显示进度条和进度百分比
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(() {
                    return Expanded(
                      child: LinearProgressIndicator(value: uploadProgress.value, backgroundColor: Colors.grey[300]),
                    );
                  }),

                  // 显示上传进度百分比
                  Obx(() {
                    return SizedBox(
                      width: 60,
                      child: Text('${(uploadProgress.value * 100).toStringAsFixed(2)}%', textAlign: TextAlign.center),
                    );
                  }),
                ],
              ),
            ],
          ),
        ),
      ),
      isScrollControlled: true,
      backgroundColor: AppTheme.whiteColor,
      isDismissible: false,
      barrierColor: Colors.black.withAlpha(128),
    );
  }

  /// 更新上传进度（带防抖）
  void _updateUploadProgress(int count, int total) {
    final now = DateTime.now();
    if (_lastProgressUpdate != null && now.difference(_lastProgressUpdate!).inMilliseconds < _progressUpdateInterval) {
      return;
    }
    _lastProgressUpdate = now;

    final progress = count / total;
    uploadProgress.value = progress;
  }

  /// 更新文件列表数据
  ///
  /// 将当前的文件列表转换为 JSON 格式并更新到 entry 中
  /// - 自动处理空列表的情况
  /// - 确保数据同步
  ///
  /// 返回更新是否成功
  bool updateFileListData() {
    try {
      if (files.isEmpty) {
        updateDefaultData([]);
        return true;
      }

      final fileJsonList = files.map((file) => file.toJson()).toList();
      updateDefaultData(fileJsonList);
      return true;
    } catch (e) {
      LogUtil.e('更新文件列表失败：$e');
      return false;
    }
  }

  /// 初始化自定义文件选择控件的默认值
  ///
  /// [defaultData] 是一个可选参数, 如果不为空, 则将其转换为 [FileModel] 的列表,并将其分配给 [files] 中.
  ///
  /// 该方法通常用于从服务器端获取数据, 并将其设置为控件的默认值.
  ///
  void initFiles(List<dynamic>? defaultData) {
    if (defaultData != null) {
      files.value = defaultData.map((file) => FileModel.fromJson(file as Map<String, dynamic>)).toList();
      updateFileListData();
    }
  }

  /// 处理文件添加按钮点击事件
  ///
  /// 该方法会打开系统文件选择器让用户选择文件
  /// 选择完成后会将文件信息添加到 [files] 列表中
  ///
  /// 示例:
  /// ```dart
  /// onFileAddBtnClick(); // 打开文件选择器
  /// ```
  Future<void> onFileAddBtnClick() async {
    final selectedFilePath = await PickerHelper.selectFileByTypes(fileTypes: FileTypeConstant.uploadAllowFiles);
    LogUtil.d('图片选择器返回的图片地址: $selectedFilePath');
    if (selectedFilePath.isNotEmpty) {
      try {
        isUploading.value = true;
        uploadProgress.value = 0;

        // 显示上传进度弹窗
        _showUploadDialog();

        LogUtil.d('开始上传文件');
        final UploadFileResult response = await uploadFileUseCase(
          UploadFileParams(
            filePath: selectedFilePath,
            onProgress: (count, total) {
              _updateUploadProgress(count, total);
            },
          ),
        );

        LogUtil.d('上传文件成功,${response.path}');
        final newFile = FileModel(
          url: response.path,
          turl: '',
          size: response.size.toInt(),
          total: 100,
          loaded: 100,
          status: 'success',
          fileName: selectedFilePath.split('/').last,
          progress: 1,
          uploadDate: DateTimeUtils.getFormatDateTime(DateTime.now()),
          uploadUserName: response.uploadUserName,
        );
        files.add(newFile);
        if (!updateFileListData()) {
          throw Exception('更新文件列表失败');
        }

        // 关闭进度弹窗
        if (Get.isBottomSheetOpen ?? false) {
          Get.back();
        }

        // 上传成功提示
        await dialogService.show(title: '完了', content: 'アップロードが完了しました', confirmText: 'OK', type: DialogType.info);
      } catch (e) {
        LogUtil.d('上传文件出错：$e');
        // 关闭进度弹窗
        if (Get.isBottomSheetOpen ?? false) {
          Get.back();
        }
        await dialogService.show(title: 'エラー', content: e.toString(), confirmText: 'OK', type: DialogType.error);
      } finally {
        isUploading.value = false;
        uploadProgress.value = 0;
        LogUtil.d('上传文件结束');
      }
    }
  }

  /// 删除指定索引位置的文件
  ///
  /// [index] 要删除的文件索引
  ///
  /// 该方法会从 [files] 列表中移除指定索引位置的文件
  /// 并更新 [entry] 中的默认数据
  ///
  /// 示例:
  /// ```dart
  /// onDeleteFile(0); // 删除第一个文件
  /// ```
  void onDeleteFile(int index) {
    if (index >= 0 && index < files.length) {
      files.removeAt(index);
      if (!updateFileListData()) {
        LogUtil.e('删除文件后更新列表失败');
        dialogService.show(title: 'エラー', content: '削除に失敗しました。再試行してください', type: DialogType.error);
      }
    }
  }

  /// 处理文件项点击事件
  ///
  /// [fileName] 被点击的文件名
  ///
  /// 点击后会：
  /// 1. 查找对应的文件
  /// 2. 开始下载文件
  /// 3. 显示下载进度
  Future<void> onFileItemTap(String fileName) async {
    try {
      if (downloadingIndex.value != -1) {
        await dialogService.show(title: '警告', content: 'ダウンロード中のファイルがあります', type: DialogType.error);
        return;
      }

      // 查找文件索引和文件对象
      final index = files.indexWhere((file) => file.fileName == fileName);
      if (index == -1) {
        LogUtil.e('文件未找到：$fileName');
        return;
      }
      final file = files[index];

      if (file.url == null || file.url!.isEmpty) {
        LogUtil.e('文件URL为空：$fileName');
        await dialogService.show(title: 'エラー', content: 'ファイルのURLが無効です', type: DialogType.error);
        return;
      }

      downloadingIndex.value = index;
      downloadProgress.value = 0;

      // 显示下载进度弹窗
      _showPopup(fileName);

      final String realUrl = await getTurlUseCase(file.url!);
      LogUtil.d('获取到的临时URL: $realUrl');

      // 使用下载用例下载文件
      await downloadFileUseCase(
        DownloadFileParams(
          url: realUrl,
          fileName: file.fileName ?? fileName,
          onProgress: (progress) {
            downloadProgress.value = progress;
          },
        ),
      );

      // 下载完成，关闭弹窗
      if (Get.isBottomSheetOpen ?? false) {
        Get.back();
      }

      downloadingIndex.value = -1;
      downloadProgress.value = 0;
    } catch (e) {
      LogUtil.e('文件下载失败：$e');

      // 发生错误时关闭弹窗
      if (Get.isBottomSheetOpen ?? false) {
        Get.back();
      }

      downloadingIndex.value = -1;
      downloadProgress.value = 0;

      await dialogService.show(title: 'エラー', content: 'ダウンロードに失敗しました。再試行してください', type: DialogType.error);
    }
  }

  /// 获取文件下载状态
  bool isDownloading(int index) => downloadingIndex.value == index;

  /// 获取文件下载进度
  double getDownloadProgress(int index) {
    return isDownloading(index) ? downloadProgress.value : 0.0;
  }

  /// 显示下载进度弹窗
  ///
  /// [fileName] 文件名
  ///
  /// 该方法会显示一个底部弹窗,包含:
  /// - 文件名
  /// - 下载进度条
  /// - 下载进度百分比
  ///
  /// 弹窗无法手动关闭,只能等待下载完成或失败时自动关闭
  void _showPopup(String fileName) {
    Get.bottomSheet(
      SafeArea(
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.all(16),
          width: double.infinity,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 第一行: 显示文件名
              Text(fileName, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
              const SizedBox(height: 20),

              // 第二行: 显示进度条和进度百分比
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(() {
                    return Expanded(
                      child: LinearProgressIndicator(
                        value: downloadProgress.value / 100,
                        backgroundColor: Colors.grey[300],
                      ),
                    );
                  }),

                  // 显示下载进度百分比
                  Obx(() {
                    return SizedBox(
                      width: 60,
                      child: Text('${downloadProgress.value.toStringAsFixed(2)}%', textAlign: TextAlign.center),
                    );
                  }),
                ],
              ),
            ],
          ),
        ),
      ),
      isScrollControlled: true,
      backgroundColor: AppTheme.whiteColor,
      isDismissible: false,
      barrierColor: Colors.black.withAlpha(128),
    );
  }

  @override
  void onClose() {
    downloadingIndex.close();
    downloadProgress.close();
    uploadProgress.close();
    isUploading.close();
    super.onClose();
  }
}

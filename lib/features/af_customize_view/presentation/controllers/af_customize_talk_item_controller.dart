import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AfCustomizeTalkItemController extends GetxController {
  late final WebViewController webViewController;

  // 添加一个变量来控制WebView是否已初始化
  final isWebViewInitialized = false.obs;

  String _talkLiveUrl = '';

  @override
  void onInit() {
    super.onInit();

    // 配置 WebView 控制器
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..enableZoom(false)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // 可以在这里处理加载进度
          },
          onPageStarted: (String url) {
            // 可以在这里处理页面开始加载
          },
          onPageFinished: (String url) {
            // 可以在这里处理页面加载完成
            isWebViewInitialized.value = true;
          },
          onWebResourceError: (WebResourceError error) {
            // 可以在这里处理加载错误
          },
        ),
      );
  }

  @override
  void onClose() {
    // 确保WebView在控制器销毁时正确释放资源
    isWebViewInitialized.value = false;
    super.onClose();
  }

  updateTalkUrl(String talkLiveUrl) {
    if (talkLiveUrl.isEmpty) {
      return;
    }

    // 如果URL相同且WebView已初始化，不重复加载
    if (_talkLiveUrl == talkLiveUrl && isWebViewInitialized.value) {
      return;
    }

    _talkLiveUrl = talkLiveUrl;
    webViewController.loadRequest(Uri.parse(_talkLiveUrl));
  }
}

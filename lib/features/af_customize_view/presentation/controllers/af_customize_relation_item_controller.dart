import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/string_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/relation_asset_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/states/asset_relation_state.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_relation_add/domain/entities/asset_relation_add_result.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_relation_list/data/entitys/relation_asset_list_arugment.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_relation_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/asset_type_sort_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:get/get.dart';

/// 关联资产项目控制器
///
/// 用于管理关联资产列表的控制器类。
/// 提供了添加、编辑、删除和点击关联资产等功能。
class AfCustomizeRelationItemController extends GetxController {
  final NavigationService navigationService;

  /// 关联资产列表
  ///
  /// 使用 RxList 存储关联资产列表,支持响应式更新
  final RxList<RelationAssetModel> relatedAssets = <RelationAssetModel>[].obs;

  String assetId;

  List<AssetTypeListModel> assetTypeList = [];

  /// 关联资产状态
  AssetRelationState? relationState;

  /// 动态获取自定义视图控制器
  AfCustomizeViewController get afController => Get.find<AfCustomizeViewController>(tag: assetId);

  AfCustomizeRelationItemController({required this.navigationService, required this.assetId});

  /// 添加关联资产
  ///
  /// 处理添加关联资产按钮的点击事件
  Future<void> onRelationAddBtnClick() async {
    final relationAddResult = await navigationService.navigateTo(AutoRoutes.assetRelationAdd);
    LogUtil.d('relationAddResult: $relationAddResult');
    if (relationAddResult != null) {
      final result = relationAddResult as AssetRelationAddResult;
      final asset = result.info;
      final assetTypeName = result.assetTypeName;
      final isFromScan = result.isFromScan;
      LogUtil.d('asset: $asset');
      LogUtil.d('assetTypeName: $assetTypeName');
      LogUtil.d('isFromScan: $isFromScan');

      // 检查是否为当前资产
      if (asset.assetId.toString() == assetId) {
        CommonDialog.show(content: '対象と同一の資産は関連資産として登録することができません。', confirmText: '閉じる');
        return;
      }

      // 检查是否已经存在相同资产类型的关联资产
      final assetTypeId = asset.assetTypeId.toString();
      final existingAssetTypeIndex = relatedAssets.indexWhere((element) => element.assetTypeId == assetTypeId);

      // 创建新的关联资产项
      final newRelationItem = AssetRelationItem(
        asset.tenantId,
        asset.assetId,
        asset.assetTypeId,
        int.parse(assetId),
        asset.assetText,
        null, // 默认关联类型
        asset.createdById != null ? int.parse(asset.createdById!) : 0, // createdById
        asset.createdDate?.toString(), // createdDate
        asset.modifiedById != null ? int.parse(asset.modifiedById!) : 0, // modifiedById
        asset.modifiedDate?.toString(), // modifiedDate
        assetTypeName,
        asset.state,
      );

      if (existingAssetTypeIndex >= 0) {
        // 已存在该资产类型，添加到现有列表中
        final existingAsset = relatedAssets[existingAssetTypeIndex];
        final updatedItems = [...existingAsset.assetRelationItems, newRelationItem];

        // 创建更新后的关联资产模型
        final updatedAsset = RelationAssetModel(
          assetTypeId: existingAsset.assetTypeId,
          assetTypeName: existingAsset.assetTypeName,
          count: updatedItems.length,
          assetRelationItems: updatedItems,
        );

        // 更新列表
        relatedAssets[existingAssetTypeIndex] = updatedAsset;
      } else {
        // 不存在该资产类型，创建新的关联资产类型
        final newAsset = RelationAssetModel(
          assetTypeId: assetTypeId,
          assetTypeName: assetTypeName,
          count: 1,
          assetRelationItems: [newRelationItem],
        );

        // 添加到列表
        relatedAssets.add(newAsset);
      }

      // 更新关联资产状态，以便保存时能将新添加的关联资产一起保存
      updateRelationState();

      // 显示添加成功提示
      CommonDialog.showCustomToast('関連資産が追加されました');
    }
  }

  /// 删除关联资产
  ///
  /// [asset] - 要删除的关联资产对象
  /// 从关联资产列表中移除指定资产
  void onDeleteAsset(RelationAssetModel asset) {
    relatedAssets.remove(asset);

    // 更新关联资产状态，以便保存时能将删除的关联资产一起保存
    updateRelationState();

    // 显示删除成功提示
    CommonDialog.showCustomToast('関連資産が削除されました');
  }

  /// 点击关联资产列表
  ///
  /// [asset] - 被点击的关联资产对象
  Future<void> onRelationAssetListItemTap(RelationAssetModel asset) async {
    // 确保关联资产状态不为空
    if (relationState == null) return;

    await navigationService.navigateTo(
      StringUtils.buildLevelPath(AutoRoutes.relationAssetListId, asset.assetTypeId),
      arguments: RelationAssetListArgument(
        relationAssetModel: asset,
        assetId: assetId,
        needNavigationBar: afController.needNavigationBar ?? true,
      ),
      id: SharedNavBarEnum.assetList.navigatorId,
    );
  }

  /// 初始化关联资产状态
  ///
  /// [relation] - 关联资产状态对象,包含关联资产的相关数据
  /// 用于初始化关联资产列表,将关联资产状态对象中的数据同步到控制器中
  void initRelation(AssetRelationState relation, List<AssetTypeListModel> assetTypeList) {
    relationState = relation;
    this.assetTypeList.assignAll(assetTypeList);
    // 创建临时列表
    final List<RelationAssetModel> tempList = [];

    relation.relateDict.forEach((key, value) {
      final assetType = assetTypeList.firstWhereOrNull((element) => element.assetTypeId == int.parse(key));
      final String assetTypeName = assetType?.assetTypeName ?? '';

      tempList.add(
        RelationAssetModel(
          assetTypeId: key,
          assetTypeName: assetTypeName,
          count: value.length,
          assetRelationItems: value,
        ),
      );
    });

    // 一次性更新列表
    relatedAssets.assignAll(tempList);
  }

  /// 更新关联资产状态
  ///
  /// 根据当前的关联资产列表更新关联资产状态
  void updateRelationState() {
    if (relationState == null) return;

    // 构建新的关联资产字典
    final Map<String, List<AssetRelationItem>> newRelateDict = {};

    // 遍历关联资产列表，构建关联资产字典
    for (var asset in relatedAssets) {
      newRelateDict[asset.assetTypeId] = asset.assetRelationItems;
    }

    // 更新关联资产字典
    relationState?.updateRelateDict(newRelateDict);

    // 生成关联资产列表字符串
    final StringBuffer buffer = StringBuffer();
    for (var asset in relatedAssets) {
      for (var item in asset.assetRelationItems) {
        buffer.write('${item.assetId},');
      }
    }

    // 更新关联资产列表字符串
    relationState?.updateRelateListString(buffer.toString());
  }
}

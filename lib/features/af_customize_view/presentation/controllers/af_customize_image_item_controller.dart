import 'package:asset_force_mobile_v2/core/event_bus/event_bus.dart';
import 'package:asset_force_mobile_v2/core/events/image_deleted_event.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/picker_helper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/data/models/image_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/image_preview_result.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_image_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/upload_image_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/base_af_customize_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/image_preview/presentation/pages/image_preview_page.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

/// 图片项控制器
///
/// 负责管理图片项的显示、上传、删除等操作
/// 遵循Clean Architecture原则，通过事件总线与其他组件通信
class AfCustomizeImageItemController extends BaseAfCustomizeController {
  final GetTurlUseCase getTurlUseCase;
  final UploadImageUseCase uploadImageUseCase;
  final NavigationService navigationService;

  /// 图片URL列表
  final RxList<RxImageModel> imageUrls = <RxImageModel>[].obs;

  /// 是否正在上传
  final RxBool isUploading = false.obs;

  /// 图片之间的默认间距
  static const double defaultSpacing = 5.0;

  /// 默认每行显示的图片数量
  static const int defaultCrossAxisCount = 3;

  /// 图片之间的间距
  double spacing = defaultSpacing;

  /// 每行显示的图片数量
  int crossAxisCount = defaultCrossAxisCount;

  AfCustomizeImageItemController({
    required super.assetId,
    required this.getTurlUseCase,
    required this.uploadImageUseCase,
    required this.navigationService,
  }) {
    // 在初始化时注册到主控制器
    afCustomizeViewController.registerImageController(this);
  }

  @override
  void onClose() {
    // 在控制器销毁时注销
    afCustomizeViewController.unregisterImageController(this);
    // 清理资源
    imageUrls.clear();
    super.onClose();
  }

  /// 设置布局参数
  void setLayoutParams({double? spacing, int? crossAxisCount}) {
    if (spacing != null) this.spacing = spacing;
    if (crossAxisCount != null) this.crossAxisCount = crossAxisCount;
  }

  /// 计算每个图片项的宽度
  double calculateItemWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return (screenWidth - (spacing * (crossAxisCount + 1))) / crossAxisCount;
  }

  /// 初始化图片URL列表
  List<dynamic> initImageUrls() {
    return entry.getImagesMap();
  }

  /// 设置图片列表
  void setImageUrls(List<dynamic> urls) {
    imageUrls.value = entry.getRxImageModels(urls);
  }

  /// 更新图片URL列表
  ///
  /// 同步 imageUrls 和 entry.imageModels 中的 isHomeImage 状态
  /// 使用安全的查找方式，避免 "No element" 异常
  void updateImageUrls() {
    imageUrls.forEach((element) {
      // 使用 .value 获取响应式字符串的实际值进行比较
      final matchingModel = entry.imageModels.firstWhereOrNull((e) => e.url.value == element.url.value);

      // 如果找到匹配的模型，更新 isHomeImage 状态
      if (matchingModel != null) {
        element.isHomeImage.value = matchingModel.isHomeImage.value;
      }
    });
  }

  /// 添加按钮点击事件回调
  void onAddPressed() async {
    try {
      // 显示选择图片来源的对话框
      await Get.bottomSheet(
        SafeArea(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(14)),
                  child: Column(
                    children: [
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: const Text(
                          '画像の選択',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 13, color: Colors.black54),
                        ),
                      ),
                      const Divider(height: 1, color: Color(0xFFE0E0E0)),
                      TextButton(
                        onPressed: () {
                          Get.back();
                          _pickImage(ImageSource.camera);
                        },
                        style: TextButton.styleFrom(minimumSize: const Size.fromHeight(57)),
                        child: const Text('写真を撮る', style: TextStyle(fontSize: 20, color: Colors.blue)),
                      ),
                      const Divider(height: 1, color: Color(0xFFE0E0E0)),
                      TextButton(
                        onPressed: () {
                          Get.back();
                          _pickImage(ImageSource.gallery);
                        },
                        style: TextButton.styleFrom(minimumSize: const Size.fromHeight(57)),
                        child: const Text('画像を選択', style: TextStyle(fontSize: 20, color: Colors.blue)),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(14)),
                  child: TextButton(
                    onPressed: () => Get.back(),
                    style: TextButton.styleFrom(minimumSize: const Size.fromHeight(57)),
                    child: const Text(
                      'キャンセル',
                      style: TextStyle(fontSize: 20, color: Colors.blue, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
        ),
      );
    } catch (e) {
      CommonDialog.show(content: '选择图片失败，请重试');
    }
  }

  /// 选择或拍摄图片
  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await PickerHelper.pickImage(source);

      if (image == null) return;

      // 显示上传中状态
      isUploading.value = true;

      // 上传图片
      final response = await uploadImageUseCase(UploadImageParams(file: image));

      LogUtil.d('response :${response.url} ${response.path}');

      // 构建新图片数据
      final ImageModel newImage = ImageModel(
        url: response.path,
        turl: response.url,
        loaded: false,
        fileName: '',
        uploadDate: '',
        isHomeImage: false,
      );

      // 更新图片列表
      final List<RxImageModel> updatedUrls = [RxImageModel.fromImageModel(newImage), ...imageUrls];
      imageUrls.value = updatedUrls;
      updateDefaultData(updatedUrls);

      // 如果updateHomeImage返回true表示发生了变化
      final bool hasChanged = afCustomizeViewController.updateHomeImage(item: entry);

      // 只有当发生变化时才重新同步数据
      if (hasChanged) {
        setImageUrls(initImageUrls());
      }
    } catch (e) {
      CommonDialog.show(content: '图片上传失败，请重试');
    } finally {
      isUploading.value = false;
    }
  }

  /// 获取图片临时URL
  /// - [url] : 原始图片URL
  Future<String> getTurl(String url) async {
    try {
      return await getTurlUseCase(url);
    } catch (e) {
      // 如果获取临时URL失败，返回原始URL
      return url;
    }
  }

  /// 判断是否为首页图片
  bool isHomeImage(RxImageModel imageObj) {
    return imageObj.isHomeImage.value;
  }

  /// 异步获取图片URL
  ///
  /// 符合Clean Architecture原则的异步URL获取方法
  /// 所有业务逻辑都在控制器层处理，UI层只负责显示
  ///
  /// [imageObj] 图片模型对象
  void asyncGetImageUrl(RxImageModel imageObj) {
    // 避免重复请求：检查是否正在重试或正在获取临时URL
    if (imageObj.isRetrying.value || imageObj.isGettingTurl) {
      return;
    }

    // 设置获取状态，防止并发请求
    imageObj.isGettingTurl = true;

    Future.microtask(() async {
      try {
        final String originalUrl = imageObj.url.value;
        if (originalUrl.isEmpty || imageObj.turl.value.isNotEmpty) {
          return;
        }

        // 通过UseCase获取临时URL
        final String newTurl = await getTurl(originalUrl);
        if (newTurl.isNotEmpty && newTurl != originalUrl) {
          // 更新临时URL
          imageObj.turl.value = newTurl;
        }
      } catch (e) {
        LogUtil.w('异步获取临时URL失败: $e');
      } finally {
        // 重置获取状态
        imageObj.isGettingTurl = false;
      }
    });
  }

  /// 处理图片加载错误
  ///
  /// 符合Clean Architecture原则的错误处理方法
  /// 将重试逻辑封装在控制器层，UI层只负责调用和显示结果
  ///
  /// [imageObj] 图片模型对象
  /// [size] 组件尺寸
  /// 返回错误处理后的Widget
  Widget handleImageLoadError(RxImageModel imageObj, double size) {
    // 检查是否可以重试（未达到重试次数限制且未在重试中）
    if (imageObj.retryCount < 1 && !imageObj.isRetrying.value) {
      LogUtil.d('开始自动重试图片加载');

      // 异步执行重试逻辑，避免阻塞UI
      Future.microtask(() async {
        final bool retrySuccess = await retryGetImageUrl(imageObj);

        if (retrySuccess) {
          LogUtil.d('重试成功，图片URL已更新');
          // 重试成功后，响应式的turl属性会自动触发UI重建
        } else {
          LogUtil.w('重试失败，显示错误占位符');
        }
      });

      // 在重试过程中显示加载指示器
      return _buildLoadingWidget(size);
    } else {
      // 已达到重试次数限制或正在重试中，显示错误占位符
      LogUtil.w('已达到重试次数限制或正在重试中，显示错误占位符');
      return _buildErrorWidget(size);
    }
  }

  /// 构建加载指示器Widget
  ///
  /// 私有方法，用于构建统一的加载指示器
  ///
  /// [size] 组件尺寸
  Widget _buildLoadingWidget(double size) {
    return Container(
      width: size,
      height: size,
      color: AppTheme.whiteColor,
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2.0,
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.grayColor),
        ),
      ),
    );
  }

  /// 构建错误占位符Widget
  ///
  /// 私有方法，用于构建统一的错误占位符
  ///
  /// [size] 组件尺寸
  Widget _buildErrorWidget(double size) {
    return Container(
      width: size,
      height: size,
      color: AppTheme.whiteColor,
      child: const Center(child: Icon(Icons.error_outline, color: AppTheme.grayColor, size: 24.0)),
    );
  }

  /// 重试获取图片URL
  ///
  /// 当图片加载失败时调用此方法进行重试，重新获取临时URL并更新图片对象
  ///
  /// 功能特点：
  /// - 检查重试次数限制，每个图片最多只能重试一次
  /// - 设置重试状态，在重试过程中显示加载指示器
  /// - 强制重新获取临时URL，绕过缓存机制
  /// - 提供完整的错误处理和日志记录
  /// - 确保重试失败时不会影响应用稳定性
  ///
  /// [imageObj] 需要重试的图片模型对象
  ///
  /// 返回值：
  /// - true: 重试成功，已更新新的临时URL
  /// - false: 重试失败或已达到重试次数限制
  ///
  /// 使用场景：
  /// - CachedNetworkImage 的 errorWidget 被触发时
  /// - 图片加载失败需要自动恢复时
  /// - 临时URL过期需要刷新时
  Future<bool> retryGetImageUrl(RxImageModel imageObj) async {
    // 检查重试次数限制，避免无限重试
    if (imageObj.retryCount >= 1) {
      LogUtil.w('图片重试次数已达上限，不再重试: ${imageObj.url.value}');
      return false;
    }

    // 获取原始URL
    final String? originalUrl = imageObj.url.value;
    if (originalUrl == null || originalUrl.isEmpty) {
      LogUtil.w('原始URL为空，无法进行重试');
      return false;
    }

    try {
      // 设置重试状态，触发UI更新显示加载指示器
      imageObj.isRetrying.value = true;
      imageObj.retryCount++;

      LogUtil.d('开始重试获取图片临时URL，重试次数: ${imageObj.retryCount}, 原始URL: $originalUrl');

      // 添加适当的延迟，避免立即重试造成服务器压力
      await Future.delayed(const Duration(milliseconds: 500));

      // 强制重新获取临时URL，清空现有的turl缓存
      imageObj.turl.value = '';

      // 调用 getTurl 获取新的临时URL
      final String newTurl = await getTurl(originalUrl);

      // 更新图片对象的临时URL，响应式属性会自动触发UI更新
      imageObj.turl.value = newTurl;

      LogUtil.d('重试成功，获取到新的临时URL: $newTurl');
      return true;
    } catch (e) {
      // 重试失败的错误处理
      LogUtil.e('重试获取临时URL失败: $e');
      return false;
    } finally {
      // 无论成功失败，都要重置重试状态
      imageObj.isRetrying.value = false;
    }
  }

  /// 获取图片边框颜色
  Color getImageBorderColor(RxImageModel imageObj) {
    return isHomeImage(imageObj) ? AppTheme.homeImageBorderColor : AppTheme.placeholderColor;
  }

  /// 获取图片边框宽度
  double getImageBorderWidth(RxImageModel imageObj) {
    return isHomeImage(imageObj) ? 3.0 : 1.0;
  }

  /// 获取图片边框圆角
  double getImageBorderRadius(RxImageModel imageObj) {
    return isHomeImage(imageObj) ? 1.0 : 2.0;
  }

  /// 获取图片内边框装饰
  BoxDecoration? getImageInnerDecoration(bool isHomeImage) {
    return isHomeImage ? BoxDecoration(border: Border.all(color: AppTheme.whiteColor, width: 1)) : null;
  }

  /// 图片项点击事件
  ///
  /// 当用户点击图片项时触发此事件
  ///
  /// 参数:
  /// - [entry] : 资产项包装器,包含图片相关数据
  /// - [index] : 被点击图片的索引
  ///
  /// 功能:
  /// - 导航到图片预览页面
  /// - 传递资产项和图片索引参数
  /// - 处理删除的图片URL，用于S3清理
  Future<void> onImageItemPressed(RxAssetItemWrapper entry, int index) async {
    final pageArguments = ImagePreviewPageArguments(
      pageTitle: entry.itemDisplayName ?? '',
      index: index,
      imageModels: imageUrls,
      isShowSetHomeImageBtn: _isShowSetHomeImageBtn(),
    );
    final ImagePreviewResult result = await navigationService.navigateTo(
      AutoRoutes.imagePreview,
      arguments: pageArguments,
    );

    // 处理删除的图片URL，传递给编辑控制器用于S3清理
    if (result.deletedImageUrls != null && result.deletedImageUrls!.isNotEmpty) {
      _handleDeletedImageUrls(result.deletedImageUrls!);
    }

    if (result.isHomeImageSet == true) {
      // 如果设置了主图像
      afCustomizeViewController.updateHomeImage(
        item: entry,
        isSetHomeImage: true,
        index: index,
        imageModels: result.imageModels,
      );
    } else if (result.isHomeImageSet == false) {
      // 如果取消了主图像
      afCustomizeViewController.resetImagesHomeStatus();
    } else {
      updateDefaultData(result.imageModels ?? []);
    }
  }

  /// 判断是否显示设置主图像按钮
  bool _isShowSetHomeImageBtn() => afCustomizeViewController.scene == AfCustomizeViewScene.assetDetail;

  /// 处理删除的图片URL列表
  ///
  /// 通过项目现有的EventBus发布图片删除事件，遵循Clean Architecture原则
  /// 实现组件间的解耦通信，不直接依赖特定的控制器
  ///
  /// [deletedUrls] 被删除的图片URL列表
  ///
  /// 功能特点：
  /// - 使用项目现有的EventBus基础设施
  /// - 实现发布-订阅解耦，提高组件复用性
  /// - 支持多个订阅者同时处理删除事件
  /// - 遵循Clean Architecture的依赖规则
  /// - 包含详细的日志记录用于调试
  void _handleDeletedImageUrls(List<String> deletedUrls) {
    try {
      // 创建图片删除事件
      final event = ImageDeletedEvent(
        deletedUrls,
        context: {
          'source': 'af_customize_image_item',
          'assetId': assetId,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      // 通过项目现有的EventBus发布事件
      EventBus.fire(event);

      LogUtil.d('已发布图片删除事件: ${deletedUrls.length}个URL');
      LogUtil.d('删除的URL列表: ${deletedUrls.join(', ')}');
    } catch (e) {
      // 事件发布失败时记录错误，但不影响正常流程
      LogUtil.e('发布图片删除事件失败: $e');
      LogUtil.d('删除的URL列表: ${deletedUrls.join(', ')}');
    }
  }
}

import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/button_state.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/time_display_result.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/enums/date_type_enum.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_time_display_dialog_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';

/// 时间选择器控制器
class AfTimeDisplayController extends GetxController {
  // 可观察变量
  final Rx<DateTime?> selectedDate = Rx<DateTime?>(null);
  final Rx<DateTime?> currentDisplayMonth = Rx<DateTime?>(null); // 当前显示的月份
  final RxString dateExample = ''.obs;
  final RxBool isTimePickerOpen = false.obs;

  // 添加一个全局键用于定位时间显示组件
  final GlobalKey timeDisplayKey = GlobalKey();

  // 配置项
  late DateType dateType;
  String? maxDate;
  String? minDate;
  String? lastDate;

  // 添加时间选择器相关参数
  final RxDouble timePickerWidth = 150.0.obs;
  final RxDouble timePickerHeight = 180.0.obs;

  /// 初始化控制器
  void initialize({String? initialDate, String? maxDate, String? minDate, DateType dateType = DateType.date}) {
    this.dateType = dateType;
    this.maxDate = maxDate;
    this.minDate = minDate;

    if (initialDate != null && initialDate.isNotEmpty) {
      dateExample.value = initialDate;
      _parseInitialDate();
    } else {
      selectedDate.value = DateTime.now();
    }

    // 初始化当前显示月份为选中日期的月份
    currentDisplayMonth.value = selectedDate.value;
  }

  /// 解析初始日期
  void _parseInitialDate() {
    if (dateExample.isEmpty) {
      final now = DateTime.now();
      selectedDate.value = now;
      dateExample.value = _formatDateToString(now);
    } else {
      try {
        String dateStr = dateExample.value.replaceAll('/', '-');
        if (!dateStr.contains(':')) {
          dateStr += ' 00:00';
        }
        selectedDate.value = DateFormat('yyyy-MM-dd HH:mm').parse(dateStr);
      } catch (e) {
        LogUtil.d('Invalid date format: $e');
        selectedDate.value = DateTime.now();
        dateExample.value = _formatDateToString(selectedDate.value!);
      }
    }
  }

  /// 格式化日期为字符串
  String _formatDateToString(DateTime date) {
    final year = date.year.toString();
    final month = date.month.toString().padLeft(2, '0');
    final day = date.day.toString().padLeft(2, '0');
    final hours = date.hour.toString().padLeft(2, '0');
    final minutes = date.minute.toString().padLeft(2, '0');

    if (dateType == DateType.date) {
      return '$year/$month/$day';
    } else {
      return '$year/$month/$day $hours:$minutes';
    }
  }

  /// 获取日期信息
  Map<String, String> _getDateInfo(DateTime date) {
    return {
      'yearValues': date.year.toString(),
      'monthValues': date.month.toString().padLeft(2, '0'),
      'dayValues': date.day.toString().padLeft(2, '0'),
      'hourValues': date.hour.toString().padLeft(2, '0'),
      'minuteValues': date.minute.toString().padLeft(2, '0'),
    };
  }

  /// 切换日期选择器模态框
  void switchDateModal(bool isOpen) {
    isTimePickerOpen.value = isOpen;
    if (isOpen) {
      lastDate = dateExample.value;
    }
  }

  /// 日期变更处理
  void onNameChange(DateTime date) {
    selectedDate.value = date;
    // 更新当前显示的月份
    currentDisplayMonth.value = date;
    dateExample.value = _formatDateToString(date);
  }

  /// 更新小时
  void updateHour(int hour) {
    if (selectedDate.value != null) {
      selectedDate.value = DateTime(
        selectedDate.value!.year,
        selectedDate.value!.month,
        selectedDate.value!.day,
        hour,
        selectedDate.value!.minute,
      );
      dateExample.value = _formatDateToString(selectedDate.value!);
    }
  }

  /// 更新分钟
  void updateMinute(int minute) {
    if (selectedDate.value != null) {
      selectedDate.value = DateTime(
        selectedDate.value!.year,
        selectedDate.value!.month,
        selectedDate.value!.day,
        selectedDate.value!.hour,
        minute,
      );
      dateExample.value = _formatDateToString(selectedDate.value!);
    }
  }

  /// 按钮点击事件处理
  TimeDisplayResult getTimeDate(ButtonState buttonState) {
    isTimePickerOpen.value = false;

    Map<String, String> pickdatetime = {
      'yearValues': '',
      'monthValues': '',
      'dayValues': '',
      'hourValues': '',
      'minuteValues': '',
    };

    ButtonState valueAssa;
    String? resultDate;

    switch (buttonState) {
      case ButtonState.today:
        final now = DateTime.now();
        selectedDate.value = now;
        resultDate = _formatDateToString(now);
        dateExample.value = resultDate;
        pickdatetime = _getDateInfo(now);
        valueAssa = ButtonState.today;
        break;

      case ButtonState.clear:
        dateExample.value = '';
        resultDate = null;
        valueAssa = ButtonState.clear;
        break;

      case ButtonState.finished:
        if (selectedDate.value != null) {
          resultDate = _formatDateToString(selectedDate.value!);
          dateExample.value = resultDate;
          pickdatetime = _getDateInfo(selectedDate.value!);
        } else {
          resultDate = null;
        }
        valueAssa = ButtonState.finished;
        break;

      case ButtonState.cancel:
        dateExample.value = lastDate ?? '';
        valueAssa = ButtonState.cancel;
        resultDate = lastDate;

        if (lastDate != null && lastDate!.isNotEmpty) {
          try {
            String dateStr = lastDate!.replaceAll('/', '-');
            if (!dateStr.contains(':')) {
              dateStr += ' 00:00';
            }
            final DateTime parsedDate = DateFormat('yyyy-MM-dd HH:mm').parse(dateStr);
            pickdatetime = _getDateInfo(parsedDate);
          } catch (e) {
            if (selectedDate.value != null) {
              pickdatetime = _getDateInfo(selectedDate.value!);
            } else {
              final now = DateTime.now();
              pickdatetime = _getDateInfo(now);
            }
          }
        } else if (selectedDate.value != null) {
          pickdatetime = _getDateInfo(selectedDate.value!);
        }
        break;
    }

    return TimeDisplayResult(valueState: valueAssa, valueTime: pickdatetime, formattedDate: resultDate);
  }

  /// 获取十年后的最大日期
  String getFinalMomentInTenYears() {
    final currentDate = DateTime.now();
    final futureYear = currentDate.year + 10;
    final futureDate = DateTime(futureYear + 1, 1, 0, 23, 59, 59);
    return '${futureYear}-12-${futureDate.day}';
  }

  /// 判断是否为空值
  bool isEmptyValue() {
    return dateExample.value == 'valueBlank';
  }

  /// 判断是否为普通日期
  bool isNormalDate() {
    return dateExample.value.length >= 10 && dateExample.value != 'valueBlank';
  }

  /// 判断是否为"现在"值
  bool isNowValue() {
    return dateExample.value == 'now' || dateExample.value == '<now>' || dateExample.value == '現在';
  }

  /// 判断是否为"今日"值
  bool isTodayValue() {
    return dateExample.value == 'today' || dateExample.value == '<today>' || dateExample.value == '今日';
  }

  /// 判断两个日期是否为同一天
  bool isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  /// 返回两个数中的较小值
  int min(int a, int b) {
    return a < b ? a : b;
  }

  /// 返回两个数中的较大值
  int max(int a, int b) {
    return a > b ? a : b;
  }

  /// 切换到上个月
  void previousMonth() {
    final currentDate = currentDisplayMonth.value ?? selectedDate.value ?? DateTime.now();
    final DateTime minDate = this.minDate != null ? DateFormat('yyyy-MM-dd').parse(this.minDate!) : DateTime(1900);

    final previousMonth = DateTime(currentDate.year, currentDate.month - 1, 1);
    if (previousMonth.isAfter(minDate) ||
        (previousMonth.year == minDate.year && previousMonth.month == minDate.month)) {
      final int lastDayOfPrevMonth = DateTime(currentDate.year, currentDate.month, 0).day;
      final newDay = min(currentDate.day, lastDayOfPrevMonth);
      // 只更新显示的月份，不更新选中状态
      currentDisplayMonth.value = DateTime(
        currentDate.year,
        currentDate.month - 1,
        newDay,
        currentDate.hour,
        currentDate.minute,
      );
    }
  }

  /// 切换到下个月
  void nextMonth() {
    final currentDate = currentDisplayMonth.value ?? selectedDate.value ?? DateTime.now();
    final DateTime maxDate = this.maxDate != null
        ? DateFormat('yyyy-MM-dd').parse(this.maxDate!)
        : DateTime(DateTime.now().year + 10, 12, 31);

    final nextMonth = DateTime(currentDate.year, currentDate.month + 1, 1);
    if (nextMonth.isBefore(maxDate) || (nextMonth.year == maxDate.year && nextMonth.month == maxDate.month)) {
      final int lastDayOfNextMonth = DateTime(currentDate.year, currentDate.month + 2, 0).day;
      final newDay = min(currentDate.day, lastDayOfNextMonth);
      // 只更新显示的月份，不更新选中状态
      currentDisplayMonth.value = DateTime(
        currentDate.year,
        currentDate.month + 1,
        newDay,
        currentDate.hour,
        currentDate.minute,
      );
    }
  }

  /// 切换到选择的年月
  void setYearMonth(int year, int month) {
    final currentDate = currentDisplayMonth.value ?? selectedDate.value ?? DateTime.now();
    // 只更新显示的月份
    currentDisplayMonth.value = DateTime(
      year,
      month,
      min(currentDate.day, DateTime(year, month + 1, 0).day),
      currentDate.hour,
      currentDate.minute,
    );
  }

  /// 构建年月选择器
  Widget buildYearMonthPicker(BuildContext context) {
    /// 基准线日期
    final DateTime baseLineDate = DateTime.now();
    // 获取当前选中日期或默认为今天
    final DateTime currentDate = currentDisplayMonth.value ?? selectedDate.value ?? DateTime.now();

    // 最小和最大日期
    final DateTime minDate = this.minDate != null ? DateFormat('yyyy-MM-dd').parse(this.minDate!) : DateTime(1900);
    final DateTime maxDate = this.maxDate != null
        ? DateFormat('yyyy-MM-dd').parse(this.maxDate!)
        : DateTime(DateTime.now().year + 10, 12, 31);

    // 获取可选择的年份范围（限制范围以减少内存使用）
    final int minYear = max(minDate.year, baseLineDate.year - 10);
    final int maxYear = min(maxDate.year, baseLineDate.year + 10);
    final List<int> years = List.generate(maxYear - minYear + 1, (index) => minYear + index);

    // 月份列表
    final List<int> months = List.generate(12, (index) => index + 1);

    // 预设初始选中的年月
    int selectedYear = currentDate.year;
    int selectedMonth = currentDate.month;

    // 创建固定的控制器，防止重建导致的跳跃
    final yearController = FixedExtentScrollController(initialItem: years.indexOf(selectedYear));

    final monthController = FixedExtentScrollController(initialItem: selectedMonth - 1);

    return Container(
      padding: const EdgeInsets.only(top: 0),
      child: Stack(
        children: [
          // 简化的选中项高亮背景
          Positioned.fill(
            child: Center(
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          Row(
            children: [
              // 年份选择器
              Expanded(
                child: ListWheelScrollView.useDelegate(
                  itemExtent: 40,
                  diameterRatio: 1.7,
                  perspective: 0.002,
                  physics: const FixedExtentScrollPhysics(),
                  controller: yearController,
                  onSelectedItemChanged: (index) {
                    if (index >= 0 && index < years.length) {
                      final newYear = years[index];
                      selectedYear = newYear;
                      // 更新显示的月份
                      final int lastDayOfMonth = DateTime(newYear, selectedMonth + 1, 0).day;
                      final newDay = min(currentDate.day, lastDayOfMonth);
                      currentDisplayMonth.value = DateTime(
                        newYear,
                        selectedMonth,
                        newDay,
                        currentDate.hour,
                        currentDate.minute,
                      );
                    }
                  },
                  childDelegate: ListWheelChildBuilderDelegate(
                    childCount: years.length,
                    builder: (context, index) {
                      final year = years[index];
                      final isSelected = index == yearController.selectedItem;
                      return Center(
                        child: Text(
                          '${year}年',
                          style: TextStyle(
                            fontSize: isSelected ? 18 : 16,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            color: isSelected ? AppTheme.darkBlueColor : Colors.black,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),

              // 月份选择器
              Expanded(
                child: ListWheelScrollView.useDelegate(
                  itemExtent: 40,
                  diameterRatio: 1.7,
                  perspective: 0.002,
                  physics: const FixedExtentScrollPhysics(),
                  controller: monthController,
                  onSelectedItemChanged: (index) {
                    if (index >= 0 && index < months.length) {
                      final newMonth = months[index];
                      selectedMonth = newMonth;
                      // 更新显示的月份
                      final int lastDayOfMonth = DateTime(selectedYear, newMonth + 1, 0).day;
                      final newDay = min(currentDate.day, lastDayOfMonth);
                      currentDisplayMonth.value = DateTime(
                        selectedYear,
                        newMonth,
                        newDay,
                        currentDate.hour,
                        currentDate.minute,
                      );
                    }
                  },
                  childDelegate: ListWheelChildBuilderDelegate(
                    childCount: months.length,
                    builder: (context, index) {
                      final month = months[index];
                      final isSelected = index == monthController.selectedItem;
                      return Center(
                        child: Text(
                          '${month}月',
                          style: TextStyle(
                            fontSize: isSelected ? 18 : 16,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            color: isSelected ? AppTheme.darkBlueColor : Colors.black,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建时间选择器
  Widget buildTimeSelector(bool isHour, int currentValue, int minValue, int maxValue) {
    final scrollController = FixedExtentScrollController(initialItem: currentValue - minValue);

    return Expanded(
      child: SizedBox(
        width: 60,
        child: Stack(
          children: [
            // 简化的选中项高亮背景
            Positioned.fill(
              child: Center(
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            ListWheelScrollView.useDelegate(
              itemExtent: 40,
              perspective: 0.002,
              diameterRatio: 1.7,
              physics: const FixedExtentScrollPhysics(),
              controller: scrollController,
              onSelectedItemChanged: (index) {
                final value = minValue + index;
                if (isHour) {
                  updateHour(value);
                } else {
                  updateMinute(value);
                }
              },
              childDelegate: ListWheelChildBuilderDelegate(
                childCount: maxValue - minValue + 1,
                builder: (context, index) {
                  final value = minValue + index;
                  final isSelected = index == scrollController.selectedItem;
                  return Container(
                    alignment: Alignment.center,
                    child: Text(
                      value.toString().padLeft(2, '0'),
                      style: TextStyle(
                        fontSize: isSelected ? 18 : 16,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        color: isSelected ? AppTheme.darkBlueColor : Colors.black,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示时间选择器弹出层
  Future<void> showTimePickerPopup(BuildContext context) async {
    // 获取当前选中时间
    final DateTime now = selectedDate.value ?? DateTime.now();
    final int currentHour = now.hour;
    final int currentMinute = now.minute;

    // 获取RenderBox来计算位置
    RenderBox? renderBox;
    Offset position = const Offset(0, 0);

    // 尝试使用全局键获取位置
    if (timeDisplayKey.currentContext != null) {
      renderBox = timeDisplayKey.currentContext!.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        position = renderBox.localToGlobal(Offset.zero);
      }
    } else {
      // 备用方案：使用当前上下文
      renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        position = renderBox.localToGlobal(Offset.zero);
      }
    }

    // 计算弹出框的位置和大小
    final Size size = MediaQuery.of(context).size;
    final double popupWidth = timePickerWidth.value;

    // 计算弹窗位置，确保不会超出屏幕边界
    double left = position.dx;
    if (left + popupWidth > size.width) {
      left = size.width - popupWidth - 10;
    }

    // 显示时间选择器弹出层
    await showDialog(
      context: context,
      barrierColor: Colors.transparent, // 透明背景
      builder: (BuildContext context) {
        return _buildTimePickerDialog(context, position, left, currentHour, currentMinute);
      },
    );
  }

  /// 构建时间选择器对话框
  Widget _buildTimePickerDialog(
    BuildContext context,
    Offset position,
    double left,
    int currentHour,
    int currentMinute,
  ) {
    return Stack(
      children: [
        // 点击空白区域关闭弹窗
        Positioned.fill(
          child: GestureDetector(
            onTap: () {
              Get.back();
            },
            child: Container(color: Colors.transparent),
          ),
        ),
        // 时间选择器弹窗
        Positioned(
          top: position.dy, // 在时间显示下方显示
          left: left,
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: timePickerWidth.value,
              height: timePickerHeight.value,
              decoration: BoxDecoration(
                color: AppTheme.whiteColor,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 4)),
                ],
              ),
              child: Stack(
                children: [
                  // 半透明高亮当前选择项的背景条
                  Positioned.fill(
                    top: 0,
                    bottom: 0,
                    child: Center(
                      child: Container(
                        height: 40,
                        decoration: const BoxDecoration(color: AppTheme.timePickerHighlightBackgroundColor),
                      ),
                    ),
                  ),
                  // 时间选择器
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 小时选择器
                        SizedBox(
                          width: 50,
                          child: ListWheelScrollView.useDelegate(
                            itemExtent: 40,
                            diameterRatio: 1.7,
                            perspective: 0.005,
                            physics: const FixedExtentScrollPhysics(),
                            controller: FixedExtentScrollController(initialItem: currentHour),
                            onSelectedItemChanged: (index) {
                              updateHour(index);
                            },
                            childDelegate: ListWheelChildBuilderDelegate(
                              childCount: 24,
                              builder: (context, index) {
                                return Center(
                                  child: Text(
                                    index.toString().padLeft(2, '0'),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.normal,
                                      color: AppTheme.timePickerNormalTextColor,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        const Padding(
                          padding: EdgeInsets.symmetric(horizontal: 6.0),
                          child: Text(':', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                        ),
                        // 分钟选择器
                        SizedBox(
                          width: 50,
                          child: ListWheelScrollView.useDelegate(
                            itemExtent: 40,
                            diameterRatio: 1.7,
                            perspective: 0.005,
                            physics: const FixedExtentScrollPhysics(),
                            controller: FixedExtentScrollController(initialItem: currentMinute),
                            onSelectedItemChanged: (index) {
                              updateMinute(index);
                            },
                            childDelegate: ListWheelChildBuilderDelegate(
                              childCount: 60,
                              builder: (context, index) {
                                return Center(
                                  child: Text(
                                    index.toString().padLeft(2, '0'),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.normal,
                                      color: AppTheme.timePickerNormalTextColor,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 显示日期选择对话框
  Future<void> showDatePicker(
    BuildContext context,
    Function(ButtonState valueState, Map<String, String> valueTime, String? formattedDate) onTimeSelected,
  ) async {
    final result = await AfTimeDisplayDialog.show(
      context: context,
      initialDate: dateExample.value,
      maxDate: maxDate,
      minDate: minDate,
      dateType: dateType,
    );

    if (result != null) {
      dateExample.value = result.formattedDate ?? '';
      onTimeSelected(result.valueState, result.valueTime, result.formattedDate);
    }
  }
}

/// 时间选择器对话框
class AfTimeDisplayDialog {
  /// 显示时间选择对话框
  static Future<TimeDisplayResult?> show({
    required BuildContext context,
    String? initialDate,
    String? maxDate,
    String? minDate,
    DateType dateType = DateType.date,
  }) async {
    final controller = Get.find<AfTimeDisplayController>();
    controller.initialize(initialDate: initialDate, maxDate: maxDate, minDate: minDate, dateType: dateType);

    final result = await showDialog<TimeDisplayResult>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          backgroundColor: const Color(0xFFF4F5F8),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.7,
              maxWidth: MediaQuery.of(context).size.width * 0.9,
            ),
            child: const AfTimeDisplayDialogView(),
          ),
        );
      },
    );

    return result;
  }
}

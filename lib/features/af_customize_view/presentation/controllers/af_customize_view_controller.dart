import 'dart:convert';

import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/adapters/asset_item_adapter.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_image_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/enums/schedule_item_ids.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/calc_asset_dict_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/check_validate_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/prepare_asset_data_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_image_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_customize_logic_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:get/get.dart';

/// 首页图片自动设置标志值
const String _AUTO_SET_HOME_IMAGE_FLAG = '1';

/// Appointment字段类型枚举
enum AppointmentFieldType {
  /// 基本预约字段（直接映射到appointment对象的属性）
  basic,

  /// extraCommonTextObj字段（存储在extraCommonTextObj中）
  extraCommon,

  /// reservationTextObj字段（存储在reservationTextObj中）
  reservation,

  /// 未知类型
  unknown,
}

/// 资产自定义视图控制器
///
/// 负责管理资产自定义视图的数据和状态
/// 包括资产字典、图片控制器、数据验证等功能
class AfCustomizeViewController extends GetxController {
  /// 资产字典，存储所有资产项目
  /// 键为资产分类ID，值为该分类下的资产项目列表
  final Rx<Map<String, List<RxAssetItemWrapper>>> _assetDict = Rx<Map<String, List<RxAssetItemWrapper>>>({});

  /// 获取资产字典
  Map<String, List<RxAssetItemWrapper>> get assetDict => _assetDict.value;

  /// 设置资产字典
  set assetDict(Map<String, List<RxAssetItemWrapper>> dict) {
    _assetDict.value = dict;
  }

  /// 是否为终日
  /// 在日程场景中使用，标记当前是否为全天事件
  final RxBool isUnitDay = false.obs;

  /// 图片控制器列表
  /// 存储所有的图片控制器实例，用于统一管理图片相关操作
  final RxList<AfCustomizeImageItemController> imageControllers = <AfCustomizeImageItemController>[].obs;

  /// 准备资产数据用例
  /// 用于初始化和准备资产数据
  final PrepareAssetDataUseCase prepareAssetDataUseCase;

  /// 计算资产字典用例
  /// 用于处理资产项目间的计算关系
  final CalcAssetDictUseCase calcAssetDictUseCase;

  /// 检查验证用例
  /// 用于验证资产项目的有效性
  final CheckValidateUseCase checkValidateUseCase;

  /// 预览模式标志
  /// 预览模式下所有条目不可编辑
  final previewFlg = false.obs;

  /// Talk url 即时通讯链接
  /// 用于实时通信功能
  final RxString liveTalkUrl = ''.obs;

  /// 当前场景
  /// 决定视图的行为和显示方式
  AfCustomizeViewScene scene = AfCustomizeViewScene.assetDetail;

  /// 是否显示默认值
  /// 控制是否显示主数据的默认值
  bool isShowMasterDefaultValue = false;

  /// 计算资产字典的结果
  ///
  /// 存储从 [CalcAssetDictUseCase] 获取的结果。
  /// 包含以下内容:
  /// - [itemMap] 存储所有项目的映射,key为项目名称,value为对应的项目包装器
  /// - [calculateItems] 存储所有计算类型的项目列表
  CalcAssetDictResult? calcAssetDictResult;

  /// 是否需要底部导航栏
  ///
  /// 主要用于关联资产页面跳转时控制底部导航栏的显示
  bool? needNavigationBar;

  /// 是否需要进入时校验
  /// 控制页面加载时是否自动进行数据验证
  bool needEnterValidate = true;

  /// 日程事件列表
  /// 在日程场景中使用，存储相关的日程事件数据
  AppointmentListResponse? scheduleEventList;

  /// 预约列表项
  /// 存储当前处理的预约信息
  Appointment? appointment;

  /// 事件类型列表
  final RxList<EventTypeItem> eventTypeItems = <EventTypeItem>[].obs;

  final NavigationService navigationService;

  /// JS执行器
  final JsExecutor jsExecutor;

  /// CustomizeLogic
  GetCustomizeLogicResult? customizeLogicJs;

  /// Js执行器上下文
  JsExecutorContext jsExecutorContext;

  /// JavaScript 执行器实例
  /// 用于执行自定义逻辑 JavaScript 代码
  /// 采用单例模式，避免频繁创建和初始化
  /// 构造函数
  ///
  /// 参数:
  /// * [prepareAssetDataUseCase] - 准备资产数据用例
  /// * [calcAssetDictUseCase] - 计算资产字典用例
  /// * [checkValidateUseCase] - 检查验证用例
  AfCustomizeViewController({
    required this.prepareAssetDataUseCase,
    required this.calcAssetDictUseCase,
    required this.checkValidateUseCase,
    required this.navigationService,
    required this.jsExecutor,
    required this.jsExecutorContext,
  }) {
    // 监听预览模式变化，通知所有子控制器更新可编辑状态
    ever(previewFlg, (_) => _notifyEditableStateChanged());
  }

  /// 通知所有子控制器更新可编辑状态
  void _notifyEditableStateChanged() {
    // 每个子控制器都会通过 ever 监听器自动响应 previewFlg 的变化
    // 这里只需要触发资产字典的更新，确保UI重新构建
    _assetDict.refresh();
  }

  /// 更新首页图片(home画像)
  /// 图片状态需要提前在调用该函数之前设置好
  ///
  /// 处理首页图片的设置和自动设置逻辑:
  /// 1. 检查是否为图片类型
  /// 2. 处理手动设置首页图片的情况，重置其他图片状态
  /// 3. 根据自动设置标志处理图片状态，如果开启了自动设置首页图片，则重置其他图片状态
  ///
  /// 参数:
  /// * [item] 当前图片项包装器
  /// * [isSetHomeImage] 是否手动设置为首页图片
  /// * [index] 图片在列表中的索引需要更新为 home 画像的图片索引
  /// * [imageModels] 可选的图片模型列表，用于指定要操作的图片集合
  ///
  /// 返回值:
  /// * [bool] 是否成功更新了首页图片
  bool updateHomeImage({
    required RxAssetItemWrapper item,
    bool isSetHomeImage = false,
    int? index,
    List<RxImageModel>? imageModels,
  }) {
    // 仅处理图片类型的项目
    if (!SharedItemTypeEnum.image.equals(item.itemType)) return false;

    // 重置其他图片状态并设置新的首页图片
    void setHomeImage(int imageIndex) {
      resetImagesHomeStatus();
      _setImageAsHomeImage(item, imageIndex, imageModels ?? []);
    }

    // 手动设置首页图片
    if (isSetHomeImage && index != null) {
      setHomeImage(index);
      return true;
    }

    // 不是自动设置首页图片,直接返回
    if (item.optionObject?.mainImageAutoSetFlg != _AUTO_SET_HOME_IMAGE_FLAG) return false;

    // 已存在首页图片，不进行自动设置
    if (_checkImageHomeStatus(item)) {
      return false;
    }

    // 设置新的首页图片
    setHomeImage(index ?? 0);
    return true;
  }

  /// 检查所有资产字典中是否存在已设置为首页图片的项目
  ///
  /// 遍历资产字典中的所有项目,检查每个图片类型项目的 defaultData 中是否有
  /// isHomeImage 为 true 的图片。
  /// 并且避免重置当前传入的 item 项目的 home 画像。
  ///
  /// 参数:
  /// * [item] - 当前检查的资产项包装器
  ///
  /// 返回值:
  /// * true - 存在已设置为首页的图片
  /// * false - 不存在已设置为首页的图片
  bool _checkImageHomeStatus(RxAssetItemWrapper item) {
    for (var items in assetDict.values) {
      for (var tItem in items) {
        if (_shouldSkipItem(tItem)) continue;
        // 跳过当前项目
        if (tItem == item) continue;

        final imageUrls = tItem.imageModels;
        for (var imageModel in imageUrls) {
          if (imageModel.isHomeImage.value) {
            return true;
          }
        }
      }
    }
    return false;
  }

  /// 重置所有图片的首页状态
  ///
  /// 遍历资产字典中的所有项目,重置每个图片类型项目中所有图片的首页状态。
  ///
  /// 处理流程:
  /// 1. 遍历资产字典中的所有项目组
  /// 2. 遍历每个项目组中的所有项目
  /// 3. 跳过非图片类型的项目
  /// 4. 重置图片项目中所有图片的首页状态
  /// 5. 更新所有图片控制器的图片URL
  void resetImagesHomeStatus() {
    for (var items in assetDict.values) {
      for (var item in items) {
        if (_shouldSkipItem(item)) continue;
        _resetImageHomeStatus(item);
      }
    }
    for (var myControlelr in imageControllers) {
      myControlelr.updateImageUrls();
    }
  }

  /// 判断是否应该跳过处理该项
  ///
  /// 在重置首页图片状态时,需要跳过以下两种情况:
  /// 1. 非图片类型的项 - 只处理图片类型的项
  ///
  /// 参数说明:
  /// * [item] - 待判断的项目
  ///
  /// 返回值:
  /// * true - 表示应该跳过该项
  /// * false - 表示需要处理该项
  bool _shouldSkipItem(RxAssetItemWrapper item) {
    return !SharedItemTypeEnum.image.equals(item.itemType);
  }

  /// 重置单个图片项的首页状态
  ///
  /// 将指定图片项中所有图片的首页状态重置为 false
  ///
  /// 参数说明:
  /// * [item] - 需要重置状态的图片项包装器对象
  ///
  /// 处理流程:
  /// 1. 获取图片列表数据
  ///   - 如果是字符串类型,则初始化为空列表
  ///   - 如果是 null,则初始化为空列表
  ///   - 否则使用原始列表数据
  /// 2. 遍历图片列表
  ///   - 将每个图片的 isHomeImage 属性设置为 false
  /// 3. 更新模型数据
  ///   - 调用 updateDefaultData 方法更新图片列表
  void _resetImageHomeStatus(RxAssetItemWrapper item) {
    final imageUrls = item.imageModels;
    // 使用 forEach 替代 for 循环,更简洁
    imageUrls.forEach((url) {
      url.isHomeImage.value = false;
    });
    item.updateDefaultData(imageUrls);
  }

  /// 更新资产字典
  ///
  /// 将新的资产数据转换为 RxAssetItemWrapper 对象并存储到资产字典中
  /// 然后准备资产数据、计算资产字典并进行验证
  ///
  /// 参数:
  /// * [newDict] - 新的资产字典数据
  ///
  /// 处理流程:
  /// 1. 将新字典中的项目转换为 RxAssetItemWrapper 对象
  /// 2. 调用 prepareAssetDataUseCase 准备资产数据
  /// 3. 计算资产字典
  /// 4. 如果需要，进行数据验证
  Future<void> updateAssetDict(Map<String, List<dynamic>> newDict) async {
    // 转换并更新响应式Map
    final convertedDict = newDict.map(
      (key, items) => MapEntry(
        key,
        items.map((item) {
          if (item is AssetItemListModel) {
            // asset 模型
            return RxAssetItemWrapper.fromAssetItem(item);
          } else if (item is ReservationItemCommon) {
            // schedule 模型
            return RxAssetItemWrapper.fromReservationItem(item);
          } else if (item is EventTypeItem) {
            // schedule event 模型
            return RxAssetItemWrapper.fromEventTypeItem(item);
          } else if (item is SharedLayoutSetting) {
            // 履历情报 模型
            return RxAssetItemWrapper.fromSharedLayoutSetting(item);
          } else {
            // 创建适配器处理其他类型
            final adapter = item is AssetItemAdapter ? item : AssetItemAdapter(item as AssetItemListModel);
            return RxAssetItemWrapper(adapter);
          }
        }).toList(),
      ),
    );

    // 更新响应式字典
    _assetDict.value = convertedDict;

    await prepareAssetDataUseCase(
      PrepareAssetDataParams(assetDict: assetDict, scene: scene, isShowMasterDefaultValue: isShowMasterDefaultValue),
    );

    // 计算资产计算项目
    calcAssetDictResult = await calcAssetDict();

    // 更新 JavaScript 执行器的数据字典
    await updateJsExecutorData();

    LogUtil.d('执行自定义逻辑 JavaScript:af_customize_view_controller');
    // 执行自定义逻辑 JavaScript（页面绘制刷新时机）
    await executeCustomizeLogicJs();

    if (needEnterValidate) {
      await validateAndShowErrors();
    }
  }

  /// 验证所有资产项目
  ///
  /// 遍历资产字典中的所有项目并进行验证
  /// 如果有错误，会显示错误提示
  /// 并且同时展示项目下方红色提示文言
  ///
  /// 返回值:
  /// * [List<CheckValidateResult>] - 错误项目结果列表
  Future<List<CheckValidateResult>> validateAllItems() async {
    final List<CheckValidateResult> errorItems = [];

    for (var entries in assetDict.values) {
      for (var item in entries) {
        final result = await checkValidateUseCase(CheckValidateParams(entry: item, scene: scene));
        if (result.showAlert) {
          errorItems.add(result);
        }
      }
    }

    return errorItems;
  }

  /// 更新当前场景
  ///
  /// 参数:
  /// * [scene] - 新的场景值
  void updateScene(AfCustomizeViewScene scene) {
    this.scene = scene;
  }

  /// 更新是否显示主数据默认值的设置
  ///
  /// 参数:
  /// * [showDefaultValue] - 是否显示默认值
  void updateMaster(bool showDefaultValue) {
    isShowMasterDefaultValue = showDefaultValue;
  }

  /// 当非计算项目更新时，触发相关计算项目的数据更新
  ///
  /// 参数:
  /// * [itemName] - 更新项目的名称,用于查找依赖该项目的计算项目
  void updateCalculateItems(String itemName) {
    // 如果计算结果为空，直接返回
    if (calcAssetDictResult == null) return;

    // 使用 UseCase 更新计算项目
    calcAssetDictUseCase(CalcAssetDictParams.calcObj(calcAssetDictResult, itemName));
  }

  /// 重新计算计算项目
  ///
  /// 参数:
  /// - [itemName] 需要重新计算的资产项目名称
  ///
  /// 说明:
  /// 1. 检查项目名称是否为空
  /// 2. 如果项目名称不为空,则调用 [updateCalculateItems] 方法重新计算相关的计算项目
  /// 3. 计算项目的值会被更新到对应的资产项目中
  /// 4. 如果计算失败,计算项目的值会被设置为空字符串
  void recalculateItems(String? itemName) {
    if (itemName != null) {
      updateCalculateItems(itemName);
    }
  }

  /// 注册一个新的图片控制器
  ///
  /// 将图片控制器添加到控制器列表中，以便统一管理
  ///
  /// 参数:
  /// * [controller] - 要注册的图片控制器
  void registerImageController(AfCustomizeImageItemController controller) {
    if (!imageControllers.contains(controller)) {
      imageControllers.add(controller);
    }
  }

  /// 注销一个图片控制器
  ///
  /// 从控制器列表中移除指定的图片控制器
  ///
  /// 参数:
  /// * [controller] - 要注销的图片控制器
  void unregisterImageController(AfCustomizeImageItemController controller) {
    imageControllers.remove(controller);
  }

  /// 获取所有已注册的图片控制器
  ///
  /// 返回值:
  /// * [List<AfCustomizeImageItemController>] - 图片控制器列表
  List<AfCustomizeImageItemController> getImageControllers() {
    return imageControllers.toList();
  }

  /// 根据项目ID查找条目
  RxAssetItemWrapper? findEntryByItemId(String itemId) {
    for (final entries in assetDict.values) {
      for (final entry in entries) {
        if (entry.itemId == itemId) {
          return entry;
        }
      }
    }
    return null;
  }

  /// 控制器关闭时的清理工作
  ///
  /// 清理所有图片控制器、资产字典和 JsExecutor 实例
  @override
  void onClose() {
    // 清理所有图片控制器
    imageControllers.clear();
    // 销毁资产字典结果对象
    assetDict.clear();
    // 清理 JsExecutor 实例和桥接服务的控制器引用
    jsExecutor.dispose();
    super.onClose();
    LogUtil.d('AfCustomizeViewController: 资源清理完成');
  }

  /// 计算资产字典
  ///
  /// 参数:
  /// * [assetDict] - 资产字典,键为资产ID,值为资产项目列表
  ///
  /// 返回值:
  /// * [CalcAssetDictResult] - 资产字典计算结果
  ///
  /// 说明:
  /// 1. 调用 [calcAssetDictUseCase] 用例计算资产字典
  /// 2. 将资产字典作为参数传入用例
  /// 3. 返回计算结果
  Future<CalcAssetDictResult> calcAssetDict() async {
    return await calcAssetDictUseCase(CalcAssetDictParams(assetDict));
  }

  /// 将指定索引的图片设置为首页图片
  ///
  /// 参数:
  /// * [item] - 要设置的资产项包装器
  /// * [index] - 要设置为首页图片的索引
  /// * [imageModels] - 图片模型列表
  ///
  /// 说明:
  /// 1. 获取资产项中的图片列表
  /// 2. 将所有图片的 isHomeImage 属性设置为 false
  /// 3. 将指定索引的图片 isHomeImage 属性设置为 true
  /// 4. 更新资产项的默认数据
  void _setImageAsHomeImage(RxAssetItemWrapper item, int index, List<RxImageModel> imageModels) {
    final images = imageModels;
    for (var i = 0; i < images.length; i++) {
      images[i].isHomeImage.value = i == index;
    }
    item.updateDefaultData(images);
    item.imageModels.refresh();
  }

  /// 校验所有项目并显示错误信息
  ///
  /// 在数据编辑后或保存按钮点击时调用此方法
  /// 验证所有项目，如果存在错误项目，显示错误提示
  ///
  /// 返回值:
  /// * [Future<bool>] - 如果没有错误项目返回 true，否则返回 false
  Future<bool> validateAndShowErrors() async {
    final errorItems = await validateAllItems();
    if (errorItems.isNotEmpty) {
      CommonDialog.show(content: errorItems.first.alertMessage ?? '');
      return false;
    }
    return true;
  }

  /// 获取资产文本的 JSON 字符串
  ///
  /// 遍历所有资产数据，构建资产文本映射，然后转换为 JSON 字符串
  /// 用于保存资产数据
  ///
  /// 返回值:
  /// * [String] - 资产文本的 JSON 字符串
  String getAssetTextJson() {
    final Map<String, dynamic> assetTextMap = {};

    // 遍历所有资产数据
    for (final value in assetDict.values) {
      // 处理每个资产数据
      for (var i = 0; i < value.length; i++) {
        final item = value[i];
        final itemName = item.itemName;
        if (itemName != null) {
          assetTextMap[itemName] = item.defaultData;
        }
      }
    }

    return jsonEncode(assetTextMap);
  }

  /// 设置是否需要底部导航栏
  ///
  /// 参数:
  /// * [needNavigationBar] - 是否需要底部导航栏
  void setNeedNavigationBar(bool? needNavigationBar) {
    this.needNavigationBar = needNavigationBar;
  }

  /// 设置是否需要进入时校验
  ///
  /// 参数:
  /// * [needEnterValidate] - 是否需要进入时校验
  void setNeedEnterValidate(bool needEnterValidate) {
    this.needEnterValidate = needEnterValidate;
  }

  /// 当列表中Item 发生变更时触发的回调
  ///
  /// 处理项目变更的通用逻辑，主要用于特定场景的逻辑处理
  ///
  /// 注意：JavaScript 执行已在 BaseAfCustomizeController.updateDefaultData 中统一处理
  ///
  /// 参数:
  /// * [entry] - 变更触发时的对象
  void onItemChanged(RxAssetItemWrapper entry) {
    LogUtil.d(
      'onItemChanged - itemName: ${entry.itemName}, itemId: ${entry.itemId}, scene: $scene, defaultData: ${entry.defaultData}',
    );
    if (scene == AfCustomizeViewScene.assetSchedule) {
      _onScheduleItemChanged(entry);
    }
  }

  /// 当Schedule项目变更时触发
  ///
  /// 处理日程相关项目的特殊逻辑
  ///
  /// 参数:
  /// * [entry] - 变更的日程项目
  void _onScheduleItemChanged(RxAssetItemWrapper entry) {
    // 如果 itemId 为 4时为终日
    if (entry.itemId == ScheduleItemIds.allDay.value) {
      // 终日需要修改当前 assetDict 中的所有时间项目的dateType 为 date
      setUnitDay(entry.defaultData == '1');
    }

    // 同步更新appointment数据
    _syncAppointmentData(entry);
  }

  /// 同步更新appointment数据
  ///
  /// 当子组件数据发生变化时，将变化同步到appointment对象中
  /// 根据字段类型进行分类处理，避免数据冗余
  ///
  /// 参数:
  /// * [entry] - 变更的项目
  void _syncAppointmentData(RxAssetItemWrapper entry) {
    if (appointment == null) return;

    final itemId = entry.itemId;
    final defaultData = entry.defaultData?.toString() ?? '';

    LogUtil.d('同步appointment数据 - itemId: $itemId, defaultData: $defaultData');

    // 根据字段类型进行分类处理
    final fieldType = _getFieldType(itemId);

    switch (fieldType) {
      case AppointmentFieldType.basic:
        // 只处理基本预约字段
        _updateBasicAppointmentFields(itemId, defaultData);
        break;
      case AppointmentFieldType.extraCommon:
        // 只处理extraCommonTextObj字段
        _updateExtraCommonTextObj(itemId, defaultData);
        break;
      case AppointmentFieldType.reservation:
        // 只处理reservationTextObj字段
        _updateReservationTextObj(itemId, defaultData);
        break;
      case AppointmentFieldType.unknown:
        // 未知类型，记录日志但不处理
        LogUtil.w('未知的字段类型 - itemId: $itemId, 跳过同步');
        break;
    }

    LogUtil.d('appointment数据同步完成 - 字段类型: ${fieldType.name}');
  }

  /// 获取字段类型
  ///
  /// 根据itemId判断该字段应该存储在appointment对象的哪个位置
  ///
  /// 参数:
  /// * [itemId] - 项目ID
  ///
  /// 返回值:
  /// * [AppointmentFieldType] - 字段类型
  AppointmentFieldType _getFieldType(String? itemId) {
    if (itemId == null) return AppointmentFieldType.unknown;

    // 基本预约字段 - 直接映射到appointment对象的属性
    if (_isBasicAppointmentField(itemId)) {
      return AppointmentFieldType.basic;
    }

    // 检查是否为动态生成的事件类型项目
    if (_isEventTypeItem(itemId)) {
      return AppointmentFieldType.extraCommon;
    }

    // 检查是否为预约公共项目
    if (_isReservationCommonItem(itemId)) {
      return AppointmentFieldType.reservation;
    }

    // 默认情况下，其他项目存储在extraCommonTextObj中
    return AppointmentFieldType.extraCommon;
  }

  /// 判断是否为基本预约字段
  ///
  /// 参数:
  /// * [itemId] - 项目ID
  ///
  /// 返回值:
  /// * [bool] - 是否为基本预约字段
  bool _isBasicAppointmentField(String itemId) {
    return ScheduleItemIds.name.equals(itemId) ||
        ScheduleItemIds.startDate.equals(itemId) ||
        ScheduleItemIds.endDate.equals(itemId) ||
        ScheduleItemIds.eventType.equals(itemId);
  }

  /// 判断是否为事件类型项目
  ///
  /// 参数:
  /// * [itemId] - 项目ID
  ///
  /// 返回值:
  /// * [bool] - 是否为事件类型项目
  bool _isEventTypeItem(String itemId) {
    // 检查是否在当前事件类型项目列表中
    return eventTypeItems.any((item) => item.itemId?.toString() == itemId);
  }

  /// 判断是否为预约公共项目
  ///
  /// 参数:
  /// * [itemId] - 项目ID
  ///
  /// 返回值:
  /// * [bool] - 是否为预约公共项目
  bool _isReservationCommonItem(String itemId) {
    // 检查是否在预约公共项目列表中
    final reservationItemCommonList = scheduleEventList?.reservationItemCommonList ?? [];
    return reservationItemCommonList.any((item) => item?.itemId?.toString() == itemId);
  }

  /// 更新基本预约字段
  ///
  /// 参数:
  /// * [itemId] - 项目ID
  /// * [defaultData] - 默认数据
  void _updateBasicAppointmentFields(String? itemId, String defaultData) {
    if (appointment == null || itemId == null) return;

    // 预约名称
    if (ScheduleItemIds.name.equals(itemId)) {
      appointment!.reservationName = defaultData;
      LogUtil.d('更新预约名称: $defaultData');
      return;
    }

    // 开始时间
    if (ScheduleItemIds.startDate.equals(itemId)) {
      // 当时间类型的字段发生变化时，要将时间的/分隔符修改为-
      final formattedStartTime = _formatTimeFieldData(defaultData);
      appointment!.start = formattedStartTime;
      LogUtil.d('更新开始时间: $formattedStartTime (原始值: $defaultData)');
      return;
    }

    // 结束时间
    if (ScheduleItemIds.endDate.equals(itemId)) {
      // 当时间类型的字段发生变化时，要将时间的/分隔符修改为-
      final formattedEndTime = _formatTimeFieldData(defaultData);
      appointment!.end = formattedEndTime;
      LogUtil.d('更新结束时间: $formattedEndTime (原始值: $defaultData)');
      return;
    }

    // 终日
    if (ScheduleItemIds.allDay.equals(itemId)) {
      appointment!.unitDay = defaultData;
      LogUtil.d('更新终日: $defaultData');
      return;
    }

    // アラート設定
    if (ScheduleItemIds.alertSetting.equals(itemId)) {
      appointment!.alertSetting = defaultData;
      LogUtil.d('更新アラート設定: $defaultData');
      return;
    }

    // 事件类型
    if (ScheduleItemIds.eventType.equals(itemId)) {
      // 查找对应的事件类型
      final eventTypeList = scheduleEventList?.eventTypeList ?? [];
      final selectedEventType = eventTypeList.firstWhereOrNull((eventType) => eventType?.label == defaultData);

      if (selectedEventType != null) {
        appointment!.eventTypeId = selectedEventType.itemId;
        appointment!.eventTypeName = selectedEventType.label;
        LogUtil.d('更新事件类型: ${selectedEventType.itemId} (ID: ${selectedEventType.label})');
      }
      return;
    }
  }

  /// 格式化时间字段数据
  ///
  /// 当时间类型的字段发生变化时，将时间的/分隔符修改为-分隔符
  ///
  /// 参数:
  /// * [timeData] - 原始时间数据
  ///
  /// 返回值:
  /// * [String] - 格式化后的时间数据，将/分隔符替换为-分隔符
  String _formatTimeFieldData(String timeData) {
    if (timeData.isEmpty) return timeData;

    // 将时间的/分隔符修改为-分隔符
    return timeData.replaceAll('/', '-');
  }

  /// 更新extraCommonTextObj字段
  ///
  /// 参数:
  /// * [itemId] - 项目ID
  /// * [defaultData] - 默认数据
  void _updateExtraCommonTextObj(String? itemId, String defaultData) {
    if (appointment == null || itemId == null) return;

    // 确保extraCommonTextObj不为null
    appointment!.extraCommonTextObj ??= <String, dynamic>{};

    // 获取当前的extraCommonTextObj
    final extraCommonTextObj = appointment!.extraCommonTextObj as Map<String, dynamic>;

    // 更新对应的字段
    extraCommonTextObj[itemId] = defaultData;

    LogUtil.d('更新extraCommonTextObj - itemId: $itemId, value: $defaultData');
    LogUtil.d('当前extraCommonTextObj内容: ${extraCommonTextObj.toString()}');
  }

  /// 更新reservationTextObj字段
  ///
  /// 参数:
  /// * [itemId] - 项目ID
  /// * [defaultData] - 默认数据
  void _updateReservationTextObj(String? itemId, String defaultData) {
    if (appointment == null || itemId == null) return;

    // 确保reservationTextObj不为null
    appointment!.reservationTextObj ??= <String, dynamic>{};

    // 获取当前的reservationTextObj
    final reservationTextObj = appointment!.reservationTextObj as Map<String, dynamic>;

    // 更新对应的字段
    reservationTextObj[itemId] = defaultData;

    LogUtil.d('更新reservationTextObj - itemId: $itemId, value: $defaultData');
  }

  /// 设置场景
  ///
  /// 参数:
  /// * [scene] - 新的场景值
  void setScene(AfCustomizeViewScene scene) {
    this.scene = scene;
  }

  /// 设置终日状态
  ///
  /// 参数:
  /// * [isUnitDay] - 是否为终日
  void setUnitDay(bool isUnitDay) {
    this.isUnitDay.value = isUnitDay;
  }

  /// 更新日程事件列表
  ///
  /// 参数:
  /// * [scheduleEventList] - 新的日程事件列表
  void updateScheduleEventList(AppointmentListResponse? scheduleEventList) {
    this.scheduleEventList = scheduleEventList;
  }

  /// 设置预约列表项
  ///
  /// 更新当前控制器中的预约列表项数据
  ///
  /// 参数:
  /// * [appointmentListItem] - 新的预约列表项数据
  void setAppointment(Appointment? appointmentListItem) {
    appointment = appointmentListItem;
  }

  /// 更新事件类型相关项目
  ///
  /// 当用户选择一个事件类型时，将该事件类型的项目添加到视图中
  /// 同时清理与旧事件类型相关的数据，避免数据不匹配问题
  ///
  /// 参数:
  /// * [eventTypeItems] - 事件类型相关的项目列表
  void updateEventTypeItems(List<EventTypeItem?> eventTypeItems) {
    // 过滤掉null值并转换为列表
    final filteredItems = eventTypeItems.whereType<EventTypeItem>().toList();

    // 清理与旧事件类型相关的数据
    _clearOldEventTypeData();

    // 使用assignAll方法来确保响应式更新正确触发
    this.eventTypeItems.assignAll(filteredItems);
  }

  /// 清理与旧事件类型相关的数据
  ///
  /// 当切换事件类型时，清理 extraCommonTextObj 中与旧事件类型项目相关的数据
  /// 避免新布局项目显示不匹配的旧数据
  void _clearOldEventTypeData() {
    if (appointment?.extraCommonTextObj == null) return;

    final extraCommonTextObj = appointment!.extraCommonTextObj as Map<String, dynamic>;

    // 获取当前事件类型项目的 itemId 列表
    final currentEventTypeItemIds = eventTypeItems.map((item) => item.itemId?.toString()).toSet();

    // 创建新的 extraCommonTextObj，只保留非事件类型相关的数据
    final newExtraCommonTextObj = <String, dynamic>{};

    extraCommonTextObj.forEach((key, value) {
      // 如果这个 key 不属于当前事件类型的项目，则保留
      if (currentEventTypeItemIds.isNotEmpty && !currentEventTypeItemIds.contains(key)) {
        newExtraCommonTextObj[key] = value;
      }
    });

    // 更新 appointment 的 extraCommonTextObj
    appointment!.extraCommonTextObj = newExtraCommonTextObj;

    LogUtil.d('清理旧事件类型数据 - 清理前: ${extraCommonTextObj.keys.toList()}');
    LogUtil.d('清理旧事件类型数据 - 清理后: ${newExtraCommonTextObj.keys.toList()}');
  }

  /// 构建资产文本映射
  ///
  /// 遍历资产字典中的所有资产项目，提取项目名称和默认数据，
  /// 构建用于保存资产信息的文本映射表
  ///
  /// 返回值：
  /// * [Map<String, dynamic>] 资产文本映射表
  ///   - 键：资产项目名称 (itemName)
  ///   - 值：资产项目的默认数据 (defaultData)
  ///
  /// 使用场景：
  /// * 资产详情保存时构建资产文本数据
  /// * 资产历史记录编辑时构建资产文本数据
  /// * 其他需要将资产字典转换为文本映射的场景
  ///
  /// 注意事项：
  /// * 只处理具有有效 itemName 的资产项目
  /// * 自动过滤系统管理的时间字段，避免数据冲突
  /// * 遵循 Clean Architecture 原则，作为数据转换的公共方法
  Map<String, dynamic> buildAssetTextMap() {
    final Map<String, dynamic> assetTextMap = {};

    // 定义需要过滤的系统字段列表
    // 这些字段由系统自动管理，不应包含在用户编辑的资产文本数据中
    const List<String> excludedSystemFields = ['updatedDate', 'createdDate', '更新日', '更新時間'];

    // 遍历资产字典中的所有分类
    for (final categoryItems in assetDict.values) {
      // 遍历每个分类下的所有资产项目
      for (var i = 0; i < categoryItems.length; i++) {
        final item = categoryItems[i];
        final itemName = item.itemName;

        // 只处理具有有效项目名称的资产项目
        if (itemName != null) {
          // 过滤掉字段
          if (!excludedSystemFields.contains(itemName)) {
            // 确保数据可以被JSON序列化
            final serializedValue = _ensureSerializable(item.defaultData, item.itemType, item.optionObject);
            assetTextMap[itemName] = serializedValue;
          }
        }
      }
    }

    return assetTextMap;
  }

  /// 确保数据可以被JSON序列化
  ///
  /// 将复杂的嵌套Map和对象转换为可序列化的基本数据类型
  ///
  /// 参数：
  /// * [data] - 原始数据
  /// * [itemType] - 项目类型
  /// * [optionObject] - 选项对象
  ///
  /// 返回值：
  /// * [dynamic] - 可序列化的数据
  dynamic _ensureSerializable(dynamic data, String? itemType, OptionObjModel? optionObject) {
    try {
      // 处理null值
      if (data == null) {
        return null;
      }

      // 优先处理checkbox类型的特殊逻辑
      if (itemType == SharedItemTypeEnum.checkbox.value) {
        if ((optionObject?.checkboxMultiFlg ?? '') == '1' && data is String) {
          try {
            final decoded = jsonDecode(data);
            return _cleanForSerialization(decoded);
          } catch (e) {
            LogUtil.d('Checkbox数据解析失败，使用原始字符串: $e');
            return data;
          }
        }
        // checkbox单选或其他情况，直接返回原始数据
        return data;
      }

      // 非checkbox的基本类型直接返回
      if (data is String || data is num || data is bool) {
        return data;
      }

      // 处理复杂对象
      return _cleanForSerialization(data);
    } catch (e) {
      LogUtil.e('数据序列化处理失败: $e');
      // 返回字符串形式作为备选方案
      return data?.toString() ?? '';
    }
  }

  /// 清理数据以确保可序列化
  ///
  /// 递归处理复杂数据结构，移除不可序列化的对象
  ///
  /// 参数：
  /// * [data] - 要清理的数据
  ///
  /// 返回值：
  /// * [dynamic] - 清理后的可序列化数据
  dynamic _cleanForSerialization(dynamic data) {
    if (data == null) {
      return null;
    }

    // 基本类型直接返回
    if (data is String || data is num || data is bool) {
      return data;
    }

    // 处理List类型
    if (data is List) {
      return data.map((item) => _cleanForSerialization(item)).toList();
    }

    // 处理Map类型
    if (data is Map) {
      final Map<String, dynamic> cleanedMap = {};
      data.forEach((key, value) {
        final stringKey = key.toString();
        cleanedMap[stringKey] = _cleanForSerialization(value);
      });
      return cleanedMap;
    }

    // 尝试调用toJson方法
    try {
      if (data.runtimeType.toString().contains('Model') || data.runtimeType.toString().contains('Obj')) {
        final jsonData = (data as dynamic).toJson();
        return _cleanForSerialization(jsonData);
      }
    } catch (e) {
      LogUtil.d('对象没有toJson方法或调用失败: ${data.runtimeType}');
    }

    // 最后转换为字符串
    return data.toString();
  }

  /// 设置自定义逻辑JS
  ///
  void setCustomizeLogicJs(GetCustomizeLogicResult? customizeLogicJs) {
    this.customizeLogicJs = customizeLogicJs;
    LogUtil.d('设置自定义逻辑JS: ${customizeLogicJs?.jsString}');
  }

  /// 执行自定义逻辑 JavaScript
  ///
  /// 在页面绘制刷新时调用，避免频繁触发
  /// 使用单例 JsExecutor 实例，提高性能
  ///
  Future<void> executeCustomizeLogicJs() async {
    // 检查是否有自定义逻辑 JavaScript 代码
    final jsString = customizeLogicJs?.jsString;
    if (jsString == null || jsString.isEmpty) {
      LogUtil.d('没有自定义逻辑 JavaScript 代码，跳过执行');
      return;
    }

    // 检查 assetDict 是否为空
    // 注意：这里只是警告，不阻止执行，因为某些场景下可能需要在数据加载前执行 JS
    if (assetDict.isEmpty) {
      LogUtil.w('executeCustomizeLogicJs: assetDict 为空，批量更新管理器将无法创建');
    }

    try {
      // 初始化 JsExecutor（如果尚未初始化）

      // 设置控制器到桥接服务（数据会自动从 controller.assetDict 获取）
      jsExecutor.setController(this);

      LogUtil.d('开始执行自定义逻辑 JavaScript - assetDict.isEmpty: ${assetDict.isEmpty}');
      await jsExecutor.eval(jsString, JsCodeLocal.customizedLogicJavascript);
      LogUtil.d('自定义逻辑 JavaScript 执行完成');
    } catch (e) {
      LogUtil.e('执行自定义逻辑 JavaScript 失败: $e');
    }
  }

  /// 更新 JavaScript 执行器的数据字典
  ///
  /// 当 assetDict 发生变化时调用，确保 JavaScript 环境中的数据是最新的
  Future<void> updateJsExecutorData() async {
    LogUtil.d('刷新 JsExecutor 的数据字典');
    await jsExecutor.refreshJavaScriptData();
  }
}

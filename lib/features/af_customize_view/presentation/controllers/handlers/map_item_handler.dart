import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/formatters/value_formatter.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/handlers/item_handler_strategy.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/domain/models/map_picker_params.dart';
import 'package:flutter/material.dart';

/// 地图类型项目处理策略
class MapItemHandler implements ItemHandlerStrategy {
  final RxAssetItemWrapper entry;
  final NavigationService navigationService;
  final Function(String) updateDefaultData;

  MapItemHandler({required this.entry, required this.navigationService, required this.updateDefaultData});

  @override
  Future<void> handleTap(BuildContext context) async {
    final result = await navigationService.navigateTo(
      AutoRoutes.mapPicker,
      arguments: MapPickerParams(
        title: entry.itemDisplayName ?? '',
        currentAddress: entry.defaultData?.toString() ?? '',
      ),
    );

    if (result != null) {
      updateDefaultData(result.address);
    }
  }

  @override
  String getDisplayValue() {
    final s = ValueFormatter.formatValue(
      itemType: entry.itemType ?? '',
      defaultData: entry.defaultData,
      percentage: entry.optionObject?.percentage,
      unit: entry.optionObject?.unit,
      valueForShow: entry.valueForShow.value,
      itemValue: entry.itemValue?.toString() ?? '',
      dateType: entry.optionObject?.dateType,
    );
    if (s.isEmpty) {
      return '未設定';
    }
    return s;
  }

  @override
  void initData() {
    // 地图类型不需要特殊初始化
  }

  @override
  void updateData(String value) {
    updateDefaultData(value);
  }

  @override
  void dispose() {
    // 地图类型处理器不需要特殊的清理操作
  }
}

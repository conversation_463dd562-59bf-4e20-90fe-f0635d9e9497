import 'dart:async';
import 'dart:convert';

import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/enums/date_type_enum.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/enums/schedule_item_ids.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/formatters/value_formatter.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_time_display_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/handlers/item_handler_strategy.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:flutter/material.dart';

/// 日期类型项目处理策略
class DateItemHandler implements ItemHandlerStrategy {
  final RxAssetItemWrapper entry;
  final AfCustomizeViewController viewController;
  final Function(String) updateDefaultData;

  /// isUnitDay监听器的订阅，用于在dispose时取消订阅
  StreamSubscription<bool>? _unitDaySubscription;

  DateItemHandler({required this.entry, required this.viewController, required this.updateDefaultData});

  @override
  Future<void> handleTap(BuildContext context) async {
    final DateType dateType = _getDateType();

    final result = await AfTimeDisplayDialog.show(context: context, dateType: dateType);

    if (result != null) {
      if (result.isClear) {
        // 清除
        updateDefaultData('');
      } else if (result.formattedDate != null) {
        // 选择日期
        updateDefaultData(result.formattedDate ?? '');

        // 如果是assetSchedule场景，确保数据格式与isUnitDay一致
        if (viewController.scene == AfCustomizeViewScene.assetSchedule) {
          _handleUnitDayChange(viewController.isUnitDay.value);
        }
      }
    }
  }

  @override
  String getDisplayValue() {
    String? dateTypeValue = entry.optionObject?.dateType;

    // 在assetSchedule场景中处理开始和终了日期
    if (_isScheduleDateField()) {
      dateTypeValue = viewController.isUnitDay.value ? DateType.date.value : DateType.dateTime.value;
    }

    LogUtil.d('entry-type:${entry.itemType},entry-defaultData:${entry.defaultData},dateTypeValue:$dateTypeValue');
    final formatValue = ValueFormatter.formatValue(
      itemType: entry.itemType ?? '',
      defaultData: entry.defaultData,
      percentage: entry.optionObject?.percentage,
      unit: entry.optionObject?.unit,
      valueForShow: entry.valueForShow.value,
      itemValue: entry.itemValue?.toString() ?? '',
      dateType: dateTypeValue,
    );
    LogUtil.d('formatValue:$formatValue');
    return formatValue.replaceAll('-', '/');
  }

  /// 判断当前字段是否为日程视图中的日期字段
  bool _isScheduleDateField() {
    return viewController.scene == AfCustomizeViewScene.assetSchedule &&
        (ScheduleItemIds.startDate.equals(entry.itemId) || ScheduleItemIds.endDate.equals(entry.itemId));
  }

  @override
  void initData() {
    // 初始化时确保dateType和defaultData与isUnitDay一致
    if (viewController.scene == AfCustomizeViewScene.assetSchedule) {
      _handleUnitDayChange(viewController.isUnitDay.value);

      // 监听isUnitDay的变化，并保存StreamSubscription引用以便后续清理
      _unitDaySubscription = viewController.isUnitDay.listen(_handleUnitDayChange);
    }
  }

  @override
  void dispose() {
    // 取消isUnitDay监听器，防止内存泄漏和已释放控制器的访问
    _unitDaySubscription?.cancel();
    _unitDaySubscription = null;
  }

  @override
  void updateData(String value) {
    updateDefaultData(value);
  }

  /// 处理isUnitDay变化
  void _handleUnitDayChange(bool isUnitDay) {
    // 只处理开始日时和终了日时，其他date类型字段不做处理，以原始数据为准
    if (_isScheduleDateField()) {
      // 更新optionObject中的dateType
      _updateDateType(isUnitDay);

      // 更新defaultData
      _updateDefaultDataIfNeeded(isUnitDay);
    }

    // 更新显示值
    entry.valueForShow.value = getDisplayValue();
  }

  /// 获取日期类型
  DateType _getDateType() {
    // 只对开始日时和终了日时应用终日状态逻辑，其他date类型字段以原始数据为准
    if (_isScheduleDateField()) {
      // 开始日时和终了日时：根据isUnitDay状态决定日期类型
      return viewController.isUnitDay.value ? DateType.date : DateType.dateTime;
    }

    // 其他字段：基于optionObject中的dateType值返回对应的日期类型，保持原始数据
    if (entry.optionObject?.dateType == DateType.dateTime.value) {
      return DateType.dateTime;
    } else {
      return DateType.date;
    }
  }

  /// 更新dateType
  void _updateDateType(bool isUnitDay) {
    if (entry.optionObject == null) return;

    final currentOption = entry.optionObject!;
    final newDateType = isUnitDay ? DateType.date.value : DateType.dateTime.value;

    if (currentOption.dateType == newDateType) return;

    currentOption.dateType = newDateType;

    try {
      final modelType = entry.model.runtimeType.toString();
      if (modelType.contains('AssetItemAdapter')) {
        entry.model.option = jsonEncode(currentOption.toJson());
      }
    } catch (e) {
      LogUtil.d('更新 dateType 时发生异常: $e');
    }
  }

  /// 更新defaultData
  void _updateDefaultDataIfNeeded(bool isUnitDay) {
    if (entry.defaultData == null || entry.defaultData.toString().isEmpty) return;

    final currentData = entry.defaultData.toString();

    // 如果是终日且当前数据包含时间部分，则去掉时间部分
    if (isUnitDay && currentData.contains(' ')) {
      final datePart = currentData.split(' ')[0];
      updateDefaultData(datePart);
    }
    // 如果不是终日，但数据只有日期部分（没有时间部分），需要添加默认时间
    else if (!isUnitDay && !currentData.contains(' ')) {
      // 判断是否是终了日时项目
      final isEndDate = entry.itemId == ScheduleItemIds.endDate.value;

      // 如果是终了日时项目，设置时间为 23:59，否则设置为 00:00
      final defaultTime = isEndDate ? '23:59' : '00:00';
      final dateTimeValue = '$currentData $defaultTime';
      updateDefaultData(dateTimeValue);
    }
  }
}

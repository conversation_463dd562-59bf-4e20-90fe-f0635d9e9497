import 'package:asset_force_mobile_v2/core/extensions/nullable_extensions.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/enums/schedule_item_ids.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/formatters/value_formatter.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/handlers/item_handler_strategy.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/list_selector/domain/models/list_selector_params.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';
import 'package:flutter/material.dart';

/// 列表类型项目处理策略
class ListItemHandler implements ItemHandlerStrategy {
  final RxAssetItemWrapper entry;
  final AfCustomizeViewController viewController;
  final NavigationService navigationService;
  final Function(String) updateDefaultData;

  ListItemHandler({
    required this.entry,
    required this.viewController,
    required this.navigationService,
    required this.updateDefaultData,
  });

  @override
  Future<void> handleTap(BuildContext context) async {
    // 如果是assetSchedule场景，需要判断条目是否为 eventtype
    if (viewController.scene == AfCustomizeViewScene.assetSchedule && ScheduleItemIds.eventType.equals(entry.itemId)) {
      // 如果是事件类型，则打开事件类型选择器
      await _handleEventTypeSelector();
      return;
    }

    // 普通列表选择
    final result = await navigationService.navigateTo(
      AutoRoutes.listSelector,
      arguments: ListSelectorParams(title: entry.itemDisplayName ?? '', items: entry.optionObject?.data ?? []),
    );

    if (result != null) {
      updateDefaultData(result);
    }
  }

  @override
  String getDisplayValue() {
    return ValueFormatter.formatValue(
      itemType: entry.itemType ?? '',
      defaultData: entry.defaultData,
      percentage: entry.optionObject?.percentage,
      unit: entry.optionObject?.unit,
      valueForShow: entry.valueForShow.value,
      itemValue: entry.itemValue?.toString() ?? '',
      dateType: entry.optionObject?.dateType,
    );
  }

  @override
  void initData() {
    // 列表类型不需要特殊初始化
    if (ScheduleItemIds.eventType.equals(entry.itemId)) {
      Future.microtask(() {
        viewController.appointment?.let((it) {
          entry.updateDefaultData(it.eventTypeName ?? '');
          final eventTypeList = viewController.scheduleEventList?.eventTypeList ?? [];
          _updateEventTypeSelection(it.eventTypeName, eventTypeList);
        });
      });
    }
  }

  @override
  void updateData(String value) {
    updateDefaultData(value);
  }

  /// 处理事件类型选择器
  Future<void> _handleEventTypeSelector() async {
    // 打开事件类型选择器
    final eventTypeList = viewController.scheduleEventList?.eventTypeList ?? [];
    final eventTypeLabels = eventTypeList.map((e) => e?.label ?? '').toList();

    final result = await navigationService.navigateTo(
      AutoRoutes.listSelector,
      arguments: ListSelectorParams(title: entry.itemDisplayName ?? '', items: eventTypeLabels),
    );

    _updateEventTypeSelection(result, eventTypeList);
  }

  /// 更新事件类型选择并更新列表条目
  void _updateEventTypeSelection(result, List<EventType?> eventTypeList) {
    if (result != null) {
      LogUtil.d('result: $result');
      updateDefaultData(result);

      // 获取选中的事件类型
      final selectedEventType = eventTypeList.firstWhere(
        (element) => (element?.label ?? '') == result,
        orElse: () => EventType(),
      );

      // 更新预约项目信息
      if (selectedEventType != null) {
        // 记录旧的事件类型ID用于日志
        final oldEventTypeId = viewController.appointment?.eventTypeId;

        viewController.appointment?.eventTypeName = selectedEventType.label ?? '';
        viewController.appointment?.eventTypeId = selectedEventType.itemId ?? 0;

        LogUtil.d('Event type changed from $oldEventTypeId to ${selectedEventType.itemId}');
        LogUtil.d('Selected event type: ${selectedEventType.label}');
        LogUtil.d('Selected event type key: ${selectedEventType.key}');
        LogUtil.d('Selected event type value: ${selectedEventType.value}');
        LogUtil.d('Selected event type itemId: ${selectedEventType.itemId}');
        LogUtil.d('Updated appointment eventTypeId: ${viewController.appointment?.eventTypeId}');

        // 如果需要，可以在这里添加额外的处理逻辑
        // 例如更新相关的事件类型项目
        if (viewController.scene == AfCustomizeViewScene.assetSchedule) {
          // 获取事件类型相关的项目并更新
          final eventTypeItems = selectedEventType.itemList ?? [];
          LogUtil.d('Event type items: ${eventTypeItems.length}');

          // 更新事件类型项目（这会自动清理旧数据）
          viewController.updateEventTypeItems(eventTypeItems);
        }
      }
    }
  }

  @override
  void dispose() {
    // 列表类型处理器不需要特殊的清理操作
  }
}

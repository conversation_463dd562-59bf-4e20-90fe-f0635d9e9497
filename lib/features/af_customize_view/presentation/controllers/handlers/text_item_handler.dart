import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/formatters/value_formatter.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/handlers/item_handler_strategy.dart';
import 'package:flutter/material.dart';

/// 文本类型项目处理策略
class TextItemHandler implements ItemHandlerStrategy {
  final RxAssetItemWrapper entry;
  final TextEditingController textEditingController;
  final FocusNode focusNode;
  final Function(String) updateDefaultData;
  final VoidCallback onEditingComplete;

  TextItemHandler({
    required this.entry,
    required this.textEditingController,
    required this.focusNode,
    required this.updateDefaultData,
    required this.onEditingComplete,
  });

  @override
  Future<void> handleTap(BuildContext context) async {
    startEditing();
  }

  @override
  String getDisplayValue() {
    return ValueFormatter.formatValue(
      itemType: entry.itemType ?? '',
      defaultData: entry.defaultData,
      percentage: entry.optionObject?.percentage,
      unit: entry.optionObject?.unit,
      valueForShow: entry.valueForShow.value,
      itemValue: entry.itemValue?.toString() ?? '',
      dateType: entry.optionObject?.dateType,
    );
  }

  @override
  void initData() {
    // 文本类型不需要特殊初始化
    textEditingController.text = entry.valueForShow.value;
  }

  @override
  void updateData(String value) {
    updateDefaultData(value);
  }

  /// 开始编辑
  void startEditing() {
    final valueWithoutCommas = entry.valueForShow.value.replaceAll(',', '');
    textEditingController.text = valueWithoutCommas;
    focusNode.requestFocus();
  }

  /// 结束编辑
  void endEditing() {
    final newValue = textEditingController.text;
    updateDefaultData(newValue);
    onEditingComplete();
  }

  @override
  void dispose() {
    // 文本类型处理器不需要特殊的清理操作
    // TextEditingController 和 FocusNode 由主控制器管理
  }
}

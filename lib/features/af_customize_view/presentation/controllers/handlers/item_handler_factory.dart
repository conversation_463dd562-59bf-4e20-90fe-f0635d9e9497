import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/handlers/date_item_handler.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/handlers/item_handler_strategy.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/handlers/list_item_handler.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/handlers/map_item_handler.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/handlers/text_item_handler.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:flutter/material.dart';

/// 项目处理策略工厂
class ItemHandlerFactory {
  /// 创建对应类型的项目处理策略
  static ItemHandlerStrategy createHandler({
    required RxAssetItemWrapper entry,
    required AfCustomizeViewController viewController,
    required NavigationService navigationService,
    required Function(String) updateDefaultData,
    TextEditingController? textEditingController,
    FocusNode? focusNode,
    VoidCallback? onEditingComplete,
  }) {
    // 根据项目类型创建对应的处理策略
    if (SharedItemTypeEnum.date.equals(entry.itemType)) {
      return DateItemHandler(entry: entry, viewController: viewController, updateDefaultData: updateDefaultData);
    } else if (SharedItemTypeEnum.map.equals(entry.itemType)) {
      return MapItemHandler(entry: entry, navigationService: navigationService, updateDefaultData: updateDefaultData);
    } else if (SharedItemTypeEnum.list.equals(entry.itemType)) {
      return ListItemHandler(
        entry: entry,
        viewController: viewController,
        navigationService: navigationService,
        updateDefaultData: updateDefaultData,
      );
    } else {
      // 默认使用文本处理策略
      return TextItemHandler(
        entry: entry,
        textEditingController: textEditingController!,
        focusNode: focusNode!,
        updateDefaultData: updateDefaultData,
        onEditingComplete: onEditingComplete!,
      );
    }
  }
}

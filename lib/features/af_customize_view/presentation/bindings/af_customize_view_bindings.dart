import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/calc_asset_dict_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/check_validate_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/download_file_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_master_default_value_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/prepare_asset_data_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/upload_file_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/upload_image_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_asset_count_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_checkbox_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_digital_sign_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_file_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_image_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_master_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_radio_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_relation_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_rfid_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_talk_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_text_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_user_select_item_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_time_display_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/controllers/expandable_section_controller.dart';
import 'package:asset_force_mobile_v2/features/master_select/data/repositories/master_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/repositories/master_repository.dart';
import 'package:get/get.dart';

class AfCustomizeViewBindings extends Bindings with JsExecutorBindingMixin {
  AfCustomizeViewBindings();

  @override
  void dependencies() {
    final id = Get.parameters['id'] ?? '';

    LogUtil.d('af_customize_view_bindings id: $id');

    // #region 注册 DioUtil
    final dioUtil = Get.find<DioUtil>();

    // repository
    Get.lazyPut<MasterRepository>(() => MasterRepositoryImpl(dioUtil: dioUtil));

    // #region 注册 UseCase

    // 注册 JsExecutor 相关依赖（使用统一的服务，自动清理）
    final jsExecutorContext = registerJsExecutorWithTag(id);

    Get.lazyPut(() => GetMasterDefaultValueUseCase(masterRepository: Get.find<MasterRepository>()));
    Get.lazyPut(() => GetTurlUseCase(s3Repository: Get.find()));
    Get.lazyPut(() => CalcAssetDictUseCase(Get.find<JsExecutor>(tag: id)));
    Get.lazyPut(() => PrepareAssetDataUseCase());
    Get.lazyPut(() => CheckValidateUseCase());
    Get.lazyPut(() => UploadImageUseCase(dioUtil: dioUtil, s3Repository: Get.find()));
    Get.lazyPut(() => DownloadFileUseCase(dioUtil: dioUtil));
    Get.lazyPut(() => UploadFileUseCase(dioUtil: dioUtil, s3Repository: Get.find()));
    // #endregion

    // #region Main Controller
    Get.lazyPut(
      () => AfCustomizeViewController(
        prepareAssetDataUseCase: Get.find(),
        calcAssetDictUseCase: Get.find(),
        checkValidateUseCase: Get.find(),
        navigationService: Get.find(),
        jsExecutor: Get.find<JsExecutor>(tag: id),
        jsExecutorContext: jsExecutorContext,
      ),
      tag: id,
    );

    // 时间选择组件控制器
    Get.lazyPut(() => AfTimeDisplayController());

    // #region Sub Controllers
    Get.lazyPut(() => ExpandableSectionController(), tag: id, fenix: true);
    Get.create(() => AfCustomizeAssetCountItemController(assetId: id), tag: id);
    Get.create(
      () => AfCustomizeMasterItemController(navigationService: Get.find(), assetId: id),
      tag: id,
    );
    Get.create(() => AfCustomizeDigitalSignItemController(assetId: id), tag: id);
    Get.create(
      () => AfCustomizeImageItemController(
        getTurlUseCase: Get.find(),
        uploadImageUseCase: Get.find(),
        navigationService: Get.find(),
        assetId: id,
      ),
      tag: id,
    );
    Get.create(
      () => AfCustomizeTextItemController(navigationService: Get.find(), assetId: id),
      tag: id,
    );
    Get.create(() => AfCustomizeRadioItemController(assetId: id), tag: id);
    Get.create(() => AfCustomizeRfidItemController(assetId: id), tag: id);
    Get.create(
      () => AfCustomizeCheckboxItemController(assetId: id, navigatorService: Get.find<NavigationService>()),
      tag: id,
    );
    Get.create(() => AfCustomizeTalkItemController(), tag: id);
    Get.create(
      () => AfCustomizeFileItemController(
        uploadFileUseCase: Get.find(),
        downloadFileUseCase: Get.find(),
        dialogService: Get.find(),
        getTurlUseCase: Get.find(),
        assetId: id,
      ),
      tag: id,
    );
    Get.create(
      () => AfCustomizeUserSelectItemController(navigationService: Get.find(), assetId: id),
      tag: id,
    );
    Get.lazyPut(
      () => AfCustomizeRelationItemController(navigationService: Get.find(), assetId: id),
      tag: id,
    );
  }
}

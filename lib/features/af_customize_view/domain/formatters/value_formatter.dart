import 'package:asset_force_mobile_v2/core/utils/number_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/enums/date_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';

class ValueFormatter {
  static String formatValue({
    required String itemType,
    required dynamic defaultData,
    String? percentage,
    String? unit,
    String valueForShow = '',
    String itemValue = '',
    String? dateType,
  }) {
    if (_isAssetItemValue(itemValue)) {
      return valueForShow;
    }

    final formattedValue = _formatByType(itemType, defaultData, dateType: dateType);
    return _appendPercentageAndUnit(
      formattedValue,
      percentage: percentage,
      unit: unit,
      hasDefaultData: defaultData?.toString().isNotEmpty ?? false,
    );
  }

  static String _formatByType(String itemType, dynamic defaultData, {String? dateType}) {
    if (defaultData == null || defaultData.toString().isEmpty) {
      return '';
    }

    final value = defaultData.toString();

    if (SharedItemTypeEnum.calculate.equals(itemType)) {
      final (commaPoint, decimalPoint) = _getCalculateConfig();
      return _formatNumber(value, commaPoint, decimalPoint);
    }

    if (SharedItemTypeEnum.currency.equals(itemType)) {
      final decimalPoint = _getCurrencyConfig();
      return _formatNumber(value, 3, decimalPoint);
    }

    if (SharedItemTypeEnum.number.equals(itemType)) {
      final (commaPoint, decimalPoint) = _getNumberConfig();
      return _formatNumber(value, commaPoint, decimalPoint);
    }

    // 处理日期类型，根据dateType参数决定显示格式
    if (SharedItemTypeEnum.date.equals(itemType)) {
      // 如果指定了dateType为date，则只显示日期部分（如果有时间部分则去掉）
      if (dateType == DateType.date.value && value.contains(' ')) {
        return value.split(' ')[0]; // 只返回日期部分
      }
      // 如果指定了dateType为dateTime，确保显示完整的日期时间
      else if (dateType == DateType.dateTime.value) {
        // 如果只有日期部分，可以考虑添加默认时间，但这里不处理
        // 因为通常用户会自己选择时间
        return value;
      }
    }

    return value;
  }

  static String _formatNumber(String value, int commaPoint, int decimalPoint) {
    return NumberUtils.formatNumberWithCommaAndDecimal(commaPoint, decimalPoint, value, true);
  }

  static String _appendPercentageAndUnit(
    String value, {
    String? percentage,
    String? unit,
    required bool hasDefaultData,
  }) {
    final result = hasDefaultData && percentage != null && percentage != '0' ? percentage : value;
    return hasDefaultData && unit != null ? '$result$unit' : result;
  }

  static bool _isAssetItemValue(String value) => value.contains('assetItemName:');

  static (int, int) _getCalculateConfig() => (3, 12); // 示例配置
  static int _getCurrencyConfig() => 2; // 示例配置
  static (int, int) _getNumberConfig() => (3, 2); // 示例配置
}

import 'dart:io';

import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/controllers/download_controller.dart';
import 'package:dio/dio.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';

/// 文件下载参数类
///
/// 用于传递下载文件所需的参数:
/// - [url]: 文件下载地址
/// - [fileName]: 保存的文件名
/// - [onProgress]: 下载进度回调函数,参数为下载进度(0.0-1.0)
class DownloadFileParams {
  /// 文件下载地址
  final String url;

  /// 保存的文件名
  final String fileName;

  /// 下载进度回调函数
  /// 参数为下载进度,范围0.0-1.0
  final Function(double progress)? onProgress;

  /// 构造函数
  ///
  /// [url]和[fileName]为必填参数
  /// [onProgress]为可选参数,用于监听下载进度
  DownloadFileParams({required this.url, required this.fileName, this.onProgress});
}

/// 文件下载用例
///
/// 使用已有的 [DownloadController] 实现文件下载
/// 这是一个适配器模式的应用，将现有的下载功能适配到 Clean Architecture 的用例中
class DownloadFileUseCase extends UseCase<void, DownloadFileParams> {
  final DioUtil dioUtil;

  DownloadFileUseCase({required this.dioUtil});

  @override
  Future<void> call(DownloadFileParams params) async {
    try {
      // get save file's full path
      final String saveFile = await _getSaveFile(params.fileName);

      // start download。
      await _download(params.url, saveFile, params);

      // open file
      _openFile(saveFile);
    } catch (e) {
      LogUtil.e('Error during download: $e');
      throw BusinessException('通信環境を確認して、もう一度やり直してください。');
    }
  }

  Future<String> _getSaveFile(String filename) async {
    final dir = await getApplicationDocumentsDirectory();
    return '${dir.path}/$filename';
  }

  Future<void> _download(String url, String savePath, DownloadFileParams params) async {
    await dioUtil.dio.download(
      url,
      savePath,
      options: Options(extra: {'type': 'download'}),
      onReceiveProgress: (received, total) {
        if (total != -1) {
          final progress = (received / total) * 100;
          params.onProgress?.call(progress);
        }
      },
    );
  }

  /// 打开文件
  ///
  /// [filePath] 文件路径
  ///
  /// 该方法会检查文件是否存在,如果存在则使用系统默认程序打开文件
  ///
  /// 示例:
  /// ```dart
  /// _openFile('/path/to/file.pdf');
  /// ```
  Future<void> _openFile(String filePath) async {
    if (await File(filePath).exists()) {
      OpenFile.open(filePath);
    }
  }
}

import 'package:asset_force_mobile_v2/core/constant/message_constants.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/number_validator.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';

class CheckValidateParams {
  RxAssetItemWrapper entry;
  AfCustomizeViewScene scene;

  CheckValidateParams({required this.entry, required this.scene});
}

class CheckValidateResult {
  bool showAlert;
  String? alertMessage;

  CheckValidateResult({required this.showAlert, this.alertMessage});
}

/// 校验数据有效性
class CheckValidateUseCase extends UseCase<CheckValidateResult, CheckValidateParams> {
  @override
  Future<CheckValidateResult> call(CheckValidateParams params) async {
    final entry = params.entry;

    var showAlert = false;
    var alertMessage = '';

    // 如果数据无效，则显示提示
    if (entry.isValid == false) {
      showAlert = true;
      alertMessage = MessageConstants.inputError;
      return CheckValidateResult(showAlert: showAlert, alertMessage: alertMessage);
    }

    // 只读数据，并且必填项目
    if (entry.isReadOnly == true) {
      if (entry.isRequired == true) {
        if (params.scene == AfCustomizeViewScene.assetDetail ||
            params.scene == AfCustomizeViewScene.assetDetailEdit ||
            params.scene == AfCustomizeViewScene.assetNewCreate) {
          final defaultData = params.entry.defaultData;
          if (defaultData == null || defaultData.isEmpty || defaultData.trim().isEmpty) {
            if (entry.itemDisplayName?.isNotEmpty ?? false) {
              entry.setShowMessage('${entry.itemDisplayName}${MessageConstants.pleaseSet}');
            } else {
              entry.setShowMessage('${entry.itemName}${MessageConstants.pleaseSet}');
            }

            showAlert = true;
            alertMessage = MessageConstants.inputError;
            return CheckValidateResult(showAlert: showAlert, alertMessage: alertMessage);
          }
        }
      }

      // 对于通过 customizeLogic setValue 进来的 通货、数字不可编辑内容
      // 需要判断是否不符合规则，如果不符合的情况则弹出 error 信息并拦截下一步操作
      // 数字或通货类型输入位数检查
      final errorMessage = NumberValidator.validateNumericAndCurrencyField(entry);
      if (errorMessage.isNotEmpty) {
        entry.setShowMessage(errorMessage);
        showAlert = true;
        alertMessage = errorMessage;
        return CheckValidateResult(showAlert: showAlert, alertMessage: alertMessage);
      }
    }

    // 非只读数据
    if (entry.isReadOnly != true) {
      // 必填项目
      if (entry.isRequired == true) {
        final defaultData = entry.defaultData;

        // Master类型字段的特殊处理
        if (SharedItemTypeEnum.master.equals(entry.itemType)) {
          // Master字段验证：检查是否有masterId或者display数据
          bool isMasterDataEmpty = false;
          if (defaultData == null ||
              (defaultData is Map && defaultData.isEmpty) ||
              (defaultData is String && defaultData.trim().isEmpty)) {
            isMasterDataEmpty = true;
          } else if (defaultData is Map) {
            // 检查Master数据结构是否有效
            final masterId = defaultData['masterId'];
            final display = defaultData['display'];
            if ((masterId == null || masterId.toString().isEmpty) &&
                (display == null || (display is Map && display.isEmpty))) {
              isMasterDataEmpty = true;
            }
          }

          if (isMasterDataEmpty) {
            if (entry.itemDisplayName?.isNotEmpty ?? false) {
              entry.setShowMessage('${entry.itemDisplayName}${MessageConstants.pleaseSet}');
            } else {
              entry.setShowMessage('${entry.itemName}${MessageConstants.pleaseSet}');
            }

            showAlert = true;
            alertMessage = MessageConstants.inputError;
            return CheckValidateResult(showAlert: showAlert, alertMessage: alertMessage);
          }
        } else {
          // 非Master类型字段的常规验证
          if (defaultData == null || defaultData.isEmpty || (defaultData is String && defaultData.trim().isEmpty)) {
            if (entry.itemDisplayName?.isNotEmpty ?? false) {
              entry.setShowMessage('${entry.itemDisplayName}${MessageConstants.pleaseSet}');
            } else {
              entry.setShowMessage('${entry.itemName}${MessageConstants.pleaseSet}');
            }

            showAlert = true;
            alertMessage = MessageConstants.inputError;
            return CheckValidateResult(showAlert: showAlert, alertMessage: alertMessage);
          }
        }
      }

      // 检查复选框类型的必填项
      if (entry.isRequired == true && SharedItemTypeEnum.checkbox.equals(entry.itemType)) {
        if (entry.defaultData == '[]') {
          if (entry.itemDisplayName?.isNotEmpty ?? false) {
            entry.setShowMessage('${entry.itemDisplayName}${MessageConstants.pleaseSet}');
          } else {
            entry.setShowMessage('${entry.itemName}${MessageConstants.pleaseSet}');
          }

          showAlert = true;
          alertMessage = MessageConstants.inputError;
          return CheckValidateResult(showAlert: showAlert, alertMessage: alertMessage);
        }

        // 检查数据是否为空或0
        if (entry.defaultData == '' || entry.defaultData == null || entry.defaultData == '0') {
          if (entry.itemDisplayName?.isNotEmpty ?? false) {
            entry.setShowMessage('${entry.itemDisplayName}${MessageConstants.pleaseSet}');
          } else {
            entry.setShowMessage('${entry.itemName}${MessageConstants.pleaseSet}');
          }

          showAlert = true;
          alertMessage = MessageConstants.inputError;
          return CheckValidateResult(showAlert: showAlert, alertMessage: alertMessage);
        }
      }

      if (entry.defaultData != null) {
        // 桁数チェック
        final maxLength = entry.optionObject?.maxlength;
        if (maxLength != null && entry.defaultData is String) {
          final defaultDataStr = entry.defaultData as String;
          if (defaultDataStr.length > (int.tryParse(maxLength) ?? double.infinity)) {
            if (entry.itemDisplayName?.isNotEmpty ?? false) {
              entry.setShowMessage(
                '${entry.itemDisplayName}${MessageConstants.pleaseInputWithinCharacters.replaceAll('%s', maxLength)}',
              );
            } else {
              entry.setShowMessage(
                '${entry.itemName}${MessageConstants.pleaseInputWithinCharacters.replaceAll('%s', maxLength)}',
              );
            }

            showAlert = true;
            alertMessage = MessageConstants.inputError;
            return CheckValidateResult(showAlert: showAlert, alertMessage: alertMessage);
          }
        }

        // 数字或通货类型输入位数检查
        final errorMessage = NumberValidator.validateNumericAndCurrencyField(entry);
        if (errorMessage.isNotEmpty) {
          showAlert = true;
          alertMessage = errorMessage;
          entry.setShowMessage(errorMessage);
          return CheckValidateResult(showAlert: showAlert, alertMessage: alertMessage);
        }

        // 邮箱格式检查
        if (SharedItemTypeEnum.email.equals(entry.itemType)) {
          final emailRegex = RegExp(r'^(\w+|[-+.])*@\w+([-.]\w+)*\.\w+([-.]\w+)*$');
          if (!emailRegex.hasMatch(entry.defaultData ?? '')) {
            if (entry.itemDisplayName?.isNotEmpty ?? false) {
              entry.setShowMessage('${entry.itemDisplayName}${MessageConstants.formatIncorrect}');
            } else {
              entry.setShowMessage('${entry.itemName}${MessageConstants.formatIncorrect}');
            }

            showAlert = true;
            alertMessage = MessageConstants.inputError;
            return CheckValidateResult(showAlert: showAlert, alertMessage: alertMessage);
          }
        }
      }
    }

    entry.clearMessage();
    return CheckValidateResult(showAlert: showAlert, alertMessage: alertMessage);
  }
}

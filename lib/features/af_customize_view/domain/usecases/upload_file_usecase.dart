import 'dart:io';

import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/network/interceptors/response_interceptor.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/file_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:dio/dio.dart';

/// 上传状态枚举
enum UploadStatus {
  prepare, // 准备上传
  downloading, // 正在上传中
  success, // 上传成功
  error, // 上传失败
}

/// 上传文件参数
class UploadFileParams {
  final String filePath;
  final void Function(int count, int total)? onProgress;

  UploadFileParams({required this.filePath, this.onProgress});
}

/// 上传文件响应
class UploadFileResult {
  final String url;
  final String path;
  final String fileName;
  final String uploadUserName;
  final double size;

  /// 上传状态
  final UploadStatus status;

  UploadFileResult({
    required this.url,
    required this.path,
    required this.fileName,
    required this.uploadUserName,
    required this.size,
    required this.status,
  });
}

/// 上传文件用例
///
/// 用于处理通用文件上传相关的业务逻辑
/// - 读取文件内容为字节数组
/// - 获取S3上传URL
/// - 上传文件到S3服务器
/// - 返回上传结果(包含URL和路径)
class UploadFileUseCase extends UseCase<UploadFileResult, UploadFileParams> {
  final DioUtil dioUtil;
  final S3Repository s3Repository;

  UploadFileUseCase({required this.dioUtil, required this.s3Repository});

  /// 上传文件
  @override
  Future<UploadFileResult> call(UploadFileParams params) async {
    try {
      ResponseInterceptor.addSkipInterceptorPath('amazonaws.com');
      // 读取文件内容为字节数组
      final file = File(params.filePath);
      if (!await file.exists()) {
        throw BusinessException('文件不存在');
      }
      final fileBytes = await file.readAsBytes();

      // 计算并打印文件大小
      final fileSize = fileBytes.length;
      final readableSize = FileUtils.getReadableFileSize(fileSize);
      LogUtil.d('文件大小: $readableSize');

      // 获取文件后缀名（不带点）
      final String fileExtension = params.filePath.contains('.') ? params.filePath.split('.').last : '';

      // 获取 S3 上传 URL
      final s3UploadUrlResponse = await s3Repository.getS3UploadUrl(
        DateTime.now().microsecondsSinceEpoch.toString() + '.' + fileExtension,
      );
      LogUtil.d('获取 S3 上传 URL: ${s3UploadUrlResponse.toJson()}');

      if (!s3UploadUrlResponse.isSuccess() || s3UploadUrlResponse.data?.url == null) {
        throw BusinessException('获取上传URL失败: ${s3UploadUrlResponse.msg}');
      }

      // 使用新的 uploadBytes 方法上传
      final path = s3UploadUrlResponse.data?.url ?? '';
      LogUtil.d('上传路径：${path}');

      if (path.isEmpty) {
        throw BusinessException('上传路径无效');
      }

      // 上传文件到S3服务器
      try {
        await dioUtil.uploadBytes(
          path,
          bytes: fileBytes,
          onSendProgress: (count, total) {
            params.onProgress?.call(count, total);
          },
        );
      } catch (e) {
        if (e is DioException) {
          LogUtil.e('上传失败 - DioException: ${e.message}, type: ${e.type}, response: ${e.response?.data}');
          throw BusinessException('上传失败: ${e.message ?? "未知错误"}');
        }
        throw e;
      }

      // 处理响应数据
      final String resPath = s3UploadUrlResponse.data?.path ?? '';
      final String resUrl = s3UploadUrlResponse.data?.getUrl ?? '';

      if (resPath.isEmpty || resUrl.isEmpty) {
        throw BusinessException('获取返回URL失败');
      }

      //从传入的 filelPath 中截取 fileName，并且判断是否包含后缀，如果包含则去除
      String fileName = params.filePath.split('/').last;
      if (fileName.contains('.')) {
        fileName = fileName.split('.').first;
      }

      // 获取上传用户名
      final String uploadUserName =
          (StorageUtils.get<String>(StorageUtils.keyLastName) ?? '') +
          ' ' +
          (StorageUtils.get<String>(StorageUtils.keyFirstName) ?? '');

      return UploadFileResult(
        url: resUrl,
        path: resPath,
        fileName: fileName,
        uploadUserName: uploadUserName,
        size: fileSize.toDouble(),
        status: UploadStatus.success,
      );
    } catch (e) {
      LogUtil.e('上传失败：$e');
      throw BusinessException('$e');
    }
  }
}

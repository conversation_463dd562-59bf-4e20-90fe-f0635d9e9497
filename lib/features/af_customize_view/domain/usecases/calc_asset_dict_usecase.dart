import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/calculate_helper.dart';
import 'package:asset_force_mobile_v2/core/utils/expression_calculator.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/number_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/calculation_formula.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_calculate_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';

class CalcAssetDictParams {
  Map<String, List<RxAssetItemWrapper>>? assetDict;
  CalcAssetDictResult? calcResultObj;
  String? itemName;

  /// 构造函数
  ///
  /// 参数:
  /// * [assetDict] - 资产字典,键为资产ID,值为资产项目列表
  ///
  /// 说明:
  /// 1. [assetDict] 用于存储资产项目的映射关系
  CalcAssetDictParams(this.assetDict);

  /// 构造函数
  ///
  /// 参数:
  /// * [calcResultObj] - 计算结果对象,包含项目映射和计算项目列表
  /// * [itemName] - 需要更新的项目名称
  ///
  /// 说明:
  /// 1. 用于更新依赖项目时的构造
  /// 2. [calcResultObj] 存储上一次计算的结果
  /// 3. [itemName] 指定要更新的项目名称
  CalcAssetDictParams.calcObj(this.calcResultObj, this.itemName);
}

/// 计算资产字典的结果类
///
/// [itemMap] 存储所有项目的映射,key为项目名称,value为对应的项目包装器
/// [calculateItems] 存储所有计算类型的项目列表
class CalcAssetDictResult {
  /// 存储所有项目的映射
  final Map<String, RxAssetItemWrapper> itemMap;

  /// 存储所有计算类型的项目列表
  final List<RxAssetItemWrapper> calculateItems;

  /// 构造函数
  ///
  /// [itemMap] 项目映射
  /// [calculateItems] 计算项目列表
  CalcAssetDictResult({required this.itemMap, required this.calculateItems});
}

class CalcAssetDictUseCase extends UseCase<CalcAssetDictResult, CalcAssetDictParams> {
  final JsExecutor jsExecutor;

  CalcAssetDictUseCase(this.jsExecutor);

  @override
  Future<CalcAssetDictResult> call(CalcAssetDictParams params) async {
    // 构建 itemId 到 item 的映射，同时收集计算项目
    final Map<String, RxAssetItemWrapper> itemMap = {};
    final List<RxAssetItemWrapper> calculateItems = [];

    final assetDict = params.assetDict;
    if (assetDict == null && params.calcResultObj == null && params.itemName == null) {
      return CalcAssetDictResult(itemMap: {}, calculateItems: []);
    }

    // 将 map 的所有 value 组成新的列表数据
    // 并将数据放到新的 itemMap 中提高之后遍历时的查找性能
    // 将计算类型单独存储到计算列表中
    assetDict?.values.expand((items) => items).forEach((item) {
      itemMap[item.itemName ?? ''] = item;
      if (SharedItemTypeEnum.calculate.equals(item.itemType)) {
        calculateItems.add(item);
      }
    });

    // 如果存在最后一次请求参数的传入，则执行依赖项目更新
    final calcResultObj = params.calcResultObj;
    final itemName = params.itemName;
    if (calcResultObj != null && itemName != null && itemName != '') {
      _updateDependentCalculateItems(itemName, calcResultObj);
      return calcResultObj;
    }

    // 遍历全部的计算条目，并更新计算项目的值
    for (var calcItem in calculateItems) {
      try {
        // 获取依赖项目的值并更新
        await _updateCalculateItemValue(calcItem, itemMap);
      } catch (e) {
        LogUtil.e('计算出错: ${calcItem.itemDisplayName} - $e');
        calcItem.updateDefaultData('');
      }
    }
    return CalcAssetDictResult(itemMap: itemMap, calculateItems: calculateItems);
  }

  /// 更新依赖项变化后的计算项目
  ///
  /// 当非计算项目更新时，触发相关计算项目的数据更新
  ///
  /// 参数:
  /// * [itemName] - 更新项目的名称,用于查找依赖该项目的计算项目
  /// * [calcResult] - 计算资产字典的结果对象
  ///
  /// 说明:
  /// 1. 遍历所有计算项目,检查其依赖项
  /// 2. 如果依赖项包含更新的项目,则重新计算该计算项目的值
  /// 3. 计算失败时会将计算项目的值设为空字符串
  void _updateDependentCalculateItems(String itemName, CalcAssetDictResult calcResult) {
    // 遍历所有计算项目
    for (var calcItem in calcResult.calculateItems) {
      // 获取计算公式对象
      final formula = calcItem.calculationFormula;
      if (formula == null) continue;

      // 检查当前更新的项目是否是该计算项目的依赖项
      if (formula.dependencies.contains(itemName)) {
        try {
          // 获取计算表达式
          final calculateExpressions = CalculateHelper.getCalculate(formula.formula, calcResult.itemMap, false);

          // 获取计算项目的配置信息
          final optionObject = calcItem.optionObject;
          final calculateCommaDecimalPoint = NumberUtils.parseDecimalPoint(optionObject?.calculateCommaDecimalPoint);
          final calculateDecimalPoint = NumberUtils.parseDecimalPoint(optionObject?.calculateDecimalPoint);

          // 计算新的结果
          String calculatedResult = ExpressionCalculator.calculate(calculateExpressions, calculateDecimalPoint);
          calculatedResult = NumberUtils.formatNumberWithCommaAndDecimal(
            calculateCommaDecimalPoint,
            calculateDecimalPoint,
            calculatedResult,
            true,
          );

          LogUtil.d('重新计算 ${calcItem.itemDisplayName} ${calculatedResult}');
          // 更新计算项目的值
          calcItem.updateDefaultData(calculatedResult);
        } catch (e) {
          LogUtil.e('计算更新出错: ${calcItem.itemDisplayName} - $e');
          calcItem.updateDefaultData('');
        }
      }
    }
  }

  /// 更新计算项目的值
  ///
  /// 参数:
  /// * [calcItem] - 需要更新的计算项目
  /// * [itemMap] - 所有项目的映射,用于查找依赖项的值
  ///
  /// 说明:
  /// 1. 获取计算项目的配置信息,包括计算类型、计算表达式等
  /// 2. 根据计算类型执行不同的计算逻辑:
  ///   - time: 时间计算(暂未实现)
  ///   - digital: 数字计算
  /// 3. 对于数字计算:
  ///   - 设置计算公式对象,包含表达式和依赖项
  ///   - 解析计算表达式
  ///   - 执行计算并格式化结果
  ///   - 更新计算项目的值
  Future<void> _updateCalculateItemValue(RxAssetItemWrapper calcItem, Map<String, RxAssetItemWrapper> itemMap) async {
    final OptionObjModel? optionObject = calcItem.optionObject;

    // 计算种类
    final String? calculateType = optionObject?.calculateType;

    // 计算表达式
    final String? calculate = optionObject?.calculate;

    if (calculate != null && calculateType != null) {
      if (SharedCalculateValueItemEnum.time.equals(calculateType)) {
        // 时间计算
        await _handleTimeCalculation(calcItem, itemMap, calculate, optionObject);
      } else if (SharedCalculateValueItemEnum.digital.equals(calculateType) ||
          SharedCalculateValueItemEnum.currency.equals(calculateType)) {
        // 数字、通货计算
        _handleDigitalCalculation(calcItem, itemMap, calculate, optionObject);
      } else if (SharedCalculateValueItemEnum.boolValue.equals(calculateType)) {
        // 布尔值计算
        _handleBoolValueCalculation(calcItem, itemMap, calculate, optionObject);
      } else if (SharedCalculateValueItemEnum.text.equals(calculateType)) {
        // 文本计算
        _handleTextCalculation(calcItem, itemMap, calculate, optionObject);
      }
    }
  }

  /// 处理数字计算逻辑
  ///
  /// 参数:
  /// * [calcItem] - 计算项目
  /// * [itemMap] - 所有项目的映射
  /// * [calculate] - 计算表达式
  /// * [optionObject] - 计算项目的配置信息
  void _handleDigitalCalculation(
    RxAssetItemWrapper calcItem,
    Map<String, RxAssetItemWrapper> itemMap,
    String calculate,
    OptionObjModel? optionObject,
  ) {
    // 使用 NumberUtils 中的方法
    final int calculateCommaDecimalPoint = NumberUtils.parseDecimalPoint(optionObject?.calculateCommaDecimalPoint);

    final int calculateDecimalPoint = NumberUtils.parseDecimalPoint(optionObject?.calculateDecimalPoint);

    // 设置计算公式对象
    calcItem.calculationFormula = CalculationFormula(
      formula: calculate,
      dependencies: CalculateHelper.extractItemNames(calculate),
    );
    // 计算表达式
    final calculateExpressions = CalculateHelper.getCalculate(calculate, itemMap, false);
    // 表达式计算结果
    String expressionCalculationResult = ExpressionCalculator.calculate(calculateExpressions, calculateDecimalPoint);
    expressionCalculationResult = NumberUtils.formatNumberWithCommaAndDecimal(
      calculateCommaDecimalPoint,
      calculateDecimalPoint,
      expressionCalculationResult,
      true,
    );
    calcItem.updateDefaultData(expressionCalculationResult);
  }

  /// 处理时间计算逻辑
  ///
  /// 参数:
  /// * [calcItem] - 计算项目,用于存储计算结果的包装器
  /// * [itemMap] - 所有项目的映射,包含计算所需的数据
  /// * [calculate] - 计算表达式,定义如何计算时间
  /// * [optionObject] - 计算项目的配置信息,包含计算相关的选项
  Future<void> _handleTimeCalculation(
    RxAssetItemWrapper calcItem,
    Map<String, RxAssetItemWrapper> itemMap,
    String calculate,
    OptionObjModel? optionObject,
  ) async {
    // 将 itemMap 转换为 Map<String,AssetIteListModel> 类型
    final Map<String, dynamic> convertedItemMap = {};
    itemMap.forEach((key, value) {
      // todo : 优化 获取原始模型
      convertedItemMap[key] = value.model;
    });
    final String calculateExpressions = CalculateHelper.getCalculate(calculate, itemMap, false);
    // 注意：这里需要设置控制器或使用其他方式提供数据
    // 当前的计算逻辑可能需要重构以适应新的架构
    final calcResult = await jsExecutor.eval(calculateExpressions, JsCodeLocal.calculateJavascript, needsResult: true);
    calcItem.updateDefaultData(calcResult);
  }

  /// 处理布尔值计算逻辑
  ///
  /// 参数:
  /// * [calcItem] - 计算项目
  /// * [itemMap] - 所有项目的映射
  /// * [calculate] - 计算表达式
  /// * [optionObject] - 计算项目的配置信息
  ///
  /// 说明:
  /// 1. 获取计算表达式,并替换占位符
  /// 2. 执行计算表达式,获取计算结果
  /// 3. 更新计算项目的值
  /// 4. 处理计算结果为空时的情况
  Future<void> _handleBoolValueCalculation(
    RxAssetItemWrapper calcItem,
    Map<String, RxAssetItemWrapper> itemMap,
    String calculate,
    OptionObjModel? optionObject,
  ) async {
    String calculateExpressions = CalculateHelper.getCalculate(calculate, itemMap, false);
    calculateExpressions = calculateExpressions.replaceAll(',', '');
    final String calcResult = await jsExecutor.eval(
      calculateExpressions,
      JsCodeLocal.calculateJavascript,
      needsResult: true,
    );
    if (calcResult == '') {
      calcItem.updateDefaultData('');
      return;
    }
    calcItem.updateDefaultData(calcResult == 'true' ? 'はい' : 'いいえ');
  }

  /// 处理文本计算逻辑
  ///
  /// 参数:
  /// * [calcItem] - 计算项目
  /// * [itemMap] - 所有项目的映射
  /// * [calculate] - 计算表达式
  /// * [optionObject] - 计算项目的配置信息
  ///
  /// 说明:
  /// 1. 获取计算表达式,并替换占位符
  /// 2. 执行计算表达式,获取计算结果
  /// 3. 更新计算项目的值
  /// 4. 处理计算结果为空时的情况
  void _handleTextCalculation(
    RxAssetItemWrapper calcItem,
    Map<String, RxAssetItemWrapper> itemMap,
    String calculate,
    OptionObjModel? optionObject,
  ) {
    String calculateExpressions = CalculateHelper.getCalculate(calculate, itemMap, false);
    calculateExpressions = calculateExpressions.replaceAll('\'', '');
    calcItem.updateDefaultData(calculateExpressions);
  }
}

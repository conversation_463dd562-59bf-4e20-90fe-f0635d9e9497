import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';

class GetTurlUseCase extends UseCase<String, String> {
  final S3Repository s3Repository;

  GetTurlUseCase({required this.s3Repository});

  @override
  Future<String> call(String url) async {
    try {
      return await s3Repository.getTurl(url);
    } catch (e) {
      // 如果获取临时URL失败，返回原始URL
      return url;
    }
  }
}

import 'dart:typed_data';

import 'package:asset_force_mobile_v2/core/network/interceptors/response_interceptor.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/file_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_s3_upload_url_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:dio/dio.dart';
import 'package:image_picker/image_picker.dart';

/// 上传图片参数
class UploadImageParams {
  final XFile file;

  UploadImageParams({required this.file});
}

/// 上传图片响应
class UploadImageResult {
  final String url;
  final String path;

  UploadImageResult({required this.url, required this.path});
}

/// 上传图片用例
///
/// 用于处理图片上传相关的业务逻辑
/// - 读取图片文件内容
/// - 获取S3上传URL
/// - 上传图片到S3服务器
/// - 返回上传结果(包含URL和路径)
class UploadImageUseCase extends UseCase<UploadImageResult, UploadImageParams> {
  final DioUtil dioUtil;
  final S3Repository s3Repository;

  UploadImageUseCase({required this.dioUtil, required this.s3Repository});

  /// 上传图片
  @override
  Future<UploadImageResult> call(UploadImageParams params) async {
    try {
      // 跳过ResponseInterceptor处理S3请求，避免类型错误
      ResponseInterceptor.addSkipInterceptorPath('amazonaws.com');

      // 读取文件内容为字节数组
      Uint8List bytes = await params.file.readAsBytes();

      // 计算并打印文件大小
      final fileSize = bytes.length;
      final readableSize = FileUtils.getReadableFileSize(fileSize);
      LogUtil.d('文件大小: $readableSize');

      // 判断是否需要压缩
      if (FileUtils.needCompress(fileSize)) {
        // 压缩图片
        final compressedBytes = await FileUtils.compressImage(params.file);
        final compressedFileSize = compressedBytes.length;
        final compressedReadableSize = FileUtils.getReadableFileSize(compressedFileSize);
        LogUtil.d('压缩后文件大小: $compressedReadableSize');
        bytes = compressedBytes;
      }

      // 获取 S3 上传 URL
      final SharedS3UploadUrlResponse s3UploadUrlResponse = await s3Repository.getS3UploadUrl(
        DateTime.now().microsecondsSinceEpoch.toString(),
      );
      LogUtil.d('获取 S3 上传 URL: ${s3UploadUrlResponse.toJson()}');

      if (!s3UploadUrlResponse.isSuccess() || s3UploadUrlResponse.data?.url == null) {
        throw Exception('获取上传URL失败: ${s3UploadUrlResponse.msg}');
      }

      // 使用新的 uploadBytes 方法上传
      final path = s3UploadUrlResponse.getUploadUrl();
      LogUtil.d('上传路径：${path}');

      if (path.isEmpty) {
        throw Exception('上传路径无效');
      }

      // 上传图片到S3服务器
      try {
        await dioUtil.uploadBytes(
          path,
          bytes: bytes,
          onSendProgress: (count, total) {
            final progress = (count / total * 100).toStringAsFixed(2);
            LogUtil.d('上传进度: $progress%');
          },
        );
      } catch (e) {
        if (e is DioException) {
          LogUtil.e('上传失败 - DioException: ${e.message}, type: ${e.type}, response: ${e.response?.data}');
          throw Exception('上传失败: ${e.message ?? "未知错误"}');
        }
        throw e;
      }

      // 处理响应数据
      final String resPath = s3UploadUrlResponse.data?.path ?? '';
      final String resUrl = s3UploadUrlResponse.data?.getUrl ?? '';

      if (resPath.isEmpty || resUrl.isEmpty) {
        throw Exception('获取返回URL失败');
      }

      return UploadImageResult(url: resUrl, path: resPath);
    } catch (e) {
      LogUtil.e('上传失败：$e');
      throw Exception('上传失败：$e');
    }
  }
}

import 'dart:convert';

import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/repositories/master_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/master_detail_model.dart';

/// 获取Master默认值用例
class GetMasterDefaultValueUseCase extends UseCase<GetMasterDefaultValueResult, GetMasterDefaultValueParams> {
  final MasterRepository masterRepository;

  final Map<int, dynamic> masterResult = {};

  GetMasterDefaultValueUseCase({required this.masterRepository});

  @override
  Future<GetMasterDefaultValueResult> call(GetMasterDefaultValueParams params) async {
    String masterListDefaultValue = '';
    final itemName = params.itemName;
    final masterTypeId = params.masterTypeId;
    final masterId = params.masterId;

    if (masterId != null && masterTypeId != null && itemName != null) {
      // 优先从缓存中获取
      List<MasterDetailModel> masterDetailList;

      // 检查缓存是否存在有效数据
      if (masterResult.containsKey(masterTypeId) && masterResult[masterTypeId] is List<MasterDetailModel>) {
        // 使用缓存数据
        masterDetailList = masterResult[masterTypeId] as List<MasterDetailModel>;
      } else {
        // 缓存不存在或无效，执行网络请求获取 master detail 列表数据
        masterDetailList = await masterRepository.getMasterInfoById(masterTypeId);
      }

      for (final master in masterDetailList) {
        if (masterId.toString().isNotEmpty && itemName.isNotEmpty) {
          LogUtil.d('masterId: $masterId, itemName: $itemName, masterText: ${master.masterText}');
          if (master.masterId == masterId) {
            if (master.masterText != null && master.masterText!.isNotEmpty) {
              try {
                final Map<String, dynamic> masterText = jsonDecode(master.masterText!);
                masterListDefaultValue = masterText[itemName]?.toString() ?? '';
                break; // 找到匹配项后退出循环
              } catch (e) {
                // JSON 解析失败时继续下一个
                continue;
              }
            }
          }
        }
      }

      // 更新缓存
      masterResult[masterTypeId] = masterDetailList;
    }

    return GetMasterDefaultValueResult(masterResult: masterResult, masterListDefaultValue: masterListDefaultValue);
  }
}

/// 获取Master默认值参数
class GetMasterDefaultValueParams {
  final int? masterTypeId;
  final int? masterId;
  final String? itemName;

  GetMasterDefaultValueParams({required this.masterTypeId, required this.masterId, required this.itemName});
}

/// 获取Master默认值结果
class GetMasterDefaultValueResult {
  /// 参与请求的缓存
  final Map<int, dynamic> masterResult;

  /// 用于展示的默认值
  final String masterListDefaultValue;

  GetMasterDefaultValueResult({required this.masterResult, required this.masterListDefaultValue});

  @override
  String toString() {
    return 'GetMasterDefaultValueResult{masterResult: $masterResult, masterListDefaultValue: $masterListDefaultValue}';
  }
}

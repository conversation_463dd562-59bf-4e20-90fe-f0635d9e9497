import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/processors/appur_info_summary_processor.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/processors/base_item_processor.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/processors/currency_processor.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/processors/date_processor.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/processors/image_item_processor.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/processors/master_processor.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/processors/number_processor.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';

class PrepareAssetDataParams {
  Map<String, List<RxAssetItemWrapper>> assetDict;
  AfCustomizeViewScene scene;

  bool isShowMasterDefaultValue;
  PrepareAssetDataParams({required this.assetDict, required this.scene, required this.isShowMasterDefaultValue});
}

/// 预处理资产数据
class PrepareAssetDataUseCase extends UseCase<void, PrepareAssetDataParams> {
  @override
  Future<void> call(PrepareAssetDataParams params) async {
    prepareAssetDict(params);
  }

  /// 预处理 assetDict 中的数据
  void prepareAssetDict(PrepareAssetDataParams param) {
    for (var items in param.assetDict.values) {
      for (var item in items) {
        _processItem(item, param);
      }
    }
  }

  /// 处理单个资产项目
  void _processItem(RxAssetItemWrapper item, PrepareAssetDataParams param) {
    final itemProcessor = _getItemProcessor(item.itemType);
    itemProcessor?.process(
      ProcessorParams(
        item: item,
        scene: param.scene,
        assetDict: param.assetDict,
        isShowMasterDefaultValue: param.isShowMasterDefaultValue,
      ),
    );
  }

  /// 获取对应类型的处理器
  ItemProcessor? _getItemProcessor(String? itemType) {
    switch (itemType) {
      case 'homeImage':
      case 'image':
        return ImageItemProcessor();
      case 'number':
        return NumberItemProcessor();
      case 'appurInfoSummary':
        return AppurInfoSummaryProcessor();
      case 'currency':
        return CurrencyProcessor();
      case 'date':
        return DateProcessor();
      case 'master':
        return MasterProcessor();
      default:
        return null;
    }
  }
}

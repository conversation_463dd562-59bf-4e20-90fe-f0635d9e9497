import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_relation_list_response.dart';

/// 关联资产模型类
///
/// 用于表示一个关联资产的数据模型。
/// 包含关联资产的基本信息,如ID、名称、数量和类型等。
class RelationAssetModel {
  /// 关联资产种类ID
  final String assetTypeId;

  /// 关联资产种类名称
  final String assetTypeName;

  /// 关联资产数量
  final int count;

  /// 关联资产类型
  final List<AssetRelationItem> assetRelationItems;

  /// 构造函数
  ///
  /// [assetTypeId] - 关联资产种类ID
  /// [assetTypeName] - 关联资产种类名称
  /// [count] - 关联资产数量
  /// [assetRelationItems] - 关联资产列表
  RelationAssetModel({
    required this.assetTypeId,
    required this.assetTypeName,
    required this.count,
    required this.assetRelationItems,
  });
}

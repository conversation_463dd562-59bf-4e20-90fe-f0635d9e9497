import 'package:intl/intl.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/button_state.dart';

/// 时间选择器结果实体类
class TimeDisplayResult {
  /// 按钮状态值
  final ButtonState valueState;

  /// 时间对象（年、月、日、时、分）
  final Map<String, String> valueTime;

  /// 格式化后的日期时间字符串
  final String? formattedDate;

  /// 构造函数
  TimeDisplayResult({required this.valueState, required this.valueTime, this.formattedDate});

  /// 获取年份
  String get year => valueTime['yearValues'] ?? '';

  /// 获取月份
  String get month => valueTime['monthValues'] ?? '';

  /// 获取日期
  String get day => valueTime['dayValues'] ?? '';

  /// 获取小时
  String get hour => valueTime['hourValues'] ?? '';

  /// 获取分钟
  String get minute => valueTime['minuteValues'] ?? '';

  /// 是否为清除操作
  bool get isClear => valueState == ButtonState.clear;

  /// 是否为今日操作
  bool get isToday => valueState == ButtonState.today;

  /// 是否为完成操作
  bool get isFinished => valueState == ButtonState.finished;

  /// 是否为取消操作
  bool get isCancel => valueState == ButtonState.cancel;

  /// 尝试转换为DateTime对象
  DateTime? toDateTime() {
    try {
      if (formattedDate != null && formattedDate!.isNotEmpty) {
        String dateStr = formattedDate!.replaceAll('/', '-');
        if (!dateStr.contains(':')) {
          dateStr += ' 00:00';
        }
        return DateFormat('yyyy-MM-dd HH:mm').parse(dateStr);
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  @override
  String toString() {
    return 'TimeDisplayResult(valueState: $valueState, formattedDate: $formattedDate)';
  }
}

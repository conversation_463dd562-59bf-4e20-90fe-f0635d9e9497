import 'dart:convert';

import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/item_model_base.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';

/// ReservationItemCommon适配器
///
/// 将 [ReservationItemCommon] 转换为实现 [ItemModelBase] 接口的适配器
class ReservationItemAdapter implements ItemModelBase {
  final ReservationItemCommon _model;

  /// 存储额外的属性，这些属性在原始模型中不存在
  String? _masterId;
  String? _unit;
  String? _percentage;
  dynamic _itemValue;
  bool? _isValid;
  String? _isEditPermissions = '1';
  OptionObjModel? _optionObject;

  ReservationItemAdapter(this._model) {
    if (_model.option != null && _model.option!.isNotEmpty) {
      try {
        _optionObject = OptionObjModel.fromJson(jsonDecode(_model.option!));
      } catch (e) {
        LogUtil.d('解析option字符串失败: $e');
      }
    }
  }

  @override
  int? get itemId => _model.itemId;

  @override
  String? get itemDisplayName => _model.itemDisplayName;

  @override
  String? get itemName => _model.itemLabel;

  @override
  String? get itemType => _model.itemType;

  @override
  dynamic get defaultData => _model.defaultData;
  @override
  set defaultData(dynamic value) {
    // 检查_model是否具有defaultData属性再进行赋值，避免调用不存在的setter
    if (_model.runtimeType == ReservationItemCommon) {
      try {
        _model.defaultData = value;
      } catch (e) {
        // 异常捕获，防止因为反射调用失败导致程序崩溃
        LogUtil.d('设置defaultData失败: $e');
      }
    }
  }

  @override
  dynamic get itemValue => _itemValue ?? _model.itemValObj;
  @override
  set itemValue(dynamic value) => _itemValue = value;

  @override
  String? get sysSetFlg => '0';

  @override
  String? get inputFlg => _model.isRequired;

  @override
  bool? get isValid => _isValid;
  @override
  set isValid(bool? value) => _isValid = value;

  @override
  String? get isEditPermissions => _isEditPermissions;
  @override
  set isEditPermissions(String? value) => _isEditPermissions = value;

  @override
  OptionObjModel? get optionObject {
    if (_optionObject != null) {
      return _optionObject;
    }
    if (SharedItemTypeEnum.master.equals(itemType)) {
      final tempDisplay = _model.defaultData is Map<String, dynamic> ? _model.defaultData['display'] : null;

      final decodedItemValue = jsonDecode(_model.itemVal ?? '{}');
      _model.itemValObj = decodedItemValue;
      _optionObject = OptionObjModel.fromJson(decodedItemValue);
      _optionObject?.masterDisplayItems?.forEach((element) {
        element.itemDisplayName = element.itemName ?? '';
        if (tempDisplay is String) {
          element.itemValue = tempDisplay;
        }
      });
      return _optionObject;
    }
    // final model = OptionObjModel();
    // final itemsJson = _model.itemValObj?['masterDisplayItems'];
    // final items = (itemsJson as List<dynamic>?)?.map((e) => MasterDisplayItemModel.fromJson(e)).toList() ??
    //     <MasterDisplayItemModel>[];
    // items.forEach((element) {
    //   element.itemDisplayName = element.itemName ?? '';
    // });
    // model.masterDisplayItems = items;
    // model.masterTypeId = _model.itemValObj?['masterTypeId'];
    return _optionObject;
  }

  @override
  set optionObject(OptionObjModel? value) => _optionObject = value;

  @override
  dynamic get valueForShow => _model.defaultData;
  @override
  set valueForShow(dynamic value) => _model.defaultData = value;

  @override
  String? get masterId => _masterId;
  @override
  set masterId(String? value) => _masterId = value;

  @override
  String? get unit => _unit;
  @override
  set unit(String? value) => _unit = value;

  @override
  String? get percentage => _percentage;
  @override
  set percentage(String? value) => _percentage = value;

  /// 获取原始模型
  ReservationItemCommon get model => _model;

  @override
  get itemValObj => _model.itemValObj;
}

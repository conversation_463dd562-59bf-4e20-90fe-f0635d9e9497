import 'dart:convert';

import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/item_model_base.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart';

/// SharedLayoutSetting适配器
///
/// 将 [SharedLayoutSetting] 转换为实现 [ItemModelBase] 接口的适配器
/// 使用场景：
/// 1. 履历情报
class SharedLayoutSettingAdapter implements ItemModelBase {
  final SharedLayoutSetting _model;

  String? _unit;
  String? _percentage;
  dynamic _itemValue;
  bool? _isValid;
  String? _isEditPermissions = '1';
  OptionObjModel? _optionObject;

  SharedLayoutSettingAdapter(this._model);

  @override
  set defaultData(value) {
    _model.defaultData = value;
  }

  @override
  get defaultData => _model.defaultData;

  @override
  set isValid(bool? value) {
    _isValid = value;
  }

  @override
  bool? get isValid => _isValid;

  @override
  OptionObjModel? get optionObject {
    if (_model.option != null && _model.option!.isNotEmpty && _optionObject == null) {
      _optionObject = OptionObjModel.fromJson(jsonDecode(_model.option!));
    }
    return _optionObject;
  }

  @override
  set optionObject(OptionObjModel? value) {
    _optionObject = value;
  }

  @override
  set percentage(String? value) {
    _percentage = value;
  }

  @override
  String? get percentage => _percentage;

  @override
  set unit(String? value) {
    _unit = value;
  }

  @override
  String? get unit => _unit;

  @override
  dynamic get valueForShow => _model.valueForShow;

  @override
  set valueForShow(dynamic value) => _model.valueForShow = value;

  @override
  set masterId(String? value) {
    _model.masterId = value;
  }

  @override
  String? get masterId => _model.masterId;

  @override
  String? get inputFlg => _model.inputFlg;

  @override
  String? get itemDisplayName => _model.itemDisplayName;

  @override
  int? get itemId => _model.itemId;

  @override
  String? get itemName => _model.itemName;

  @override
  String? get itemType => _model.itemType;

  @override
  get itemValObj => Map();

  @override
  String? get sysSetFlg => _model.sysSetFlg;

  @override
  dynamic get itemValue => _itemValue;
  @override
  set itemValue(dynamic value) => _itemValue = value;

  @override
  String? get isEditPermissions => _isEditPermissions;
  @override
  set isEditPermissions(String? value) => _isEditPermissions = value;
}

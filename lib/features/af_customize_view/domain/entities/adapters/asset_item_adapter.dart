import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/item_model_base.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';

/// AssetItemListModel适配器
///
/// 将 [AssetItemListModel] 转换为实现 [ItemModelBase] 接口的适配器
class AssetItemAdapter implements ItemModelBase {
  final AssetItemListModel _model;

  AssetItemAdapter(this._model);

  @override
  int? get itemId => _model.itemId;

  @override
  String? get itemDisplayName => _model.itemDisplayName;

  @override
  String? get itemName => _model.itemName;

  @override
  String? get itemType => _model.itemType;

  @override
  dynamic get defaultData => _model.defaultData;
  @override
  set defaultData(dynamic value) => _model.defaultData = value;

  @override
  dynamic get itemValue => _model.itemValue;
  @override
  set itemValue(dynamic value) => _model.itemValue = value;

  @override
  String? get sysSetFlg => _model.sysSetFlg;

  @override
  String? get inputFlg => _model.inputFlg;

  @override
  bool? get isValid => _model.isValid;
  @override
  set isValid(bool? value) => _model.isValid = value;

  @override
  String? get isEditPermissions => _model.isEditPermissions;
  @override
  set isEditPermissions(String? value) => _model.isEditPermissions = value;

  @override
  OptionObjModel? get optionObject => _model.optionObject;
  @override
  set optionObject(OptionObjModel? value) => _model.optionObject = value;

  @override
  dynamic get valueForShow => _model.valueForShow;
  @override
  set valueForShow(dynamic value) => _model.valueForShow = value;

  @override
  String? get masterId => _model.masterId;
  @override
  set masterId(String? value) => _model.masterId = value;

  @override
  String? get unit => _model.unit;
  @override
  set unit(String? value) => _model.unit = value;

  @override
  String? get percentage => _model.percentage;
  @override
  set percentage(String? value) => _model.percentage = value;

  /// 获取原始模型
  AssetItemListModel get model => _model;

  @override
  get itemValObj => Map();

  /// 增加tojson函数，将整个assetItemadapter转为jsonnn

  Map<String, dynamic> toJson() {
    return {
      'itemId': itemId,
      'itemDisplayName': itemDisplayName,
      'itemName': itemName,
      'itemType': itemType,
      'defaultData': defaultData,
      'itemValue': itemValue,
      'sysSetFlg': sysSetFlg,
      'inputFlg': inputFlg,
      'isValid': isValid,
      'isEditPermissions': isEditPermissions,
      'optionObject': optionObject?.toJson(),
      'valueForShow': valueForShow,
      'masterId': masterId,
      'unit': unit,
      'percentage': percentage,
      'itemValObj': itemValObj,
      'model': model.toJson(),
      'isShowMessage': model.isShowMessage,
      'showMessage': model.showMessage,
    };
  }
}

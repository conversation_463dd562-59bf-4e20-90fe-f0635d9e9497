import 'dart:convert';

import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/item_model_base.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';

/// EventTypeItemAdapter适配器
///
/// 将 [EventTypeItem] 转换为实现 [ItemModelBase] 接口的适配器
class EventTypeItemAdapter implements ItemModelBase {
  final EventTypeItem _model;
  String? _masterId;
  String? _unit;
  String? _percentage;
  String? _isEditPermissions = '1';
  bool? _isValid;
  OptionObjModel? _optionObject;
  dynamic _itemValue;

  EventTypeItemAdapter(this._model) {
    if (_model.option != null && _model.option!.isNotEmpty) {
      try {
        _optionObject = OptionObjModel.fromJson(jsonDecode(_model.option!));
      } catch (e) {
        LogUtil.d('解析option字符串失败: $e');
      }
    }
  }

  @override
  dynamic get defaultData => _model.defaultData;

  @override
  set defaultData(dynamic value) => _model.defaultData = value;

  @override
  String? get isEditPermissions => _isEditPermissions;

  @override
  set isEditPermissions(String? value) => _isEditPermissions = value;

  @override
  bool? get isValid => _isValid;

  @override
  set isValid(bool? value) => _isValid = value;

  @override
  dynamic get itemValue => _itemValue;

  @override
  set itemValue(dynamic value) => _itemValue = value;

  @override
  String? get masterId => _masterId;

  @override
  set masterId(String? value) => _masterId = value;

  @override
  OptionObjModel? get optionObject {
    if (_optionObject != null) {
      return _optionObject;
    }
    final decodedItemValue = jsonDecode(_model.itemVal ?? '{}');
    _model.itemValObj = decodedItemValue;
    _optionObject = OptionObjModel.fromJson(decodedItemValue);
    _optionObject?.masterDisplayItems?.forEach((element) {
      element.itemDisplayName = element.itemName ?? '';
      final tempDisplay = _model.defaultData is Map<String, dynamic> ? _model.defaultData['display'] : null;
      if (tempDisplay is String) {
        element.itemValue = tempDisplay;
      }
    });
    return _optionObject;
  }

  @override
  set optionObject(OptionObjModel? value) => _optionObject = value;

  @override
  String? get percentage => _percentage;

  @override
  set percentage(String? value) => _percentage = value;

  @override
  String? get unit => _unit;

  @override
  set unit(String? value) => _unit = value;

  @override
  dynamic get valueForShow => _model.defaultData;

  @override
  set valueForShow(dynamic value) => _model.defaultData = value;

  @override
  String? get inputFlg => _model.isRequired;

  @override
  String? get itemDisplayName => _model.itemLabel;

  @override
  String? get itemName => _model.itemLabel;

  @override
  String? get itemType => _model.itemType;

  @override
  int? get itemId => _model.itemId;

  @override
  get itemValObj => _model.itemValObj;

  @override
  String? get sysSetFlg => '0';
}

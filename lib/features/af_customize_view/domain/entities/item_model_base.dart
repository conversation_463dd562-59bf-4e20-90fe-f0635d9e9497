import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';

/// 定义通用的数据模型接口，用于统一支持不同类型的数据模型
///
/// 该接口描述了数据模型需要支持的基本属性和方法
/// 不同类型的数据模型类可以实现该接口，确保它们可以被 [RxAssetItemWrapper] 包装使用
abstract class ItemModelBase {
  /// 获取项目ID
  int? get itemId;

  /// 获取项目显示名称
  String? get itemDisplayName;

  /// 获取项目名称
  String? get itemName;

  /// 获取项目类型
  String? get itemType;

  /// 获取默认数据
  dynamic get defaultData;
  set defaultData(dynamic value);

  /// 获取项目值
  dynamic get itemValue;
  set itemValue(dynamic value);

  /// 获取系统设置标志
  String? get sysSetFlg;

  /// 获取输入标志
  String? get inputFlg;

  /// 获取是否已经校验过
  bool? get isValid;
  set isValid(bool? value);

  /// 获取编辑权限
  String? get isEditPermissions;
  set isEditPermissions(String? value);

  /// 获取选项对象
  OptionObjModel? get optionObject;
  set optionObject(OptionObjModel? value);

  /// 获取显示值
  dynamic get valueForShow;
  set valueForShow(dynamic value);

  /// 获取Master项目ID
  String? get masterId;
  set masterId(String? value);

  /// 获取单位
  String? get unit;
  set unit(String? value);

  /// 获取百分比
  String? get percentage;
  set percentage(String? value);

  /// 获取计算类型
  dynamic get itemValObj;
}

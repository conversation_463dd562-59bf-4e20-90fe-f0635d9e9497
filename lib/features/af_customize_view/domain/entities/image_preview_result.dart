import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_image_model.dart';

/// 图片预览页面返回结果
///
/// 用于在图片预览页面和编辑页面之间传递数据
/// 包含图片操作的结果信息和被删除的图片URL列表
class ImagePreviewResult {
  /// 是否设置了首页图片
  /// - true: 设置了首页图片
  /// - false: 取消了首页图片设置
  /// - null: 没有进行首页图片操作
  final bool? isHomeImageSet;

  /// 选中的图片索引
  final int? selectedIndex;

  /// 操作消息
  final String? message;

  /// 更新后的图片模型列表
  final List<RxImageModel>? imageModels;

  /// 被删除的图片URL列表
  ///
  /// 记录在预览页面中被用户删除的图片原始URL
  /// 这些URL将用于后续的S3文件清理操作
  ///
  /// 特点：
  /// - 只记录原始URL，不包含临时URL
  /// - 用于S3存储空间的清理
  /// - 支持批量删除场景
  final List<String>? deletedImageUrls;

  ImagePreviewResult({this.isHomeImageSet, this.selectedIndex, this.message, this.imageModels, this.deletedImageUrls});
}

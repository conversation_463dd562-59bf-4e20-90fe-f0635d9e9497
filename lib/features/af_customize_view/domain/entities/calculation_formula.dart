/// CalculationFormula 是一个计算公式实体类
/// 用于存储计算公式及其依赖项的信息
///
/// 主要功能:
/// - 存储原始计算公式字符串
/// - 维护计算公式所依赖的项目名称列表
class CalculationFormula {
  /// 原始计算公式字符串
  /// 包含了完整的计算表达式,其中可能包含被反引号包裹的项目名称作为变量
  final String formula;

  /// 计算公式依赖的项目名称列表
  /// 存储了公式中所有被反引号包裹的项目名称
  /// 这些项目的值变化会影响计算结果
  final List<String> dependencies;

  /// 构造函数
  ///
  /// 参数:
  /// - [formula] 计算公式字符串
  /// - [dependencies] 依赖项目名称列表
  CalculationFormula({required this.formula, required this.dependencies});
}

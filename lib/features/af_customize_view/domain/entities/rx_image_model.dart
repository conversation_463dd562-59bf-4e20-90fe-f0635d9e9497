import 'package:asset_force_mobile_v2/features/af_customize_view/data/models/image_model.dart';
import 'package:get/get.dart';

class RxImageModel {
  final RxString url = RxString('');
  final RxString turl = RxString('');
  final RxBool loaded = RxBool(false);
  final RxString fileName = RxString('');
  final RxString uploadDate = RxString('');
  final RxBool isHomeImage = RxBool(false);

  /// 图片加载重试计数器
  ///
  /// 用于跟踪图片加载失败后的重试次数，防止无限重试
  /// - 0: 未重试过
  /// - 1: 已重试一次，不再进行后续重试
  int retryCount = 0;

  /// 是否正在重试加载
  ///
  /// 响应式布尔值，用于控制重试过程中的UI状态
  /// - true: 正在重试，显示加载指示器
  /// - false: 未在重试状态
  final RxBool isRetrying = RxBool(false);

  /// 是否正在获取临时URL
  ///
  /// 防止并发请求同一个图片的临时URL
  /// - true: 正在获取中，避免重复请求
  /// - false: 未在获取状态
  bool isGettingTurl = false;

  RxImageModel({
    String? url,
    String? turl,
    bool? loaded,
    String? fileName,
    String? uploadDate,
    bool? isHomeImage,
    int? retryCount,
    bool? isRetrying,
    bool? isGettingTurl,
  }) {
    this.url.value = url ?? '';
    this.turl.value = turl ?? '';
    this.loaded.value = loaded ?? false;
    this.fileName.value = fileName ?? '';
    this.uploadDate.value = uploadDate ?? '';
    this.isHomeImage.value = isHomeImage ?? false;
    this.retryCount = retryCount ?? 0;
    this.isRetrying.value = isRetrying ?? false;
    this.isGettingTurl = isGettingTurl ?? false;
  }

  /// 从 ImageModel 创建 RxImageModel
  factory RxImageModel.fromImageModel(ImageModel model) {
    return RxImageModel(
      url: model.url,
      turl: model.turl,
      loaded: model.loaded,
      fileName: model.fileName,
      uploadDate: model.uploadDate,
      isHomeImage: model.isHomeImage,
    );
  }

  /// 转换为 ImageModel
  ImageModel toImageModel() {
    return ImageModel(
      url: url.value,
      turl: turl.value,
      loaded: loaded.value,
      fileName: fileName.value,
      uploadDate: uploadDate.value,
      isHomeImage: isHomeImage.value,
    );
  }

  /// 获取有效的显示URL
  ///
  /// 智能选择最佳的显示URL
  /// - 如果临时URL存在，直接返回临时URL（不检查过期状态）
  /// - 否则返回空字符串，让调用方处理URL获取逻辑
  ///
  /// 注意：不检查 isTurlExpired，让现有的重试逻辑处理过期URL的加载失败情况
  /// 这样可以避免不必要的缓存处理，简化逻辑
  ///
  /// 返回可用的显示URL
  String getDisplayUrl() {
    if (turl.value.isNotEmpty) {
      return turl.value;
    }
    return '';
  }

  /// 重置重试状态
  ///
  /// 清除重试相关的状态，用于重新开始重试流程
  void resetRetryState() {
    retryCount = 0;
    isRetrying.value = false;
    isGettingTurl = false;
  }
}

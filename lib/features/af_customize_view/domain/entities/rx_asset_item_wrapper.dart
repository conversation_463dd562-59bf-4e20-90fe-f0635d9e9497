import 'package:asset_force_mobile_v2/core/utils/number_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/data/models/image_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/adapters/asset_item_adapter.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/adapters/event_type_item_adapter.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/adapters/reservation_item_adapter.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/adapters/shared_layout_setting_adapter.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/calculation_formula.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/item_model_base.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_image_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_calculate_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';

/// RxAssetItemWrapper 是一个响应式的资产项目包装器类
/// 用于包装实现 [ItemModelBase] 接口的数据模型并提供响应式的数据绑定功能
///
/// 主要功能:
/// - 包装原始数据模型
/// - 提供响应式的 [defaultData] 和 [isEditing] 状态
/// - 代理原始模型的属性访问
/// - 提供数据更新方法,确保响应式数据和原始模型同步更新
class RxAssetItemWrapper {
  final ItemModelBase _model;
  final RxString valueForShow = ''.obs;
  final RxBool isReadOnly = false.obs;
  final RxList<RxImageModel> imageModels = <RxImageModel>[].obs;

  /// 计算公式对象,用于处理资产项目的计算逻辑
  ///
  /// 当资产项目需要进行计算时,可以通过此属性设置相应的计算公式
  /// 计算公式包含了计算规则和相关参数
  CalculationFormula? calculationFormula;

  /// 显示消息
  RxString showMessage = ''.obs;

  /// 是否显示消息
  RxBool isShowMessage = false.obs;

  /// 创建一个从 AssetItemListModel 包装的 RxAssetItemWrapper
  factory RxAssetItemWrapper.fromAssetItem(AssetItemListModel model) {
    return RxAssetItemWrapper(AssetItemAdapter(model));
  }

  /// 创建一个从 ReservationItemCommon 包装的 RxAssetItemWrapper
  ///
  /// 参数:
  /// * [model] - 预约项目通用列表模型
  ///
  /// 返回值:
  /// * [RxAssetItemWrapper] - 包装后的响应式资产项目包装器
  ///
  /// 说明:
  /// 此工厂方法将预约项目模型通过 ReservationItemAdapter 适配器转换为通用的资产项目包装器
  /// 使预约项目可以在自定义视图中以统一的方式处理和显示
  factory RxAssetItemWrapper.fromReservationItem(ReservationItemCommon model) {
    return RxAssetItemWrapper(ReservationItemAdapter(model));
  }

  /// 创建一个从 EventTypeItem 包装的 RxAssetItemWrapper
  ///
  /// 参数:
  /// * [model] - 事件类型项目模型
  ///
  /// 返回值:
  factory RxAssetItemWrapper.fromEventTypeItem(EventTypeItem model) {
    return RxAssetItemWrapper(EventTypeItemAdapter(model));
  }

  /// 创建一个从 SharedLayoutSetting 包装的 RxAssetItemWrapper
  ///
  /// 参数:
  /// * [item] - 共享布局设置模型
  ///
  /// 返回值:
  /// * [RxAssetItemWrapper] - 包装后的响应式资产项目包装器
  ///
  /// 说明:
  /// 此工厂方法将共享布局设置模型通过 SharedLayoutSettingAdapter 适配器转换为通用的资产项目包装器
  /// 使共享布局设置可以在自定义视图中以统一的方式处理和显示
  factory RxAssetItemWrapper.fromSharedLayoutSetting(SharedLayoutSetting item) {
    return RxAssetItemWrapper(SharedLayoutSettingAdapter(item));
  }

  RxAssetItemWrapper(this._model) {
    isReadOnly.value = _isReadOnly ?? false;
    if (SharedItemTypeEnum.image.equals(itemType)) {
      imageModels.value = getRxImageModels(getImagesMap());
    }
  }

  // 代理原始模型的属性
  /// 获取项目显示名称
  String? get itemDisplayName => _model.itemDisplayName;

  /// 获取项目类型
  String? get itemType => _model.itemType;

  /// 获取项目ID
  String? get itemId => _model.itemId.toString();

  /// 获取项目名称
  String? get itemName => _model.itemName.toString();

  /// 获取项目默认数据
  dynamic get defaultData => _model.defaultData;

  /// 是否已经校验过
  bool? get isValid => _model.isValid;

  /// 获取项目选项对象
  OptionObjModel? get optionObject => _model.optionObject;

  /// 是否只读
  bool? get _isReadOnly => optionObject?.readonly == '1';

  /// 设置是否只读
  void setReadOnly(bool? value) {
    isReadOnly.value = value ?? false;
    if (value == true) {
      _model.optionObject?.readonly = '1';
    } else {
      _model.optionObject?.readonly = '0';
    }
  }

  /// 是否必填
  bool? get isRequired => inputFlg == '1';

  /// 获取计算类型
  String? get calculateType => _model.optionObject?.calculateType;

  /// 通货类型
  String? get currencyType => _model.optionObject?.currencyType;

  /// 获取项目值
  dynamic get itemValue => _model.itemValue;

  /// 是否系统设置
  String? get sysSetFlg => _model.sysSetFlg;

  /// 获取项目编辑权限
  String? get isEditPermissions => _model.isEditPermissions;

  /// 获取项目输入权限
  String? get inputFlg => _model.inputFlg;

  /// 设置项目显示值
  void set valueForShowModel(dynamic value) {
    _model.valueForShow = value;
  }

  /// 获取模型对象
  ///
  /// 尽量减少使用此方法，因为会导致管理松散，尽量使用 rxwarpper 中的 get 属性
  /// 目前唯一的使用地方是 在 calc_asset_dict_usecase 中，需要获取 model 对象用于 calculate 计算
  get model => _model;

  /// 设置百分比
  set percentage(String? value) {
    _model.percentage = value;
  }

  /// 设置项目值
  set itemValue(dynamic value) {
    _model.itemValue = value;
  }

  /// 设置单位
  set unit(String? value) {
    _model.unit = value;
  }

  /// 获取Master项目ID
  get masterId => _model.masterId;

  /// 初始化图片URL列表
  List<dynamic> getImagesMap() {
    List<dynamic> imageUrls = [];
    if (_model.defaultData is! String) {
      imageUrls = _model.defaultData as List<dynamic>? ?? [];
    } else {
      imageUrls = [];
    }
    return imageUrls;
  }

  /// 设置图片列表
  List<RxImageModel> getRxImageModels(List<dynamic> urls) {
    return urls.map((url) => RxImageModel.fromImageModel(ImageModel.fromJson(url as Map<String, dynamic>))).toList();
  }

  /// 更新默认数据
  ///
  /// 根据不同的项目类型对数据进行格式化处理,并更新到原始模型中
  ///
  /// 参数:
  /// * [value] - 要更新的新值,可以是任意对象类型
  ///
  /// 说明:
  /// 1. 对于计算类型项目:
  ///   - 使用计算配置中的千分位和小数点设置进行格式化
  /// 2. 对于通货类型项目:
  ///   - 固定使用3位千分位
  ///   - 使用货币配置中的小数点设置进行格式化
  /// 3. 对于数字类型项目:
  ///   - 使用普通数字配置中的千分位和小数点设置进行格式化
  /// 4. 其他类型项目:
  ///   - 直接使用原始值
  /// 5. 格式化后的值会同步更新到:
  ///   - 原始模型的 defaultData 属性
  ///   - valueForShow 的可观察值
  void updateDefaultData(Object value) {
    dynamic formattedValue = value;
    dynamic rawValue = value;

    if (SharedItemTypeEnum.calculate.equals(itemType) && SharedCalculateValueItemEnum.digital.equals(calculateType)) {
      // 计算类型 - 使用计算配置
      final (commaDecimalPoint, decimalPoint) = getCalculateDecimalPointConfig();
      formattedValue = NumberUtils.formatNumberWithCommaAndDecimal(
        commaDecimalPoint,
        decimalPoint,
        value.toString(),
        true,
      );
      rawValue = formattedValue.toString().replaceAll(',', '');
    } else if (SharedItemTypeEnum.currency.equals(itemType)) {
      // 通货类型 - 使用货币配置
      final decimalPoint = getCurrencyDecimalPointConfig();
      formattedValue = NumberUtils.formatNumberWithCommaAndDecimal(
        3, // 通货类型固定使用3位千分位
        decimalPoint,
        value.toString(),
        true,
      );
      rawValue = formattedValue.toString().replaceAll(',', '');
    } else if (SharedItemTypeEnum.number.equals(itemType)) {
      // 数字类型 - 使用普通数字配置
      final (commaDecimalPoint, decimalPoint) = getDecimalPointConfig();
      formattedValue = NumberUtils.formatNumberWithCommaAndDecimal(
        commaDecimalPoint,
        decimalPoint,
        value.toString(),
        true,
      );
      rawValue = formattedValue.toString().replaceAll(',', '');
    } else if (SharedItemTypeEnum.image.equals(itemType)) {
      // 图片类型 - 使用图片配置
      // 将列表对象转换为列表 map
      formattedValue = (value as List<RxImageModel>).map((e) => e.toImageModel().toJson()).toList();
      rawValue = formattedValue;
    }

    _model.defaultData = rawValue; // 同步更新原始模型
    valueForShow.value = formattedValue.toString();
  }

  /// 获取小数点相关配置
  ///
  /// 返回一个包含千位分隔符位置和小数点位数的元组
  ///
  /// 返回值:
  /// - 第一个元素表示千位分隔符位置
  /// - 第二个元素表示小数点位数
  ///
  /// 示例:
  /// ```dart
  /// final (commaPoint, decimalPoint) = getDecimalPointConfig();
  /// // commaPoint: 3 表示每3位添加一个千位分隔符
  /// // decimalPoint: 2 表示保留2位小数
  /// ```
  (int, int) getDecimalPointConfig() {
    final optionObject = _model.optionObject;
    final commaDecimalPoint = int.tryParse(optionObject?.numberCommaDecimalPoint ?? '0') ?? 0;
    final decimalPoint = int.tryParse(optionObject?.numberDecimalPoint?.toString() ?? '0') ?? 0;
    return (commaDecimalPoint, decimalPoint);
  }

  /// 获取计算项目的小数点相关配置
  ///
  /// 返回一个包含千位分隔符位置和小数点位数的元组
  ///
  /// 返回值:
  /// - 第一个元素表示千位分隔符位置
  /// - 第二个元素表示小数点位数
  ///
  /// 示例:
  /// ```dart
  /// final (commaPoint, decimalPoint) = getCalculateDecimalPointConfig();
  /// // commaPoint: 3 表示每3位添加一个千位分隔符
  /// // decimalPoint: 2 表示保留2位小数
  /// ```
  (int, int) getCalculateDecimalPointConfig() {
    final optionObject = _model.optionObject;
    final commaDecimalPoint = int.tryParse(optionObject?.calculateCommaDecimalPoint?.toString() ?? '0') ?? 0;
    final decimalPoint = int.tryParse(optionObject?.calculateDecimalPoint?.toString() ?? '0') ?? 0;
    return (commaDecimalPoint, decimalPoint);
  }

  /// 获取货币项目的小数点相关配置
  ///
  /// 返回一个表示小数点位数的整数
  ///
  /// 返回值:
  /// - 小数点位数
  int getCurrencyDecimalPointConfig() {
    final optionObject = _model.optionObject;
    final decimalPoint = int.tryParse(optionObject?.currencyDecimalPoint?.toString() ?? '0') ?? 0;
    return decimalPoint;
  }

  /// 清除消息
  ///
  /// 将消息相关的状态重置为初始状态:
  /// - 隐藏消息显示
  /// - 清空消息内容
  void clearMessage() {
    isShowMessage.value = false;
    showMessage.value = '';
  }

  /// 设置显示消息
  ///
  /// 参数:
  /// * [message] - 要显示的消息内容
  void setShowMessage(String message) {
    isShowMessage.value = true;
    showMessage.value = message;
  }

  /// 将 RxAssetItemWrapper 转换为 JSON 格式
  ///
  /// 用于 JavaScript 执行器的数据序列化，避免 JSON 编码错误
  ///
  /// 返回值:
  /// * [Map<String, dynamic>] - 包含所有必要字段的 JSON 对象
  ///
  /// 说明:
  /// 1. 包含所有基础属性（itemId、itemName、itemType 等）
  /// 2. 包含数据相关属性（defaultData、itemValue、valueForShow）
  /// 3. 包含配置相关属性（optionObject、权限设置等）
  /// 4. 处理响应式对象的值提取
  /// 5. 处理复杂对象的序列化（如图片列表）
  Map<String, dynamic> toJson() {
    // 处理 optionObject 的序列化
    Map<String, dynamic>? optionObjectJson;
    if (optionObject != null) {
      try {
        // 尝试调用 toJson 方法
        optionObjectJson = (optionObject as dynamic).toJson();
      } catch (e) {
        // 如果没有 toJson 方法，创建基础的 JSON 表示
        optionObjectJson = {
          'readonly': optionObject!.readonly,
          'calculateType': optionObject!.calculateType,
          'currencyType': optionObject!.currencyType,
          'currencyDecimalPoint': optionObject!.currencyDecimalPoint,
          'numberCommaDecimalPoint': optionObject!.numberCommaDecimalPoint,
          'numberDecimalPoint': optionObject!.numberDecimalPoint,
          'calculateCommaDecimalPoint': optionObject!.calculateCommaDecimalPoint,
          'calculateDecimalPoint': optionObject!.calculateDecimalPoint,
          // 可以根据需要添加更多字段
        };
      }
    }

    // 处理图片列表的序列化
    List<Map<String, dynamic>>? imageModelsJson;
    if (imageModels.isNotEmpty) {
      imageModelsJson = imageModels.map((rxImage) {
        try {
          return rxImage.toImageModel().toJson();
        } catch (e) {
          // 如果序列化失败，返回基础信息
          return {'error': 'Failed to serialize image model'};
        }
      }).toList();
    }

    // 处理计算公式的序列化
    Map<String, dynamic>? calculationFormulaJson;
    if (calculationFormula != null) {
      try {
        calculationFormulaJson = (calculationFormula as dynamic).toJson();
      } catch (e) {
        // 如果没有 toJson 方法，创建基础表示
        calculationFormulaJson = {
          'type': calculationFormula.runtimeType.toString(),
          'error': 'Failed to serialize calculation formula',
        };
      }
    }

    return {
      // 基础标识信息
      'itemId': itemId,
      'itemName': itemName,
      'itemDisplayName': itemDisplayName,
      'itemType': itemType,

      // 数据相关
      'defaultData': _serializeDefaultData(),
      'itemValue': _serializeItemValue(),
      'valueForShow': valueForShow.value,

      // 权限和状态
      'inputFlg': inputFlg,
      'isEditPermissions': isEditPermissions,
      'sysSetFlg': sysSetFlg,
      'isValid': isValid,
      'isRequired': isRequired,
      'isReadOnly': isReadOnly.value,

      // 配置对象
      'optionObject': optionObjectJson,

      // 计算相关
      'calculateType': calculateType,
      'currencyType': currencyType,
      'calculationFormula': calculationFormulaJson,

      // 主数据相关
      'masterId': masterId,

      // 响应式状态（提取值）
      'showMessage': showMessage.value,
      'isShowMessage': isShowMessage.value,

      // 图片相关
      'imageModels': imageModelsJson,

      // 元数据
      'modelType': _model.runtimeType.toString(),
      'wrapperType': 'RxAssetItemWrapper',
    };
  }

  /// 序列化 defaultData
  ///
  /// 处理各种类型的 defaultData，确保可以被 JSON 序列化
  dynamic _serializeDefaultData() {
    final data = defaultData;
    if (data == null) return null;

    // 如果是基础类型，直接返回
    if (data is String || data is num || data is bool) {
      return data;
    }

    // 如果是列表
    if (data is List) {
      return data.map((item) => _serializeItem(item)).toList();
    }

    // 如果是 Map
    if (data is Map) {
      return data.map((key, value) => MapEntry(key.toString(), _serializeItem(value)));
    }

    // 其他类型尝试调用 toJson 或转换为字符串
    return _serializeItem(data);
  }

  /// 序列化 itemValue
  ///
  /// 处理各种类型的 itemValue，确保可以被 JSON 序列化
  dynamic _serializeItemValue() {
    final value = itemValue;
    if (value == null) return null;

    // 如果是基础类型，直接返回
    if (value is String || value is num || value is bool) {
      return value;
    }

    // 如果是列表
    if (value is List) {
      return value.map((item) => _serializeItem(item)).toList();
    }

    // 如果是 Map
    if (value is Map) {
      return value.map((key, val) => MapEntry(key.toString(), _serializeItem(val)));
    }

    // 其他类型尝试调用 toJson 或转换为字符串
    return _serializeItem(value);
  }

  /// 序列化单个项目
  ///
  /// 通用的项目序列化方法
  dynamic _serializeItem(dynamic item) {
    if (item == null) return null;

    // 基础类型直接返回
    if (item is String || item is num || item is bool) {
      return item;
    }

    // 尝试调用 toJson 方法
    try {
      if (item is Map) {
        return item.map((key, value) => MapEntry(key.toString(), _serializeItem(value)));
      }
      if (item is List) {
        return item.map((element) => _serializeItem(element)).toList();
      }
      // 尝试调用对象的 toJson 方法
      return (item as dynamic).toJson();
    } catch (e) {
      // 如果序列化失败，返回字符串表示
      return item.toString();
    }
  }
}

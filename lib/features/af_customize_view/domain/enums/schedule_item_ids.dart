/// Schedule项目ID
enum ScheduleItemIds {
  /// 名称
  ///
  /// isRequired（默认一定必须入力）
  name('1'),

  /// 开始日时
  ///
  /// isRequired（默认一定必须入力）
  startDate('2'),

  /// 结束日时
  ///
  /// isRequired（默认一定必须入力）
  endDate('3'),

  /// 终日
  allDay('4'),

  /// alert设定
  alertSetting('5'),

  /// 事件类型
  ///
  /// isRequired（默认一定必须入力）
  eventType('6');

  const ScheduleItemIds(this.value);

  final String value;

  bool equals(String? otherValue) {
    return value == otherValue;
  }
}

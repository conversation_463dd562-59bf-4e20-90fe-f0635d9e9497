import 'package:json_annotation/json_annotation.dart';

part 'master_display_option_model.g.dart';

@JsonSerializable()
class MasterDisplayOptionModel {
  final String? check;
  final String? readonly;
  final List<String>? checkboxOptions;
  final String? checkboxMultiFlg;
  final List<String>? radioOptions;
  final String? maxlength;
  final List<String>? data;
  final String? numberDecimalPoint;
  final String? numberCommaDecimalPoint;

  MasterDisplayOptionModel({
    this.check,
    this.readonly,
    this.checkboxOptions,
    this.checkboxMultiFlg,
    this.radioOptions,
    this.maxlength,
    this.data,
    this.numberDecimalPoint,
    this.numberCommaDecimalPoint,
  });

  factory MasterDisplayOptionModel.fromJson(Map<String, dynamic> json) => _$MasterDisplayOptionModelFromJson(json);

  Map<String, dynamic> toJson() => _$MasterDisplayOptionModelToJson(this);
}

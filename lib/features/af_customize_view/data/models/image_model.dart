import 'package:json_annotation/json_annotation.dart';

part 'image_model.g.dart';

@JsonSerializable()
class ImageModel {
  String? url;
  @JsonKey(includeToJson: false)
  String? turl;
  @JsonKey(includeToJson: false)
  bool? loaded;
  String? fileName;
  String? uploadDate;
  bool? isHomeImage;

  ImageModel({this.url, this.turl, this.loaded, this.fileName, this.uploadDate, this.isHomeImage});

  factory ImageModel.fromJson(Map<String, dynamic> json) => _$ImageModelFromJson(json);

  Map<String, dynamic> toJson() => _$ImageModelToJson(this);
}

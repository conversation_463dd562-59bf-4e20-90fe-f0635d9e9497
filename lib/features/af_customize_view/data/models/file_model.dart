import 'package:json_annotation/json_annotation.dart';

part 'file_model.g.dart';

/// 文件数据模型
@JsonSerializable()
class FileModel {
  /// 文件URL
  final String? url;

  /// 文件大小
  final int? size;

  /// 临时URL
  final String? turl;

  /// 总数
  final int? total;

  /// 已加载数量
  final int? loaded;

  /// 状态
  final String? status;

  /// 文件名
  final String? fileName;

  /// 进度
  final int? progress;

  /// 上传时间
  final String? uploadDate;

  /// 上传用户名
  final String? uploadUserName;

  const FileModel({
    required this.url,
    required this.size,
    required this.turl,
    required this.total,
    required this.loaded,
    required this.status,
    required this.fileName,
    required this.progress,
    required this.uploadDate,
    required this.uploadUserName,
  });

  /// 从JSON创建对象
  factory FileModel.fromJson(Map<String, dynamic> json) => _$FileModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$FileModelToJson(this);
}

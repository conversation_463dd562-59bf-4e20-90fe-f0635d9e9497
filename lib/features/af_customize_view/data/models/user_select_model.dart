import 'package:json_annotation/json_annotation.dart';

part 'user_select_model.g.dart';

@JsonSerializable()
class UserSelectModel {
  /// 用户ID
  final int userId;

  /// 用户名称
  final String userName;

  const UserSelectModel({required this.userId, required this.userName});

  factory UserSelectModel.fromJson(Map<String, dynamic> json) => _$UserSelectModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserSelectModelToJson(this);

  @override
  String toString() => userName;
}

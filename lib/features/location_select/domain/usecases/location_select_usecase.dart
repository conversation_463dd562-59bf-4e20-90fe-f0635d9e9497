import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/location_select/domain/repositories/location_repository.dart';

class LocationSelectUsecase extends UseCase<List<String>?, NoParams> {
  final LocationRepository repository;

  LocationSelectUsecase(this.repository);

  @override
  Future<List<String>?> call(NoParams params) async {
    try {
      final List<String>? data = await repository.getLocationList();

      if (data != null && data.isNotEmpty) {
        return ['拠点指定なし'] + data;
      } else {
        // 取不到数据的时候， 将本地的缓存清掉
        final userName = StorageUtils.get<String>(StorageUtils.keyUserName);

        if (userName != null && userName.isNotEmpty) {
          StorageUtils.remove(userName);
        }
      }

      return data;
    } catch (e) {
      return null;
    }
  }
}

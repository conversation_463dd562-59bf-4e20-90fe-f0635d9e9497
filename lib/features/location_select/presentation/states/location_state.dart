class LocationState {
  List<String>? locations;

  final String? error;
  final bool isLoading;

  /// 选择之后的地点
  String location = '';

  // 原始的值。
  final List<String>? originalLocations;

  LocationState._({this.locations, this.error, required this.isLoading, this.originalLocations});

  factory LocationState.initial() => LocationState._(isLoading: false);

  factory LocationState.loading() => LocationState._(isLoading: true);

  factory LocationState.success(List<String> locations) =>
      LocationState._(locations: locations, isLoading: false, originalLocations: List.from(locations));

  factory LocationState.error(String error) => LocationState._(error: error, isLoading: false);
}

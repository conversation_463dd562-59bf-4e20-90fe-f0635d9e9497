import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/location_select/data/repositories/location_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/location_select/domain/repositories/location_repository.dart';
import 'package:asset_force_mobile_v2/features/location_select/domain/usecases/location_select_usecase.dart';
import 'package:asset_force_mobile_v2/features/location_select/presentation/controllers/location_select_controller.dart';
import 'package:get/get.dart';

class LocationSelectBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<LocationRepository>(() => LocationRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPut<LocationSelectUsecase>(() => LocationSelectUsecase(Get.find<LocationRepository>()));
    Get.lazyPut<LocationSelectController>(() => LocationSelectController(Get.find<LocationSelectUsecase>()));
  }
}

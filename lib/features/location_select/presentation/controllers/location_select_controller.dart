import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/location_select/data/models/location_page_param.dart';
import 'package:asset_force_mobile_v2/features/location_select/domain/usecases/location_select_usecase.dart';
import 'package:asset_force_mobile_v2/features/location_select/presentation/states/location_state.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_mixin.dart';
import 'package:get/get.dart';

class LocationSelectController extends BaseController with OverlayMixin {
  final LocationSelectUsecase getLocationUseCase;

  LocationSelectController(this.getLocationUseCase);

  var state = LocationState.initial().obs;
  final searchQuery = ''.obs;
  late LocationPageParam pageParam;

  @override
  void onInit() {
    super.onInit();

    LogUtil.d('message location select onInit, ${Get.arguments}');
    if (Get.arguments is LocationPageParam) {
      pageParam = Get.arguments as LocationPageParam;
      fetchLocationList();
    } else {
      Get.back();
      Future.delayed(const Duration(milliseconds: 100), () => {handleException(SystemException())});
    }
  }

  Future<void> onRefresh() async {
    if (state.value.error != null && !state.value.isLoading) {
      fetchLocationList();
    }
  }

  void updateSearch(String query) {
    if (state.value.isLoading || state.value.error != null) {
      // 加载中或者失败, 不进入搜索
      return;
    }

    searchQuery.value = query;

    final pQuery = query.toLowerCase().trim();
    final List<String> filteredLocations = List.unmodifiable(
      state.value.originalLocations!.where((item) {
        return item.contains(pQuery);
      }),
    );

    state.value.locations = filteredLocations;

    state.refresh();
  }

  Future<void> onBack() async {
    Get.back(result: pageParam.location);
  }

  Future<void> fetchLocationList() async {
    try {
      state.value = LocationState.loading();
      final locations = await getLocationUseCase.call(const NoParams());
      state.value = LocationState.success(locations!);

      state.value.location = pageParam.location;
      if (!pageParam.isFromUserSettings) {
        final loc = getLocation();
        if (loc == null || loc.isEmpty) {
          state.value.location = '拠点指定なし';
        } else {
          state.value.location = loc;
        }
      }

      if (state.value.location.isEmpty) {
        state.value.location = '拠点指定なし';
      }

      if (searchQuery.value.isNotEmpty) {
        updateSearch(searchQuery.value);
      }
    } catch (e) {
      state.value = LocationState.error(e.toString());
    }
  }

  /// 如果没有设置， 则为 null， 如果选择了 [拠点指定なし] 则返回空字符串。
  String? getLocation() {
    if (!StorageUtils.contains(StorageUtils.keyUserName)) {
      // 未设置。
      return null;
    }

    return StorageUtils.getAssetScanLocation();
  }

  Future<void> selectLocation(String location) async {
    state.value.location = location;
    var retLocation = state.value.location;
    if (state.value.location == '拠点指定なし') {
      retLocation = '';
    }

    // 非设置页面过来的， 全部保存数据。
    if (!pageParam.isFromUserSettings) {
      await StorageUtils.setAssetScanLocation(assetLocation: retLocation);
    }
    Get.back(result: retLocation);
  }

  List<String> getLocationList() {
    return state.value.locations ?? [];
  }
}

import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/location_select/presentation/bindings/location_select_binding.dart';
import 'package:asset_force_mobile_v2/features/location_select/presentation/controllers/location_select_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/refresh_load_more_list.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/location_select_page', binding: LocationSelectBinding)
class LocationSelectPage extends GetWidget<LocationSelectController> {
  const LocationSelectPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('場所選択'),
        leading: IconButton(icon: const Icon(Icons.chevron_left), onPressed: controller.onBack),
      ),
      body: Column(
        children: [
          Obx(() {
            return Column(
              children: [
                Container(
                  color: (AppTheme.lightTheme.appBarTheme.backgroundColor),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(8)),
                    child: TextField(
                      onChanged: controller.updateSearch,
                      decoration: const InputDecoration(
                        hintText: '場所を検索',
                        prefixIcon: Icon(Icons.search),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppTheme.lightTheme.customTheme.cardBackgroundColor,
                    border: Border.all(color: AppTheme.lightTheme.customTheme.cardSeparatorColor),
                    borderRadius: const BorderRadius.all(Radius.circular(8)),
                  ),
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [Text(controller.state.value.location), const Text('設定済')],
                  ),
                ),
              ],
            );
          }),
          Obx(() {
            if (controller.state.value.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }
            if (controller.state.value.error != null) {
              return Center(child: Text(controller.state.value.error!));
            }

            final locations = controller.state.value.locations;
            return Flexible(
              fit: FlexFit.loose,
              child: Container(
                margin: const EdgeInsets.fromLTRB(16, 12, 16, 0),
                child: RefreshLoadMoreList(
                  onRefresh: controller.onRefresh,
                  onLoadMore: controller.fetchLocationList,
                  isLoading: false.obs,
                  noMoreData: true.obs,
                  items: locations!.obs,
                  shrinkWrap: true,
                  itemBuilder: (context, index, item) {
                    return Container(
                      padding: const EdgeInsets.only(left: 16, right: 16),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(index == 0 ? 8 : 0),
                          topRight: Radius.circular(index == 0 ? 8 : 0),
                          bottomLeft: Radius.circular(index == (locations.length - 1) ? 8 : 0),
                          bottomRight: Radius.circular(index == (locations.length - 1) ? 8 : 0),
                        ),
                        color: AppTheme.lightTheme.customTheme.cardBackgroundColor,
                      ),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 0),
                        decoration: BoxDecoration(
                          border: index < (locations.length - 1)
                              ? Border(bottom: BorderSide(color: AppTheme.lightTheme.customTheme.cardSeparatorColor))
                              : const Border(),
                        ),
                        child: GestureDetector(
                          onTap: () {
                            controller.selectLocation(item);
                          },
                          child: Text(item),
                        ),
                      ),
                    );
                  },
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}

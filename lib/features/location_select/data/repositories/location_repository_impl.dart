import 'package:asset_force_mobile_v2/core/extensions/get_response_extension.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/location_select/data/models/location_list_response.dart';
import 'package:asset_force_mobile_v2/features/location_select/domain/repositories/location_repository.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';

class LocationRepositoryImpl with RepositoryErrorHandler implements LocationRepository {
  final DioUtil dioUtil;

  LocationRepositoryImpl({required this.dioUtil});

  @override
  Future<List<String>?> getLocationList() async {
    return executeRepositoryTask<List<String>?>(() async {
      final resp = await dioUtil.get(GlobalVariable.getLocationList);

      if (resp.isSuccess()) {
        final p = LocationListResponse.fromJson(resp.data);
        if (p.isSuccess()) {
          return p.locationList;
        }
      }
      return null;
    }, 'Failed to get location list');
  }
}

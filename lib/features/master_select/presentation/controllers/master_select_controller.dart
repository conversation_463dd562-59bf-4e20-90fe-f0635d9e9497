import 'dart:convert';

import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/master_display_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/master_select/data/models/master_detail_search.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/models/master_item_entry.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/models/master_select_params.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/models/master_select_result.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/usecases/get_master_items_usecase.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/usecases/get_master_layout_settings_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 主数据选择控制器
///
/// 用于管理主数据选择页面的状态和业务逻辑
///
/// 主要功能:
/// - 获取和过滤主数据列表
/// - 管理层级导航
/// - 处理搜索功能
/// - 处理项目选择
class MasterSelectController extends GetxController {
  final GetMasterItemsUseCase getMasterItemsUsecase;

  /// 页面UI相关常量
  /// 搜索框相关
  static const double searchFieldHeight = 40.0;
  static const double searchFieldPadding = 10.0;
  static const double searchFieldBorderRadius = 10.0;
  static const double searchFieldIconSize = 24.0;
  static const double searchFieldFontSize = 16.0;
  static const double searchFieldCursorHeight = 20.0;
  static const EdgeInsets searchFieldContentPadding = EdgeInsets.symmetric(vertical: 10, horizontal: 10);
  static const BoxConstraints searchFieldIconConstraints = BoxConstraints(minWidth: 40, minHeight: 40);

  /// 列表项相关
  static const double listItemFontSize = 14.0;
  static const double listItemIconSize = 14.0;
  static const EdgeInsets listItemPadding = EdgeInsets.only(left: 15, right: 10);
  static const double listItemMargin = 10.0;
  static const double listItemBorderRadius = 10.0;
  static const double listItemSpacing = 8;
  static const double listItemFirstSpacing = 8.0;

  /// 文本相关
  static const double emptyTextFontSize = 16.0;
  static const double bottomTextFontSize = 16.0;
  static const EdgeInsets bottomTextPadding = EdgeInsets.only(bottom: 8, top: 8);

  /// 文本常量
  static const String emptyDataText = 'データがありません';
  static const String allItemsDisplayedText = '全件表示されました';
  static const String backButtonText = '１つ前へ戻る';

  /// 当前层级
  final RxInt level = 0.obs;

  /// master列表
  /// api返回后的原始列表
  List<MasterDetailSearchMasterDetail> masterList = [];

  /// 用于存储每个层级的选中值
  final RxMap<int, MasterItemEntry> selectedItems = <int, MasterItemEntry>{}.obs;

  /// 是否加载中
  final RxBool isLoading = false.obs;

  /// 错误信息
  final RxString error = ''.obs;

  /// 搜索关键字
  final RxString searchQuery = ''.obs;

  /// 进入画面时传入的参数
  final MasterSelectParams params;

  /// 页面标题
  final RxString title = ''.obs;

  /// 需要展示的Master 项目列表
  List<MasterDisplayItemModel> masterDisplayList = [];

  /// 当前显示在画面上的列表数据
  final RxList<MasterItemEntry> displayList = <MasterItemEntry>[].obs;

  /// 获取主数据布局设置的用例
  final GetMasterLayoutSettingsUseCase getMasterLayoutSettingsUsecase;

  /// 主数据布局设置
  List<SharedLayoutSetting> masterLayoutSettings = [];

  /// 构造函数
  ///
  /// [getMasterItemsUsecase] - 获取主数据项的用例
  /// [params] - 页面初始化参数
  MasterSelectController({
    required this.getMasterItemsUsecase,
    required this.params,
    required this.getMasterLayoutSettingsUsecase,
  }) {
    level.value = params.level;
    masterDisplayList = List<MasterDisplayItemModel>.from(params.masterDisplayList);

    // 确保 masterDisplayList 中的每个项都有有效的显示名称
    // 这是一个兼容性修复，不会破坏现有逻辑
    MasterDisplayUtils.ensureValidDisplayNames(masterDisplayList);

    // 遍历当前选中的列表,为每个层级设置选中值
    for (int i = 0; i < params.currentSelectedList.length; i++) {
      final itemValue = params.currentSelectedList[i];
      final currentMasterDisplay = masterDisplayList[i];
      final masterName = currentMasterDisplay.itemDisplayName ?? currentMasterDisplay.itemName ?? '';
      final itemType = currentMasterDisplay.itemType ?? '';
      final option = currentMasterDisplay.option ?? '';
      if (i < level.value) {
        LogUtil.d('初始化选中数据itemValue: $itemValue');
        selectedItems[i] = MasterItemEntry(
          itemValue: itemValue,
          itemDisplayValue: _transformDisplayValue(itemValue, masterName, itemType, option),
        );
      } else {
        selectedItems.remove(i);
      }
    }
  }

  @override
  Future<void> onInit() async {
    super.onInit();
    title.value = params.title;
    await fetchMasterLayoutSettings(params.masterTypeId);
    await fetchMasterItemsByType(params.masterTypeId);
  }

  /// 根据主数据类型ID获取主数据项
  ///
  /// [masterTypeId] 主数据类型的唯一标识符
  ///
  /// 执行流程:
  /// 1. 设置加载状态为true
  /// 2. 清空错误信息
  /// 3. 调用获取主数据用例获取数据
  /// 4. 对获取的数据进行过滤和转换
  /// 5. 如果发生错误，保存错误信息
  /// 6. 最后设置加载状态为false
  Future<void> fetchMasterItemsByType(String masterTypeId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final result = await getMasterItemsUsecase(masterTypeId);
      masterList = result;
      if (level.value != 0) {
        updateLevelAndPrepareData();
      } else {
        _filterAndTransformMasterList(result, title.value);
      }
    } catch (e) {
      error.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  /// 返回上一级
  ///
  /// 当层级大于0时,减少层级并重新过滤数据
  void navigateToPreviousLevel() {
    if (level.value > 0) {
      level.value--;
      updateLevelAndPrepareData();
    }
  }

  /// 选择项点击事件
  ///
  /// [index] 选中项的索引
  ///
  /// 如果是最后一层:
  /// - 返回选中值
  /// 否则:
  /// - 更新选中项
  /// - 进入下一层级
  /// - 更新数据
  void onItemSelected(int index) {
    // 更新缓存的选中条目
    selectedItems[level.value] = displayList[index];
    if (level.value >= masterDisplayList.length - 1) {
      final MasterDetailSearchMasterDetail? finalSelectedMasterDetail = getFinalSelectedMasterDetail();
      LogUtil.d('finalSelectedMasterDetail: ${finalSelectedMasterDetail?.toJson()}');
      final selectedMasterId = finalSelectedMasterDetail?.masterId ?? 0;
      final selectedMasterItems = <String, dynamic>{};
      masterLayoutSettings.forEach((element) {
        final masterText = finalSelectedMasterDetail?.masterText;
        if (masterText != null && masterText.isNotEmpty) {
          final masterTextMap = jsonDecode(masterText);
          final itemValue = masterTextMap[element.itemName];
          selectedMasterItems[element.itemId.toString()] = itemValue;
        }
      });

      Get.back(result: MasterSelectResult(selectedMasterId, selectedMasterItems));
      return;
    }
    // 更新当前选择的层级
    level.value++;
    // 更新标题和检索框内 hint 文本
    updateLevelAndPrepareData();
  }

  /// 更新层级并准备数据
  ///
  /// - 更新标题
  /// - 过滤当前层级数据
  /// - 转换数据格式
  void updateLevelAndPrepareData() {
    final currentMasterDisplay = masterDisplayList[level.value];
    title.value = currentMasterDisplay.itemDisplayName ?? currentMasterDisplay.itemName ?? '';
    final List<MasterDetailSearchMasterDetail> displayMasterDetail = filterMasterItemsByLevel(
      masterList,
      selectedItems,
      level.value,
    );
    _filterAndTransformMasterList(displayMasterDetail, title.value);
  }

  /// 搜索主数据项
  ///
  /// [value] 搜索关键字
  void searchMasterItems(String value) {}

  /// 处理特殊数据类型的显示转换
  ///
  /// [value] 原始值
  /// [masterName] 主数据名称
  /// [itemType] 项目类型
  /// [option] 选项配置字符串
  ///
  /// 返回转换后的显示值
  String _transformDisplayValue(String? value, String masterName, String itemType, String? option) {
    if (SharedItemTypeEnum.checkbox.equals(itemType) && option != null && option.isNotEmpty) {
      try {
        final optionObj = jsonDecode(option);
        if (optionObj['checkboxMultiFlg'] == '1') {
          return value ?? '';
        }
        return value == '1' ? 'あり' : 'なし';
      } catch (e) {
        // 如果解析option失败，使用简单逻辑
        return value == '1' ? 'あり' : 'なし';
      }
    }

    if (value == null || value.isEmpty) {
      return '【未入力】';
    }

    return value;
  }

  /// 从 masterText 中获取键为 masterName 的值
  ///
  /// [masterText] master文本数据,应为有效的JSON字符串
  /// [masterName] 要获取的键名
  ///
  /// 返回:
  /// - 如果masterText是有效的JSON且包含masterName键,返回对应的值的字符串形式
  /// - 如果masterText不是有效的JSON或不包含masterName键,返回空字符串
  /// - 如果解析过程中发生异常,返回空字符串
  String getRawItemName(String masterText, String masterName) {
    try {
      final Map<String, dynamic> data = jsonDecode(masterText);

      if (data[masterName] is List) {
        return data[masterName].join(',');
      }
      return data[masterName]?.toString() ?? '';
    } catch (e) {
      return '';
    }
  }

  /// 获取格式化的项目名称
  ///
  /// [index] 项目索引
  ///
  /// 返回转换后的显示名称
  String getFormattedItemName(int index) {
    final item = selectedItems[level.value];
    final itemMasterText = item?.itemValue;
    final currentMasterDisplay = params.masterDisplayList[index];
    final itemType = currentMasterDisplay.itemType ?? '';
    final option = currentMasterDisplay.option ?? '';
    return _transformDisplayValue(itemMasterText, title.value, itemType, option);
  }

  /// 获取指定层级的主数据项列表
  ///
  /// [masterList] 主数据列表
  /// [selectedItems] 已选择的项目
  /// [mLevel] 当前层级
  ///
  /// 返回过滤后的主数据列表
  List<MasterDetailSearchMasterDetail> filterMasterItemsByLevel(
    List<MasterDetailSearchMasterDetail> masterList,
    RxMap<int, MasterItemEntry> selectedItems,
    int mLevel,
  ) {
    final items = <MasterDetailSearchMasterDetail>[];

    for (final item in masterList) {
      bool isMatch = true;

      // 检查从0到当前层级的所有选择条件
      for (int i = 0; i < mLevel; i++) {
        final masterValue = selectedItems[i];
        final currentLevelMasterName = masterDisplayList[i].itemDisplayName ?? '';
        final itemValue = getRawItemName(item.masterText ?? '', currentLevelMasterName);

        if (itemValue != masterValue?.itemValue) {
          isMatch = false;
          break;
        }
      }

      if (isMatch) {
        items.add(item);
      }
    }

    return items;
  }

  /// 准备主数据列表
  ///
  /// [result] 原始主数据列表
  /// [masterName] 主数据名称
  ///
  /// 执行步骤:
  /// 1. 过滤重复项
  /// 2. 提取项目值
  /// 3. 转换显示格式
  /// 4. 更新显示列表
  void _filterAndTransformMasterList(List<MasterDetailSearchMasterDetail> result, String masterName) {
    final uniqueItems = result.where((item) {
      final currentLevelValue = getRawItemName(item.masterText ?? '', masterName);
      return result.indexWhere(
            (otherItem) => getRawItemName(otherItem.masterText ?? '', masterName) == currentLevelValue,
          ) ==
          result.indexOf(item);
    }).toList();
    final uniqueItemsString = uniqueItems.map((item) => getRawItemName(item.masterText ?? '', masterName)).toList();
    final layoutSetting = masterLayoutSettings
        .where((element) => element.itemId == masterDisplayList[level.value].itemId)
        .firstOrNull;
    final itemType = layoutSetting?.itemType ?? '';
    final option = layoutSetting?.option ?? '';
    final uniqueItemsStringWithSpecialData = uniqueItemsString
        .map(
          (item) => MasterItemEntry(
            itemValue: item,
            itemDisplayValue: _transformDisplayValue(item, masterName, itemType, option),
          ),
        )
        .toList();
    displayList.value = uniqueItemsStringWithSpecialData;
  }

  /// 获取最终选中的主数据项
  ///
  /// 从所有已选择的层级中，根据每个层级的选择条件过滤数据，最终返回第一条数据
  ///
  /// 返回:
  /// - 如果没有任何选择，返回null
  /// - 否则返回最终过滤后的第一条数据
  MasterDetailSearchMasterDetail? getFinalSelectedMasterDetail() {
    // 如果没有选择任何项目，返回第一条数据
    if (selectedItems.isEmpty) {
      return null;
    }

    // 遍历所有已选择的层级
    for (final entry in selectedItems.entries) {
      final level = entry.key;
      final selectedItem = entry.value;
      final masterName = masterDisplayList[level].itemDisplayName ?? '';

      // 根据当前层级的选择条件过滤数据
      masterList = masterList.where((item) {
        final itemValue = getRawItemName(item.masterText ?? '', masterName);
        return itemValue == selectedItem.itemValue;
      }).toList();
    }

    // 返回过滤后的第一条数据
    if (masterList.isEmpty) {
      LogUtil.d('masterList is empty');
      return null;
    }
    LogUtil.d('masterList: ${masterList.first}');
    return masterList.first;
  }

  /// 获取主数据布局设置
  ///
  /// [masterTypeId] 主数据类型ID
  Future<void> fetchMasterLayoutSettings(String masterTypeId) async {
    final result = await getMasterLayoutSettingsUsecase(masterTypeId);
    masterLayoutSettings = result;
    LogUtil.d('masterLayoutSettings: $masterLayoutSettings');
  }
}

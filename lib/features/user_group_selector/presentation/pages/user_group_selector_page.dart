import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/asset_alert/presentation/constants/notification_select_constants.dart';
import 'package:asset_force_mobile_v2/features/asset_alert/presentation/utils/notification_display_utils.dart';
import 'package:asset_force_mobile_v2/features/asset_alert/presentation/widgets/expandable_header_widget.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/selectable_list_view.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/selector_page_scaffold.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/selector_search_bar.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/presentation/bindings/user_group_selector_binding.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/presentation/constants/user_group_selector_constants.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/presentation/controllers/user_group_selector_controller.dart';
import 'package:asset_force_mobile_v2/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/builder.dart';

@GetRoutePage('/user_group_selector', binding: UserGroupSelectorBinding)
class UserGroupSelectorPage extends GetView<UserGroupSelectorController> {
  const UserGroupSelectorPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // 根据选择模式显示不同的UI
      if (UserGroupSelectorConstants.isSingleSelectionMode(controller.selectionMode.value)) {
        return _buildSingleSelectionUI();
      } else {
        return _buildMultipleSelectionUI();
      }
    });
  }

  /// 构建单选模式UI
  Widget _buildSingleSelectionUI() {
    return SelectorPageScaffold(
      title: controller.title.value,
      onClearBtnClick: controller.onClearBtnClick,
      onSearchChanged: controller.filterItems,
      onSortBtnClick: controller.onSortBtnClick,
      searchHint: controller.getSearchHint(),
      body: Obx(() {
        return Column(
          children: [
            SelectableListView(
              key: ValueKey('user_list_${controller.filteredItems.length}'),
              items: controller.filteredItems,
              selectedIndex: controller.selectedIndex.value,
              onItemTap: controller.selectItem,
              getDisplayText: controller.getUserDisplayName,
              onListLoaded: () {
                controller.showEndText.value = true;
              },
            ),
            Obx(
              () => controller.showEndText.value
                  ? const Padding(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      child: Text(
                        UserGroupSelectorConstants.messageAllItemsDisplayed,
                        style: TextStyle(color: Colors.white),
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ],
        );
      }),
    );
  }

  /// 构建多选模式UI
  Widget _buildMultipleSelectionUI() {
    return PopScope(
      canPop: false, // 阻止默认的返回行为
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // 当用户点击返回按钮时，返回选择的数据
          _handleMultipleSelectionBack();
        }
      },
      child: Scaffold(
        appBar: _buildMultipleSelectionAppBar(),
        body: SingleChildScrollView(
          child: Column(
            children: [
              if (UserGroupSelectorConstants.isUserOnlyDisplay(controller.displayType.value) ||
                  UserGroupSelectorConstants.isBothUserAndGroupDisplay(controller.displayType.value))
                _buildUserSection(),
              if (UserGroupSelectorConstants.isGroupOnlyDisplay(controller.displayType.value) ||
                  UserGroupSelectorConstants.isBothUserAndGroupDisplay(controller.displayType.value))
                _buildGroupSection(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建多选模式的应用栏
  AppBar _buildMultipleSelectionAppBar() {
    return AppBar(
      title: Text(controller.title.value),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios_new, size: 20),
        onPressed: _handleMultipleSelectionBack,
      ),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(50),
        child: SelectorSearchBar(onChanged: controller.filterItems, hintText: controller.getSearchHint()),
      ),
      actions: [
        // 排序按钮
        Padding(
          padding: const EdgeInsets.only(right: 10),
          child: Container(
            margin: const EdgeInsets.only(right: 10),
            width: 25,
            height: 25,
            child: Obx(() {
              return GestureDetector(
                onTap: () {
                  controller.sortMode.toggle();
                  controller.onSortBtnClick(controller.sortMode.value);
                },
                child: controller.sortMode.value
                    ? SvgPicture.asset(
                        Assets.iconsSortAlphaDown,
                        colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                      )
                    : SvgPicture.asset(
                        Assets.iconsSortAlphaDownAlt,
                        colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                      ),
              );
            }),
          ),
        ),
      ],
    );
  }

  /// 构建用户部分
  Widget _buildUserSection() {
    return Column(
      children: [
        ExpandableHeaderWidget(
          title: NotificationSelectConstants.userNameHeader,
          onTap: controller.toggleUserNameExpansion,
          isExpanded: controller.isUserNameExpanded,
        ),
        Obx(() => controller.isUserNameExpanded.value ? _buildUserList() : const SizedBox.shrink()),
      ],
    );
  }

  /// 构建组部分
  Widget _buildGroupSection() {
    return Column(
      children: [
        ExpandableHeaderWidget(
          title: NotificationSelectConstants.groupNameHeader,
          onTap: controller.toggleGroupNameExpansion,
          isExpanded: controller.isGroupNameExpanded,
        ),
        Obx(() => controller.isGroupNameExpanded.value ? _buildGroupList() : const SizedBox.shrink()),
      ],
    );
  }

  /// 构建用户列表（多选模式）
  Widget _buildUserList() {
    return _buildMultiSelectListWidget<SharedUserModel>(
      itemList: controller.filteredUserList,
      getDisplayName: NotificationDisplayUtils.getUserDisplayName,
      getSubtitle: NotificationDisplayUtils.getUserSubtitle,
      onItemTap: controller.toggleUserSelection,
      isSelected: controller.isUserSelected,
    );
  }

  /// 构建组列表（多选模式）
  Widget _buildGroupList() {
    return _buildMultiSelectListWidget<SharedRoleModel>(
      itemList: controller.filteredGroupList,
      getDisplayName: NotificationDisplayUtils.getRoleDisplayName,
      getSubtitle: NotificationDisplayUtils.getRoleSubtitle,
      onItemTap: controller.toggleGroupSelection,
      isSelected: controller.isGroupSelected,
    );
  }

  /// 构建多选列表组件
  Widget _buildMultiSelectListWidget<T>({
    required RxList<T> itemList,
    required String Function(T) getDisplayName,
    required String? Function(T) getSubtitle,
    required void Function(T) onItemTap,
    required bool Function(T) isSelected,
  }) {
    if (itemList.isEmpty) {
      return Container(
        margin: const EdgeInsets.only(left: 10, right: 10, top: 5),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: Colors.white.withValues(alpha: 0.85)),
        child: const Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: Text(
              NotificationSelectConstants.emptyDataMessage,
              style: TextStyle(color: Colors.grey, fontSize: 14),
            ),
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.only(left: 10, right: 10, top: 5),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: Colors.white.withValues(alpha: 0.85)),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Obx(
          () => ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.zero,
            itemCount: itemList.length,
            itemBuilder: (context, index) {
              final item = itemList[index];
              final displayName = getDisplayName(item);
              final isLastItem = index == itemList.length - 1;

              // 为每个项目生成唯一的 key，基于类型和 ID
              final itemKey = item is SharedUserModel
                  ? 'user_${item.userId}'
                  : 'group_${(item as SharedRoleModel).roleId}';

              return Obx(() {
                final selected = isSelected(item);

                return Column(
                  key: ValueKey(itemKey),
                  children: [
                    ListTile(
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                      dense: true,
                      title: Text(displayName, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                      trailing: selected
                          ? const Icon(Icons.check_circle, color: AppTheme.darkBlueColor)
                          : const SizedBox.shrink(),
                      onTap: () => onItemTap(item),
                    ),
                    if (!isLastItem)
                      const Divider(
                        height: NotificationSelectConstants.dividerHeight,
                        thickness: NotificationSelectConstants.dividerThickness,
                        indent: NotificationSelectConstants.dividerIndent,
                        endIndent: NotificationSelectConstants.dividerEndIndent,
                        color: Colors.grey,
                      ),
                  ],
                );
              });
            },
          ),
        ),
      ),
    );
  }

  /// 处理多选模式的返回逻辑
  void _handleMultipleSelectionBack() {
    // 调用控制器的确认选择方法，返回当前选中的数据
    controller.confirmMultipleSelection();
  }
}

/// 用户组选择器常量定义
class UserGroupSelectorConstants {
  // 私有构造函数，防止实例化
  UserGroupSelectorConstants._();

  /// 选择模式常量
  static const String selectionModeSingle = 'single';
  static const String selectionModeMultiple = 'multiple';

  /// 显示类型常量
  static const String displayTypeUser = 'user';
  static const String displayTypeGroup = 'group';
  static const String displayTypeBoth = 'both';

  /// 来源页面常量
  static const String fromPageChooseTantousha = 'choose-tantousha-view';
  static const String fromPageScheduleAlert = 'schedule-alert';
  static const String fromPageGroupSelect = 'groupSelect';
  static const String fromPageNotification = 'notification';
  static const String fromPageExample = 'example';
  static const String fromPageLegacy = 'legacy';
  static const String fromPageCustom = 'custom';

  /// 页面标题常量
  static const String titleTantoushaSelect = '担当者選択';
  static const String titleNotificationSelect = '通知先';
  static const String titleGroupSelect = 'グループ選択';
  static const String titleUserGroupSelect = '担当者・グループ選択';
  static const String titleTantousha = '担当者';
  static const String titleGroup = 'グループ';

  /// 搜索提示文本常量
  static const String searchHintUser = '担当者を検索';
  static const String searchHintGroup = 'グループを検索';
  static const String searchHintBoth = '検索';

  /// 按钮文本常量
  static const String buttonClear = 'クリア';
  static const String buttonConfirm = '選択完了';
  static const String buttonSelectItems = '項目を選択してください';

  /// 消息文本常量
  static const String messageAllItemsDisplayed = '全件表示されました';
  static const String messageSelectedCount = '件選択';

  /// JSON 键名常量
  static const String jsonKeyInfo = 'info';
  static const String jsonKeyType = 'type';
  static const String jsonKeyUsers = 'users';
  static const String jsonKeyGroups = 'groups';
  static const String jsonKeySelectionMode = 'selectionMode';

  /// 动态领导者参数键名常量
  static const String dynamicLeaderProcDefId = 'procDefId';
  static const String dynamicLeaderTaskDefKey = 'taskDefKey';

  /// UI 相关常量
  static const double bottomBarPadding = 16.0;
  static const double bottomBarButtonVerticalPadding = 12.0;
  static const double bottomBarSpacing = 16.0;
  static const int bottomBarButtonFlex = 2;

  /// 验证方法
  static bool isValidSelectionMode(String mode) {
    return mode == selectionModeSingle || mode == selectionModeMultiple;
  }

  static bool isValidDisplayType(String type) {
    return type == displayTypeUser || type == displayTypeGroup || type == displayTypeBoth;
  }

  static bool isSingleSelectionMode(String mode) {
    return mode == selectionModeSingle;
  }

  static bool isMultipleSelectionMode(String mode) {
    return mode == selectionModeMultiple;
  }

  static bool isUserOnlyDisplay(String type) {
    return type == displayTypeUser;
  }

  static bool isGroupOnlyDisplay(String type) {
    return type == displayTypeGroup;
  }

  static bool isBothUserAndGroupDisplay(String type) {
    return type == displayTypeBoth;
  }

  /// 获取默认搜索提示文本
  static String getDefaultSearchHint(String displayType, {String title = ''}) {
    switch (displayType) {
      case displayTypeBoth:
        if (title.isNotEmpty) {
          return '$titleを$searchHintBoth';
        } else {
          return searchHintBoth;
        }
      case displayTypeGroup:
        return searchHintGroup;
      case displayTypeUser:
      default:
        return searchHintUser;
    }
  }

  /// 获取默认标题
  static String getDefaultTitle(String displayType, String? fromPage) {
    if (fromPage != null) {
      switch (fromPage) {
        case fromPageChooseTantousha:
          return titleTantoushaSelect;
        case fromPageScheduleAlert:
          return titleNotificationSelect;
        case fromPageGroupSelect:
          return titleGroupSelect;
      }
    }

    switch (displayType) {
      case displayTypeBoth:
        return titleUserGroupSelect;
      case displayTypeGroup:
        return titleGroupSelect;
      case displayTypeUser:
      default:
        return titleTantoushaSelect;
    }
  }
}

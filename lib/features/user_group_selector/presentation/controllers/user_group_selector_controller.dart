import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/data/models/user_select_model.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_role_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/list_selector/domain/models/list_selector_params.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_mixin.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/domain/models/user_group_selector_params.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/domain/models/user_group_selector_result.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/domain/usecase/get_group_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/domain/usecase/get_user_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/presentation/constants/user_group_selector_constants.dart';
import 'package:get/get.dart';

/// 用户组选择器控制器
///
/// 负责担当者选择器页面的状态和业务逻辑
class UserGroupSelectorController extends BaseController with OverlayMixin {
  final GetUserListUseCase getUserListUseCase;
  final GetGroupListUseCase getGroupListUseCase;
  final GetRoleListUseCase getRoleListUseCase;

  /// 页面参数
  UserGroupSelectorParams? params;

  /// 全件表示
  final showEndText = false.obs;

  /// 页面标题
  final RxString title = ''.obs;

  /// 选择模式：single 或 multiple
  final RxString selectionMode = UserGroupSelectorConstants.selectionModeSingle.obs;

  /// 显示类型：user, group, both
  final RxString displayType = UserGroupSelectorConstants.displayTypeUser.obs;

  /// 来向页面
  final RxString fromPage = ''.obs;

  /// 所有用户列表
  final RxList<SharedUserModel> userList = <SharedUserModel>[].obs;

  /// 所有组列表
  final RxList<SharedRoleModel> groupList = <SharedRoleModel>[].obs;

  /// 过滤后的用户列表
  final RxList<SharedUserModel> filteredUserList = <SharedUserModel>[].obs;

  /// 过滤后的组列表
  final RxList<SharedRoleModel> filteredGroupList = <SharedRoleModel>[].obs;

  /// 过滤后的显示项目（用于单选模式）
  final RxList<dynamic> filteredItems = [].obs;

  /// 选中的用户列表（多选模式）
  final RxList<SharedUserModel> selectedUsers = <SharedUserModel>[].obs;

  /// 选中的组列表（多选模式）
  final RxList<SharedRoleModel> selectedGroups = <SharedRoleModel>[].obs;

  /// 用户名header的展开状态（多选模式）
  final RxBool isUserNameExpanded = true.obs;

  /// 组名header的展开状态（多选模式）
  final RxBool isGroupNameExpanded = true.obs;

  /// 动态领导者信息
  Map<String, dynamic>? dynamicLeader = {};

  /// 选中项的索引（单选模式）
  final RxInt selectedIndex = RxInt(-1);

  /// 初始选中的用户ID（单选模式）
  int? initialSelectedUserId;

  /// 初始选中的用户ID列表（多选模式）
  List<int>? initialSelectedUserIds;

  /// 初始选中的组ID列表（多选模式）
  List<int>? initialSelectedGroupIds;

  /// 排序模式（true: 升序, false: 降序）
  final RxBool sortMode = true.obs;

  UserGroupSelectorController({
    required this.getUserListUseCase,
    required this.getGroupListUseCase,
    required this.getRoleListUseCase,
  });

  @override
  void onReady() {
    super.onReady();
    showEndText.value = false;
    _parseArguments();
    _loadUserList();
  }

  /// 解析传入的参数
  void _parseArguments() {
    final args = Get.arguments;

    // 优先使用新的参数模型
    if (args is UserGroupSelectorParams) {
      params = args;
      title.value = args.title;
      selectionMode.value = args.selectionMode;
      displayType.value = args.displayType;
      fromPage.value = args.fromPage ?? '';
      dynamicLeader = args.dynamicLeader;
      initialSelectedUserId = args.initialSelectedUserId;

      // 初始化多选状态 - 存储初始选中的ID列表，在数据加载完成后设置选中状态
      if (args.initialSelectedUserIds != null) {
        initialSelectedUserIds = List<int>.from(args.initialSelectedUserIds!);
        LogUtil.d('设置初始选中用户ID列表: $initialSelectedUserIds');
      }
      if (args.initialSelectedGroupIds != null) {
        initialSelectedGroupIds = List<int>.from(args.initialSelectedGroupIds!);
        LogUtil.d('设置初始选中组ID列表: $initialSelectedGroupIds');
      }
    }
    // 兼容旧的参数模型
    else if (args is ListSelectorParams) {
      title.value = args.title;
      displayType.value = args.type ?? UserGroupSelectorConstants.displayTypeUser;
      fromPage.value = args.isFromPage ?? '';
      dynamicLeader = args.dynamicLeader;
      selectionMode.value = UserGroupSelectorConstants.selectionModeSingle; // 旧模式默认单选

      if (title.value.isEmpty) title.value = _getTitle();
    }

    // 设置默认标题
    if (title.value.isEmpty) title.value = _getTitle();
  }

  /// 加载数据
  Future<void> _loadUserList() async {
    try {
      showLoading();

      // 根据显示类型加载相应的数据
      if (UserGroupSelectorConstants.isUserOnlyDisplay(displayType.value) ||
          UserGroupSelectorConstants.isBothUserAndGroupDisplay(displayType.value)) {
        await _loadUsers();
      }

      if (UserGroupSelectorConstants.isGroupOnlyDisplay(displayType.value) ||
          UserGroupSelectorConstants.isBothUserAndGroupDisplay(displayType.value)) {
        await _loadGroups();
      }

      // 更新过滤后的列表和选中状态
      _updateFilteredItems();
      _setInitialSelection();
    } catch (e) {
      LogUtil.d('获取数据失败: $e');
    } finally {
      hideLoading();
    }
  }

  /// 加载用户数据
  Future<void> _loadUsers() async {
    final users = await getUserListUseCase(const NoParams());
    LogUtil.d('从服务器获取到用户列表: ${users.length}个用户');

    userList.clear();
    userList.assignAll(users);
  }

  /// 加载组数据
  Future<void> _loadGroups() async {
    List<SharedRoleModel> groups;

    // 检查是否有 dynamicLeader 参数
    final procDefId = dynamicLeader?[UserGroupSelectorConstants.dynamicLeaderProcDefId] ?? '';
    final taskDefKey = dynamicLeader?[UserGroupSelectorConstants.dynamicLeaderTaskDefKey] ?? '';

    if (procDefId.isNotEmpty && taskDefKey.isNotEmpty) {
      // 有工作流参数，使用动态组列表
      groups = await getGroupListUseCase(GetGroupListParams(processDefinitionId: procDefId, taskDefKey: taskDefKey));
      LogUtil.d('从服务器获取到动态组列表: ${groups.length}个组');
    } else {
      // 没有工作流参数，使用通用角色列表
      groups = await getRoleListUseCase(const NoParams());
      LogUtil.d('从服务器获取到通用角色列表: ${groups.length}个角色');
    }

    groupList.clear();
    groupList.assignAll(groups);
  }

  /// 设置初始选中状态
  void _setInitialSelection() {
    if (UserGroupSelectorConstants.isSingleSelectionMode(selectionMode.value)) {
      // 单选模式：设置选中索引
      if (initialSelectedUserId != null) {
        final index = userList.indexWhere((user) => user.userId == initialSelectedUserId);
        if (index != -1) {
          selectedIndex.value = index;
          LogUtil.d('单选模式：设置初始选中用户索引: $index');
        }
      }
    } else {
      // 多选模式：设置选中的用户和组列表
      _setInitialMultipleSelection();
    }
  }

  /// 设置多选模式的初始选中状态
  void _setInitialMultipleSelection() {
    // 设置初始选中的用户
    if (initialSelectedUserIds != null && initialSelectedUserIds!.isNotEmpty) {
      selectedUsers.clear();
      for (final userId in initialSelectedUserIds!) {
        final user = userList.firstWhereOrNull((user) => user.userId == userId);
        if (user != null) {
          selectedUsers.add(user);
          LogUtil.d('多选模式：添加初始选中用户: ${user.userId} - ${user.lastName} ${user.firstName}');
        }
      }
      LogUtil.d('多选模式：初始选中用户总数: ${selectedUsers.length}');
    }

    // 设置初始选中的组
    if (initialSelectedGroupIds != null && initialSelectedGroupIds!.isNotEmpty) {
      selectedGroups.clear();
      for (final groupId in initialSelectedGroupIds!) {
        final group = groupList.firstWhereOrNull((group) => group.roleId == groupId);
        if (group != null) {
          selectedGroups.add(group);
          LogUtil.d('多选模式：添加初始选中组: ${group.roleId} - ${group.roleName}');
        }
      }
      LogUtil.d('多选模式：初始选中组总数: ${selectedGroups.length}');
    }
  }

  /// 更新过滤后的列表
  void _updateFilteredItems() {
    if (UserGroupSelectorConstants.isSingleSelectionMode(selectionMode.value)) {
      // 单选模式：使用原有的filteredItems逻辑
      filteredItems.clear();
      if (UserGroupSelectorConstants.isGroupOnlyDisplay(displayType.value)) {
        filteredItems.assignAll(groupList);
      } else {
        filteredItems.assignAll(userList.toList());
      }
      LogUtil.d('更新单选列表: ${filteredItems.length}个项目');
    } else {
      // 多选模式：分别更新用户和组列表
      filteredUserList.clear();
      filteredUserList.assignAll(userList);

      filteredGroupList.clear();
      filteredGroupList.assignAll(groupList);

      LogUtil.d('更新多选列表: ${filteredUserList.length}个用户, ${filteredGroupList.length}个组');
    }
  }

  /// 过滤列表项
  void filterItems(String keyword) {
    if (keyword.isEmpty) {
      _updateFilteredItems();
      return;
    }

    final lowercaseKeyword = keyword.toLowerCase();

    if (UserGroupSelectorConstants.isSingleSelectionMode(selectionMode.value)) {
      // 单选模式：使用原有逻辑
      if (UserGroupSelectorConstants.isGroupOnlyDisplay(displayType.value)) {
        final filtered = groupList.where((group) {
          final roleName = group.roleName.toLowerCase();
          return roleName.contains(lowercaseKeyword);
        }).toList();

        filteredItems.clear();
        filteredItems.assignAll(filtered);
        LogUtil.d('过滤后的组列表: ${filteredItems.length}个项目');
      } else {
        final filtered = userList.where((user) {
          return _matchesUserKeyword(user, lowercaseKeyword);
        }).toList();

        filteredItems.clear();
        filteredItems.assignAll(filtered);
        LogUtil.d('过滤后的用户列表: ${filteredItems.length}个项目');
      }
    } else {
      // 多选模式：分别过滤用户和组
      if (UserGroupSelectorConstants.isUserOnlyDisplay(displayType.value) ||
          UserGroupSelectorConstants.isBothUserAndGroupDisplay(displayType.value)) {
        final filteredUsers = userList.where((user) {
          return _matchesUserKeyword(user, lowercaseKeyword);
        }).toList();

        filteredUserList.clear();
        filteredUserList.assignAll(filteredUsers);
      }

      if (UserGroupSelectorConstants.isGroupOnlyDisplay(displayType.value) ||
          UserGroupSelectorConstants.isBothUserAndGroupDisplay(displayType.value)) {
        final filteredGroups = groupList.where((group) {
          final roleName = group.roleName.toLowerCase();
          return roleName.contains(lowercaseKeyword);
        }).toList();

        filteredGroupList.clear();
        filteredGroupList.assignAll(filteredGroups);
      }

      LogUtil.d('过滤后的列表: ${filteredUserList.length}个用户, ${filteredGroupList.length}个组');
    }
  }

  /// 检查用户是否匹配关键词
  bool _matchesUserKeyword(SharedUserModel user, String keyword) {
    final userName = (user.userName ?? '').toLowerCase();
    final lastName = (user.lastName ?? '').toLowerCase();
    final firstName = (user.firstName ?? '').toLowerCase();
    final lastNameKana = (user.lastNameKana ?? '').toLowerCase();
    final firstNameKana = (user.firstNameKana ?? '').toLowerCase();
    final fullName = '$lastName $firstName'.toLowerCase();
    final fullNameKana = '$lastNameKana $firstNameKana'.toLowerCase();

    return userName.contains(keyword) ||
        lastName.contains(keyword) ||
        firstName.contains(keyword) ||
        lastNameKana.contains(keyword) ||
        firstNameKana.contains(keyword) ||
        fullName.contains(keyword) ||
        fullNameKana.contains(keyword);
  }

  /// 获取用户显示名称
  String getUserDisplayName(dynamic user) {
    if (user is SharedUserModel) {
      return (user.lastName ?? '') + ' ' + (user.firstName ?? '');
    } else if (user is SharedRoleModel) {
      return user.roleName;
    } else {
      return user.toString();
    }
  }

  /// 多选模式：切换用户选中状态
  void toggleUserSelection(SharedUserModel user) {
    if (selectedUsers.contains(user)) {
      selectedUsers.remove(user);
      LogUtil.d('用户取消选中: ${user.userId} - ${user.lastName} ${user.firstName}');
    } else {
      selectedUsers.add(user);
      LogUtil.d('用户选中: ${user.userId} - ${user.lastName} ${user.firstName}');
    }
    LogUtil.d('当前选中用户数量: ${selectedUsers.length}');
    // 强制触发更新
    selectedUsers.refresh();
  }

  /// 多选模式：切换组选中状态
  void toggleGroupSelection(SharedRoleModel group) {
    if (selectedGroups.contains(group)) {
      selectedGroups.remove(group);
      LogUtil.d('组取消选中: ${group.roleId} - ${group.roleName}');
    } else {
      selectedGroups.add(group);
      LogUtil.d('组选中: ${group.roleId} - ${group.roleName}');
    }
    LogUtil.d('当前选中组数量: ${selectedGroups.length}');
    // 强制触发更新
    selectedGroups.refresh();
  }

  /// 多选模式：检查用户是否被选中
  bool isUserSelected(SharedUserModel user) {
    return selectedUsers.contains(user);
  }

  /// 多选模式：检查组是否被选中
  bool isGroupSelected(SharedRoleModel group) {
    return selectedGroups.contains(group);
  }

  /// 多选模式：切换用户名header的展开状态
  void toggleUserNameExpansion() {
    isUserNameExpanded.value = !isUserNameExpanded.value;
  }

  /// 多选模式：切换组名header的展开状态
  void toggleGroupNameExpansion() {
    isGroupNameExpanded.value = !isGroupNameExpanded.value;
  }

  /// 选择项目（单选模式）
  void selectItem(int index) {
    if (!UserGroupSelectorConstants.isSingleSelectionMode(selectionMode.value)) return;
    if (index < 0 || index >= filteredItems.length) return;

    selectedIndex.value = index;
    final selectedItem = filteredItems[index];

    // 创建结果对象
    UserGroupSelectorResult result;
    if (selectedItem is SharedUserModel) {
      result = UserGroupSelectorResult(selectedUsers: [selectedItem], selectionMode: selectionMode.value);
    } else if (selectedItem is SharedRoleModel) {
      result = UserGroupSelectorResult(selectedGroups: [selectedItem], selectionMode: selectionMode.value);
    } else {
      return;
    }

    // 兼容旧的返回格式
    if (fromPage.value == UserGroupSelectorConstants.fromPageChooseTantousha) {
      Get.back(result: result.toJson());
    } else {
      // 兼容UserSelectModel格式
      if (selectedItem is SharedUserModel) {
        Get.back(
          result: UserSelectModel(
            userId: selectedItem.userId ?? 0,
            userName: (selectedItem.lastName ?? '') + (selectedItem.firstName ?? ''),
          ),
        );
      } else {
        Get.back(result: result);
      }
    }
  }

  /// 多选模式：确认选择
  void confirmMultipleSelection() {
    if (!UserGroupSelectorConstants.isMultipleSelectionMode(selectionMode.value)) return;

    final result = UserGroupSelectorResult(
      selectedUsers: selectedUsers.toList(),
      selectedGroups: selectedGroups.toList(),
      selectionMode: selectionMode.value,
    );

    LogUtil.d('多选模式确认选择 - 用户: ${selectedUsers.length}个, 组: ${selectedGroups.length}个');
    LogUtil.d('返回数据格式: ${result.toJson()}');

    // 根据fromPage决定返回格式
    if (fromPage.value == UserGroupSelectorConstants.fromPageChooseTantousha || fromPage.value == 'assetAlert') {
      // 返回JSON格式，兼容现有的处理逻辑
      Get.back(result: result.toJson());
    } else {
      // 返回对象格式
      Get.back(result: result);
    }
  }

  /// 清除选择
  void onClearBtnClick() {
    if (UserGroupSelectorConstants.isSingleSelectionMode(selectionMode.value)) {
      selectedIndex.value = -1;
      Get.back(result: null);
    } else {
      // 多选模式：清空所有选择
      selectedUsers.clear();
      selectedGroups.clear();
    }
  }

  /// 手动刷新界面
  void refresh() {
    // 重新加载数据
    _loadUserList();
  }

  /// 获取当前选择的总数（多选模式）
  int get selectedCount => selectedUsers.length + selectedGroups.length;

  /// 是否有选中项（多选模式）
  bool get hasSelection => selectedCount > 0;

  /// 排序按钮点击事件
  void onSortBtnClick(bool isAscending) {
    if (UserGroupSelectorConstants.isSingleSelectionMode(selectionMode.value)) {
      // 单选模式：排序filteredItems
      if (UserGroupSelectorConstants.isGroupOnlyDisplay(displayType.value)) {
        filteredItems.sort((a, b) => isAscending ? a.roleName.compareTo(b.roleName) : b.roleName.compareTo(a.roleName));
      } else {
        filteredItems.sort(
          (a, b) => isAscending
              ? ((a.lastName ?? '') + (a.firstName ?? '')).compareTo((b.lastName ?? '') + (b.firstName ?? ''))
              : ((b.lastName ?? '') + (b.firstName ?? '')).compareTo((a.lastName ?? '') + (a.firstName ?? '')),
        );
      }
    } else {
      // 多选模式：分别排序用户和组列表
      if (UserGroupSelectorConstants.isUserOnlyDisplay(displayType.value) ||
          UserGroupSelectorConstants.isBothUserAndGroupDisplay(displayType.value)) {
        filteredUserList.sort(
          (a, b) => isAscending
              ? ((a.lastName ?? '') + (a.firstName ?? '')).compareTo((b.lastName ?? '') + (b.firstName ?? ''))
              : ((b.lastName ?? '') + (b.firstName ?? '')).compareTo((a.lastName ?? '') + (a.firstName ?? '')),
        );
      }

      if (UserGroupSelectorConstants.isGroupOnlyDisplay(displayType.value) ||
          UserGroupSelectorConstants.isBothUserAndGroupDisplay(displayType.value)) {
        filteredGroupList.sort(
          (a, b) => isAscending ? a.roleName.compareTo(b.roleName) : b.roleName.compareTo(a.roleName),
        );
      }
    }
  }

  String _getTitle() {
    // 如果已经设置了标题，直接返回
    if (title.value.isNotEmpty) return title.value;

    // 使用常量类的方法获取默认标题
    return UserGroupSelectorConstants.getDefaultTitle(displayType.value, fromPage.value);
  }

  /// 获取搜索提示文本
  String getSearchHint() {
    if (params?.searchHint != null) return params!.searchHint!;

    // 使用常量类的方法获取默认搜索提示
    return UserGroupSelectorConstants.getDefaultSearchHint(displayType.value, title: title.value);
  }
}

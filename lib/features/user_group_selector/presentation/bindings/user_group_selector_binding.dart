import 'package:asset_force_mobile_v2/features/home/<USER>/repositories/home_repositories_impl.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/repositories/home_repositories.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_role_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/user_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/domain/usecase/get_group_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/domain/usecase/get_user_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/presentation/controllers/user_group_selector_controller.dart';
import 'package:get/get.dart';

class UserGroupSelectorBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<UserRepository>(() => UserRepositoryImpl(dioUtil: Get.find()));
    Get.lazyPut<HomeRepository>(() => HomeRepositoriesImpl(dioUtil: Get.find()));

    Get.lazyPut<GetUserListUseCase>(() => GetUserListUseCase(userRepository: Get.find()));
    Get.lazyPut<GetGroupListUseCase>(() => GetGroupListUseCase(userRepository: Get.find()));
    Get.lazyPut<GetRoleListUseCase>(() => GetRoleListUseCase(homeRepository: Get.find()));

    Get.lazyPut<UserGroupSelectorController>(
      () => UserGroupSelectorController(
        getUserListUseCase: Get.find(),
        getGroupListUseCase: Get.find(),
        getRoleListUseCase: Get.find(),
      ),
    );
  }
}

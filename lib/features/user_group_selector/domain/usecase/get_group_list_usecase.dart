import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';

class GetGroupListParams {
  final String? processDefinitionId;
  final String? taskDefKey;
  GetGroupListParams({this.taskDefKey, this.processDefinitionId});
}

/// 获取group列表
///
/// 担当者列表
class GetGroupListUseCase extends UseCase<List<SharedRoleModel>, GetGroupListParams> {
  final UserRepository userRepository;

  GetGroupListUseCase({required this.userRepository});

  @override
  Future<List<SharedRoleModel>> call(GetGroupListParams getGroupListParams) async {
    return await userRepository.getGroupList(
      getGroupListParams.processDefinitionId ?? '',
      getGroupListParams.taskDefKey ?? '',
    );
  }
}

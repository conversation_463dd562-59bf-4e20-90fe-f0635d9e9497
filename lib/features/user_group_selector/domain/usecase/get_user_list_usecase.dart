import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';

/// 获取用户列表
///
/// 担当者列表
class GetUserListUseCase extends UseCase<List<SharedUserModel>, NoParams> {
  final UserRepository userRepository;

  GetUserListUseCase({required this.userRepository});

  @override
  Future<List<SharedUserModel>> call(NoParams params) async {
    return await userRepository.getUserList();
  }
}

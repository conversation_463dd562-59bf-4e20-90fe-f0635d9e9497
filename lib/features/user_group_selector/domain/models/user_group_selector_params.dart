import 'package:asset_force_mobile_v2/features/user_group_selector/presentation/constants/user_group_selector_constants.dart';

/// 用户组选择器参数模型
class UserGroupSelectorParams {
  /// 页面标题
  final String title;

  /// 选择模式：'single' 单选模式，'multiple' 多选模式
  final String selectionMode;

  /// 显示类型：'user' 仅用户，'group' 仅组，'both' 用户和组
  final String displayType;

  /// 来源页面标识
  final String? fromPage;

  /// 动态领导者信息（用于组列表获取）
  final Map<String, dynamic>? dynamicLeader;

  /// 初始选中的用户ID（单选模式）
  final int? initialSelectedUserId;

  /// 初始选中的组ID（单选模式）
  final int? initialSelectedGroupId;

  /// 初始选中的用户列表（多选模式）
  final List<int>? initialSelectedUserIds;

  /// 初始选中的组列表（多选模式）
  final List<int>? initialSelectedGroupIds;

  /// 是否显示清除按钮
  final bool showClearButton;

  /// 是否显示排序按钮
  final bool showSortButton;

  /// 自定义搜索提示文本
  final String? searchHint;

  const UserGroupSelectorParams({
    required this.title,
    this.selectionMode = UserGroupSelectorConstants.selectionModeSingle, // 默认单选模式
    this.displayType = UserGroupSelectorConstants.displayTypeUser, // 默认显示用户
    this.fromPage,
    this.dynamicLeader,
    this.initialSelectedUserId,
    this.initialSelectedGroupId,
    this.initialSelectedUserIds,
    this.initialSelectedGroupIds,
    this.showClearButton = true,
    this.showSortButton = true,
    this.searchHint,
  });

  /// 是否为单选模式
  bool get isSingleSelection => UserGroupSelectorConstants.isSingleSelectionMode(selectionMode);

  /// 是否为多选模式
  bool get isMultipleSelection => UserGroupSelectorConstants.isMultipleSelectionMode(selectionMode);

  /// 是否仅显示用户
  bool get isUserOnly => UserGroupSelectorConstants.isUserOnlyDisplay(displayType);

  /// 是否仅显示组
  bool get isGroupOnly => UserGroupSelectorConstants.isGroupOnlyDisplay(displayType);

  /// 是否同时显示用户和组
  bool get isBothUserAndGroup => UserGroupSelectorConstants.isBothUserAndGroupDisplay(displayType);

  /// 获取搜索提示文本
  String getSearchHint() {
    if (searchHint != null) return searchHint!;

    return UserGroupSelectorConstants.getDefaultSearchHint(displayType);
  }

  /// 复制并修改参数
  UserGroupSelectorParams copyWith({
    String? title,
    String? selectionMode,
    String? displayType,
    String? fromPage,
    Map<String, dynamic>? dynamicLeader,
    int? initialSelectedUserId,
    int? initialSelectedGroupId,
    List<int>? initialSelectedUserIds,
    List<int>? initialSelectedGroupIds,
    bool? showClearButton,
    bool? showSortButton,
    String? searchHint,
  }) {
    return UserGroupSelectorParams(
      title: title ?? this.title,
      selectionMode: selectionMode ?? this.selectionMode,
      displayType: displayType ?? this.displayType,
      fromPage: fromPage ?? this.fromPage,
      dynamicLeader: dynamicLeader ?? this.dynamicLeader,
      initialSelectedUserId: initialSelectedUserId ?? this.initialSelectedUserId,
      initialSelectedGroupId: initialSelectedGroupId ?? this.initialSelectedGroupId,
      initialSelectedUserIds: initialSelectedUserIds ?? this.initialSelectedUserIds,
      initialSelectedGroupIds: initialSelectedGroupIds ?? this.initialSelectedGroupIds,
      showClearButton: showClearButton ?? this.showClearButton,
      showSortButton: showSortButton ?? this.showSortButton,
      searchHint: searchHint ?? this.searchHint,
    );
  }
}

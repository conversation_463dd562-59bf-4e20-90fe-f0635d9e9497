import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/presentation/constants/user_group_selector_constants.dart';

/// 用户组选择器结果模型
class UserGroupSelectorResult {
  /// 选中的用户列表
  final List<SharedUserModel> selectedUsers;

  /// 选中的组列表
  final List<SharedRoleModel> selectedGroups;

  /// 选择模式
  final String selectionMode;

  const UserGroupSelectorResult({
    this.selectedUsers = const [],
    this.selectedGroups = const [],
    required this.selectionMode,
  });

  /// 是否为单选模式
  bool get isSingleSelection => UserGroupSelectorConstants.isSingleSelectionMode(selectionMode);

  /// 是否为多选模式
  bool get isMultipleSelection => UserGroupSelectorConstants.isMultipleSelectionMode(selectionMode);

  /// 获取单选的用户（仅在单选模式下使用）
  SharedUserModel? get singleSelectedUser => isSingleSelection && selectedUsers.isNotEmpty ? selectedUsers.first : null;

  /// 获取单选的组（仅在单选模式下使用）
  SharedRoleModel? get singleSelectedGroup =>
      isSingleSelection && selectedGroups.isNotEmpty ? selectedGroups.first : null;

  /// 是否有选中项
  bool get hasSelection => selectedUsers.isNotEmpty || selectedGroups.isNotEmpty;

  /// 获取选中项总数
  int get totalSelectedCount => selectedUsers.length + selectedGroups.length;

  /// 转换为JSON（用于兼容现有代码）
  Map<String, dynamic> toJson() {
    if (isSingleSelection) {
      // 单选模式：返回单个项目的信息
      if (selectedUsers.isNotEmpty) {
        return {
          UserGroupSelectorConstants.jsonKeyInfo: selectedUsers.first.toJson(),
          UserGroupSelectorConstants.jsonKeyType: UserGroupSelectorConstants.displayTypeUser,
        };
      } else if (selectedGroups.isNotEmpty) {
        return {
          UserGroupSelectorConstants.jsonKeyInfo: selectedGroups.first.toJson(),
          UserGroupSelectorConstants.jsonKeyType: UserGroupSelectorConstants.displayTypeGroup,
        };
      }
      return {};
    } else {
      // 多选模式：返回所有选中项
      return {
        UserGroupSelectorConstants.jsonKeyUsers: selectedUsers.map((user) => user.toJson()).toList(),
        UserGroupSelectorConstants.jsonKeyGroups: selectedGroups.map((group) => group.toJson()).toList(),
        UserGroupSelectorConstants.jsonKeySelectionMode: selectionMode,
      };
    }
  }

  /// 从JSON创建（用于兼容现有代码）
  factory UserGroupSelectorResult.fromJson(Map<String, dynamic> json, String selectionMode) {
    if (UserGroupSelectorConstants.isSingleSelectionMode(selectionMode)) {
      final info = json[UserGroupSelectorConstants.jsonKeyInfo] as Map<String, dynamic>?;
      final type = json[UserGroupSelectorConstants.jsonKeyType] as String?;

      if (info != null && type != null) {
        if (type == UserGroupSelectorConstants.displayTypeUser) {
          return UserGroupSelectorResult(selectedUsers: [SharedUserModel.fromJson(info)], selectionMode: selectionMode);
        } else if (type == UserGroupSelectorConstants.displayTypeGroup) {
          return UserGroupSelectorResult(
            selectedGroups: [SharedRoleModel.fromJson(info)],
            selectionMode: selectionMode,
          );
        }
      }
    } else {
      final users =
          (json[UserGroupSelectorConstants.jsonKeyUsers] as List?)
              ?.map((user) => SharedUserModel.fromJson(user as Map<String, dynamic>))
              .toList() ??
          [];
      final groups =
          (json[UserGroupSelectorConstants.jsonKeyGroups] as List?)
              ?.map((group) => SharedRoleModel.fromJson(group as Map<String, dynamic>))
              .toList() ??
          [];

      return UserGroupSelectorResult(selectedUsers: users, selectedGroups: groups, selectionMode: selectionMode);
    }

    return UserGroupSelectorResult(selectionMode: selectionMode);
  }

  /// 复制并修改结果
  UserGroupSelectorResult copyWith({
    List<SharedUserModel>? selectedUsers,
    List<SharedRoleModel>? selectedGroups,
    String? selectionMode,
  }) {
    return UserGroupSelectorResult(
      selectedUsers: selectedUsers ?? this.selectedUsers,
      selectedGroups: selectedGroups ?? this.selectedGroups,
      selectionMode: selectionMode ?? this.selectionMode,
    );
  }
}

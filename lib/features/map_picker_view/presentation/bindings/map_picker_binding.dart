import 'package:asset_force_mobile_v2/core/utils/google_location_utils.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/domain/models/map_picker_params.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/domain/states/map_picker_ui_state.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/domain/usecases/get_api_key_usecase.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/domain/usecases/get_webview_url_usecase.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/presentation/controllers/map_picker_controller.dart';
import 'package:get/get.dart';

class MapPickerBinding extends Bindings {
  @override
  void dependencies() {
    final params = Get.arguments as MapPickerParams;
    // 初始化GoogleLocationService
    Get.lazyPut(() => GoogleLocationService());

    // 初始化GetApiKeyUseCase
    Get.lazyPut(() => GetApiKeyUseCase(Get.find()));
    // 初始化GetWebViewUrlUseCase
    Get.lazyPut(() => GetWebViewUrlUseCase(Get.find()));

    // 初始化MapPickerUiState
    Get.lazyPut(() => MapPickerUiState());

    // 初始化MapPickerController
    Get.lazyPut(
      () => MapPickerController(
        uiState: Get.find(),
        params: params,
        getApiKeyUseCase: Get.find(),
        getWebViewUrlUseCase: Get.find(),
      ),
    );
  }
}

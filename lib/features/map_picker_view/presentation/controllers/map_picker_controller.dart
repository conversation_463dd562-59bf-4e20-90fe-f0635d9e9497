import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/domain/models/map_picker_params.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/domain/usecases/get_api_key_usecase.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/domain/usecases/get_webview_url_usecase.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_mixin.dart';
import 'package:get/get.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/domain/states/map_picker_ui_state.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:async';

/// 地图选择器控制器
///
/// 用于控制地图选择器页面的显示和交互
///
/// 主要功能:
/// - 管理页面状态和数据
/// - 处理地图相关操作
/// - 处理搜索功能
/// - 管理WebView显示
class MapPickerController extends BaseController with OverlayMixin {
  /// UI状态对象,用于管理页面显示状态
  final MapPickerUiState uiState;

  /// 页面参数,包含标题和当前地址信息
  final MapPickerParams params;

  /// WebView控制器,用于控制地图显示
  final webViewController = WebViewController();

  /// 获取Google Maps API密钥的用例
  final GetApiKeyUseCase getApiKeyUseCase;

  /// 获取地图WebView URL的用例
  final GetWebViewUrlUseCase getWebViewUrlUseCase;

  /// Google Maps API密钥
  final RxString apiKey = ''.obs;

  /// 构造函数
  MapPickerController({
    required this.uiState,
    required this.params,
    required this.getApiKeyUseCase,
    required this.getWebViewUrlUseCase,
  });

  @override
  void onInit() {
    super.onInit();
    _initializeController();
  }

  /// 初始化控制器
  ///
  /// - 设置页面标题
  /// - 设置初始地址
  /// - 添加搜索监听
  /// - 初始化API密钥
  void _initializeController() {
    uiState.pageTitle.value = params.title;
    uiState.searchController.text = params.currentAddress;
    uiState.currentInput.value = params.currentAddress;
    uiState.initSearchListener(_updateMapView);
    _initApiKey();
  }

  @override
  void onClose() {
    uiState.dispose();
    super.onClose();
  }

  /// 初始化Google Maps API密钥
  ///
  /// 从后端获取API密钥并更新地图显示
  Future<void> _initApiKey() async {
    showLoading();
    try {
      final key = await getApiKeyUseCase(const NoParams());
      if (key != null) {
        apiKey.value = key;
        await _updateMapView();
      }
    } catch (e) {
      uiState.errorMessage.value = '获取API密钥失败';
    } finally {
      hideLoading();
    }
  }

  /// 更新地图视图
  ///
  /// - 获取新的地图URL
  /// - 更新WebView显示
  /// - 处理加载状态和错误
  Future<void> _updateMapView() async {
    if (apiKey.value.isEmpty) return;

    try {
      final webViewUrl = await getWebViewUrlUseCase(
        GetWebViewUrlParams(apiKey: apiKey.value, addressVal: uiState.searchController.text),
      );

      if (webViewUrl != null) {
        await _loadWebView(webViewUrl);
      }
    } catch (e) {
      uiState.errorMessage.value = '更新地图失败';
    }
  }

  /// 加载WebView内容
  ///
  /// 使用HTML模板加载地图iframe:
  /// - 设置viewport
  /// - 配置全屏显示样式
  /// - 加载地图URL
  Future<void> _loadWebView(String url) async {
    final htmlTemplate =
        '''
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body, html { margin: 0; padding: 0; width: 100%; height: 100%; overflow: hidden; }
            iframe { width: 100%; height: 100%; border: none; }
          </style>
        </head>
        <body>
          <iframe src="$url" allowfullscreen></iframe>
        </body>
      </html>
    ''';

    await webViewController.loadHtmlString(htmlTemplate);
  }

  /// 处理关闭按钮点击
  ///
  /// 返回选择的地址结果
  void onCloseBtnClick() {
    Get.back(result: MapPickerResult(address: uiState.searchController.text));
  }

  /// 清空搜索框
  void clearSearch() {
    uiState.clearSearch();
    _updateMapView();
  }
}

/// 地图选择结果类
///
/// 用于封装地图选择后返回的结果数据
///
/// [address] 选择的地址字符串
class MapPickerResult {
  /// 选择的地址
  final String address;

  /// 构造函数
  ///
  /// [address] 必填参数,表示选择的地址
  MapPickerResult({required this.address});
}

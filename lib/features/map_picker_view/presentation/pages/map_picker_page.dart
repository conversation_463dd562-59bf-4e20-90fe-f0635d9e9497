import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/presentation/bindings/map_picker_binding.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/presentation/controllers/map_picker_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// 地图选择器页面
///
/// 用于显示地图选择器界面,包含以下功能:
/// - 显示地图WebView
/// - 提供地址搜索功能
/// - 支持清除搜索内容
/// - 支持返回操作
@GetRoutePage('/map_picker', binding: MapPickerBinding)
class MapPickerPage extends GetView<MapPickerController> {
  /// 构造函数
  const MapPickerPage({super.key});

  /// 页面内边距
  static const double _padding = 10.0;

  /// 圆角大小
  static const double _borderRadius = 5.0;

  /// 图标大小
  static const double _iconSize = 20.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(appBar: _buildAppBar(), body: _buildBody());
  }

  /// 构建应用栏
  ///
  /// 包含:
  /// - 居中标题
  /// - 返回按钮
  /// - 透明背景
  AppBar _buildAppBar() {
    return AppBar(
      centerTitle: true,
      shadowColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      title: Obx(() => Text(controller.uiState.pageTitle.value)),
      foregroundColor: Colors.white,
      leadingWidth: 50,
      leading: _buildBackButton(),
    );
  }

  /// 构建返回按钮
  Widget _buildBackButton() {
    return IconButton(
      icon: const Icon(Icons.arrow_back_ios_new, size: _iconSize),
      onPressed: controller.onCloseBtnClick,
    );
  }

  /// 构建页面主体
  ///
  /// 添加外边距和圆角
  Widget _buildBody() {
    return Container(
      margin: const EdgeInsets.all(_padding),
      child: ClipRRect(borderRadius: BorderRadius.circular(_borderRadius), child: _buildContentArea()),
    );
  }

  /// 构建内容区域
  ///
  /// 包含:
  /// - 搜索框
  /// - 地图WebView
  Widget _buildContentArea() {
    return Container(
      decoration: BoxDecoration(color: AppTheme.white85Color, borderRadius: BorderRadius.circular(_borderRadius)),
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: _padding),
      child: Column(
        children: [
          _buildSearchField(),
          const SizedBox(height: _padding),
          _buildWebView(),
        ],
      ),
    );
  }

  /// 构建搜索输入框
  Widget _buildSearchField() {
    return TextField(
      controller: controller.uiState.searchController,
      style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
      decoration: InputDecoration(border: const UnderlineInputBorder(), suffixIcon: _buildClearButton()),
    );
  }

  /// 构建清除按钮
  ///
  /// 当输入框有内容时显示清除按钮
  Widget _buildClearButton() {
    return Obx(
      () => controller.uiState.currentInput.value.isNotEmpty
          ? IconButton(
              icon: SvgPicture.asset(
                'assets/icons/icon-circle_clear.svg',
                width: _iconSize,
                height: _iconSize,
                colorFilter: const ColorFilter.mode(Colors.black54, BlendMode.srcIn),
              ),
              onPressed: controller.clearSearch,
            )
          : const SizedBox(),
    );
  }

  /// 构建地图WebView
  Widget _buildWebView() {
    return Expanded(child: WebViewWidget(controller: controller.webViewController));
  }
}

import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/google_location_utils.dart';

/// 获取Google地图WebView URL的用例
///
/// 该用例通过[GoogleLocationService]获取Google地图嵌入式URL
///
/// 参数:
/// - [apiKey] Google地图API密钥,必需参数
/// - [addressVal] 地址值,可选参数,默认为"日本"
///
/// 返回:
/// - 成功时返回Google地图嵌入式URL字符串
/// - 失败时返回null
class GetWebViewUrlUseCase extends UseCase<String?, GetWebViewUrlParams> {
  final GoogleLocationService _googleLocationService;

  GetWebViewUrlUseCase(this._googleLocationService);

  @override
  Future<String?> call(GetWebViewUrlParams params) async {
    final String apiKey = params.apiKey;
    final String addressVal = params.addressVal;
    return await _googleLocationService.getGoogleMapIframeUrl(apiKey: apiKey, addressVal: addressVal);
  }
}

/// 获取WebView URL的参数类
///
/// 参数:
/// - [apiKey] Google地图API密钥,必需参数
/// - [addressVal] 地址值,必需参数,用于定位地图位置
class GetWebViewUrlParams {
  /// Google地图API密钥
  final String apiKey;

  /// 地址值
  final String addressVal;

  /// 创建获取WebView URL的参数对象
  ///
  /// [apiKey] 和 [addressVal] 都是必需参数
  GetWebViewUrlParams({required this.apiKey, required this.addressVal});
}

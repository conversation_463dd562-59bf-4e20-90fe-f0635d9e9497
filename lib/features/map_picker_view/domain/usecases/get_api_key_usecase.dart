import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/google_location_utils.dart';

/// 获取Google地图API密钥的用例
///
/// 该用例通过[GoogleLocationService]获取Google地图API密钥
///
/// 返回:
/// - 成功时返回API密钥字符串
/// - 失败时返回null

class GetApiKeyUseCase extends UseCase<String?, NoParams> {
  final GoogleLocationService _googleLocationService;

  GetApiKeyUseCase(this._googleLocationService);

  @override
  Future<String?> call(NoParams params) async {
    return await _googleLocationService.httpGetGooglePlatformApiKey();
  }
}

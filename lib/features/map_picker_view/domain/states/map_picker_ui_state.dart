import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:flutter/material.dart';
import 'dart:async';

class MapPickerUiState {
  /// 加载状态
  final RxBool isLoading = false.obs;

  /// 错误信息
  final RxString errorMessage = ''.obs;

  /// 选中的地址
  final RxString selectedAddress = ''.obs;

  /// 页面标题
  final RxString pageTitle = ''.obs;

  /// 搜索控制器
  final TextEditingController searchController = TextEditingController();

  /// 当前输入值
  final RxString currentInput = ''.obs;

  /// 最后一次搜索值
  final RxString lastSearchValue = ''.obs;

  /// 防抖定时器
  Timer? _debounceTimer;

  /// 初始化搜索监听
  void initSearchListener(Function() onSearch) {
    searchController.addListener(() {
      currentInput.value = searchController.text;
      _debounceTimer?.cancel();
      _debounceTimer = Timer(const Duration(milliseconds: 500), () {
        if (searchController.text != lastSearchValue.value) {
          lastSearchValue.value = searchController.text;
          onSearch();
        }
      });
    });
  }

  /// 清空搜索
  void clearSearch() {
    searchController.clear();
    currentInput.value = '';
    lastSearchValue.value = '';
  }

  /// 释放资源
  void dispose() {
    _debounceTimer?.cancel();
    searchController.dispose();
  }
}

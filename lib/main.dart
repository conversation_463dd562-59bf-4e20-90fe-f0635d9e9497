import 'dart:async';

import 'package:asset_force_mobile_v2/core/bindings/shared_binding.dart';
import 'package:asset_force_mobile_v2/core/deeplink/deeplink_service.dart';
import 'package:asset_force_mobile_v2/core/env/env_helper.dart';
import 'package:asset_force_mobile_v2/core/presentation/widgets/global_keyboard_overlay.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/routes/route_tacker.dart';
import 'package:asset_force_mobile_v2/core/routes/tracking_route_observer.dart';
import 'package:asset_force_mobile_v2/core/services/font_scale_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/overlay/floating_overlay_manager.dart';
import 'package:asset_force_mobile_v2/features/overlay/my_navigator_observer.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/controllers/navigator_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// 应用程序的入口点
///
/// 主要功能包括：
/// - 初始化日志工具
/// - 初始化持久化存储（MMKV）
/// - 初始化全局依赖
/// - 加载环境变量
/// - 设置全局异常处理机制（Flutter 框架异常 & Dart 异步异常）
/// - 锁定屏幕方向为竖屏
void main() async {
  // ⚠️ 设置 Zone 错误为致命错误（可选）
  BindingBase.debugZoneErrorsAreFatal = true;

  // -----------------------------
  // Dart 异步任务全局异常处理
  // -----------------------------
  runZonedGuarded(
    () async {
      // 确保 Flutter Binding 正确初始化
      WidgetsFlutterBinding.ensureInitialized();

      // 🔒 锁定屏幕方向为竖屏，禁止横屏自动转换
      await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

      // 初始化日志工具
      LogUtil.initialize(printer: MinimalPrettyPrinter());

      // 初始化持久化存储（MMKV）
      await StorageUtils.init();

      // 初始化全局依赖
      // SharedBinding().dependencies();

      // 🚀 加载环境变量文件
      await EnvHelper.init();

      // Flutter 框架层异常处理
      FlutterError.onError = (FlutterErrorDetails details) {
        FlutterError.presentError(details);
        LogUtil.e('Flutter Framework Error: ${details.exceptionAsString()}', stackTrace: details.stack);

        WidgetsBinding.instance.addPostFrameCallback((_) {
          CommonDialog.show(type: DialogType.error, content: 'システムエラーが発生しました。管理者にご連絡ください。');
        });
      };
      // 初始化DeepLink监听
      DeeplinkService().init();
      // 🚀 启动 Flutter 应用
      runApp(const MyApp());
    },
    (error, stackTrace) {
      // 记录 Dart 层异常到日志系统
      LogUtil.e('Uncaught Dart Exception: $error', stackTrace: stackTrace);

      // 安全地展示错误弹窗
      WidgetsBinding.instance.addPostFrameCallback((_) {
        CommonDialog.show(type: DialogType.error, content: 'システムエラーが発生しました。管理者にご連絡ください。');
      });
    },
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  FontScaleService get fontScaleService => Get.find<FontScaleService>();

  @override
  void initState() {
    super.initState();

    RouteTracker.init();
  }

  @override
  void dispose() {
    RouteTracker.reset();
    FloatingOverlayManager().dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 获取fortScaleService的对象
    return GetMaterialApp(
      defaultTransition: Transition.fade,
      // 默认值是300
      transitionDuration: const Duration(milliseconds: 0),
      title: 'Flutter Demo',
      theme: AppTheme.lightTheme,
      // 使用路由名称
      initialRoute: AutoRoutes.splash,
      initialBinding: SharedBinding(),
      navigatorKey: NavigatorController.navigatorKey,
      navigatorObservers: [MyNavigatorObserver(), TrackingRouteObserver(SharedNavBarEnum.home.navigatorId)],
      // 使用路由页面配置
      getPages: AutoRoutes.pages,
      builder: (context, child) {
        return Obx(() {
          final Widget scaledChild = MediaQuery(
            data: MediaQuery.of(
              context,
            ).copyWith(textScaler: TextScaler.linear(fontScaleService.textScaleFactor.value)),
            child: child!,
          );

          final wrappedChild = GlobalKeyBoardOverlay(
            child: Stack(
              children: [
                Container(
                  decoration: const BoxDecoration(
                    color: Colors.transparent,
                    image: DecorationImage(image: AssetImage('assets/images/bg.jpg'), fit: BoxFit.cover),
                  ),
                ),
                // 内容层
                scaledChild,
              ],
            ),
          );

          return wrappedChild;
        });
      },
    );
  }
}

import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/data/repositories/logout_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/domain/repositories/logout_repository.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 mock 类
@GenerateMocks([DioUtil])
import 'logout_repository_impl_test.mocks.dart';

void main() {
  group('LogoutRepositoryImpl', () {
    late LogoutRepositoryImpl repository;
    late MockDioUtil mockDioUtil;

    setUp(() {
      LogUtil.initialize();
      mockDioUtil = MockDioUtil();
      repository = LogoutRepositoryImpl(mockDioUtil);
    });

    tearDown(() {
      reset(mockDioUtil);
    });

    // Phase 0: 基础架构测试
    group('Phase 0: 基础架构测试', () {
      test('应该能够创建 LogoutRepositoryImpl 实例', () {
        // Assert
        expect(repository, isNotNull);
        expect(repository, isA<LogoutRepositoryImpl>());
      });

      test('应该实现 LogoutRepository 接口', () {
        // Assert
        expect(repository, isA<LogoutRepository>());
      });

      test('应该混入 RepositoryErrorHandler', () {
        // Assert
        expect(repository, isA<RepositoryErrorHandler>());
      });

      test('应该正确注入 DioUtil 依赖', () {
        // Arrange
        final testDioUtil = MockDioUtil();

        // Act
        final testRepository = LogoutRepositoryImpl(testDioUtil);

        // Assert
        expect(testRepository.dioUtil, equals(testDioUtil));
      });

      test('构造函数应该要求 DioUtil 参数', () {
        // Act & Assert - 验证构造函数参数为required
        expect(() => LogoutRepositoryImpl(mockDioUtil), returnsNormally);
      });

      test('应该具有所有必需的方法签名', () {
        // Assert - 验证所有接口方法都已实现
        expect(repository.logout, isA<Future<void> Function()>());
      });

      test('应该能够访问 RepositoryErrorHandler 的方法', () {
        // Arrange
        Future<String> testTask() async => 'test';

        // Act & Assert - 验证mixin方法可访问
        expect(() => repository.executeRepositoryTask(testTask, 'test error'), returnsNormally);
      });

      test('logout方法应该返回正确的类型', () async {
        // Arrange - 创建测试数据
        final successResponse = Response(
          data: {},
          statusCode: 200,
          requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
        );

        // Mock setup
        when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

        // Act & Assert - 验证返回类型
        final logoutResult = repository.logout();
        expect(logoutResult, isA<Future<void>>());

        // 确保能够正常完成
        await logoutResult;
      });
    });

    // Phase 1: logout() 方法核心测试
    group('Phase 1: logout() 方法核心测试', () {
      // 1.1 成功场景测试
      group('1.1 成功场景测试', () {
        test('应该成功执行logout - HTTP 200响应', () async {
          // Arrange
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

          // Act
          await repository.logout();

          // Assert
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该使用正确的URL调用DioUtil', () async {
          // Arrange
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

          // Act
          await repository.logout();

          // Assert
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
          verifyNoMoreInteractions(mockDioUtil);
        });

        test('应该处理空响应数据', () async {
          // Arrange
          final emptyResponse = Response(
            data: null,
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => emptyResponse);

          // Act & Assert - 应该正常完成不抛出异常
          await repository.logout();
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该处理包含响应数据的成功情况', () async {
          // Arrange
          final dataResponse = Response(
            data: {'status': 'success', 'message': 'Logout successful'},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => dataResponse);

          // Act & Assert
          await repository.logout();
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该处理HTTP 4xx错误状态码但不抛出异常', () async {
          // Arrange - HTTP错误状态码在当前实现中不会抛出异常
          final errorResponse = Response(
            data: {'error': 'Bad Request'},
            statusCode: 400,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => errorResponse);

          // Act & Assert - 应该正常完成，不抛出异常
          await repository.logout();
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该处理HTTP 5xx错误状态码但不抛出异常', () async {
          // Arrange - HTTP错误状态码在当前实现中不会抛出异常
          final serverErrorResponse = Response(
            data: {'error': 'Internal Server Error'},
            statusCode: 500,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => serverErrorResponse);

          // Act & Assert - 应该正常完成，不抛出异常
          await repository.logout();
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });
      });

      // 1.2 网络异常场景测试
      group('1.2 网络异常场景测试', () {
        test('应该抛出BusinessException当DioException.connectionTimeout发生时', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              type: DioExceptionType.connectionTimeout,
              message: 'Connection timeout',
            ),
          );

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<BusinessException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该抛出BusinessException当DioException.receiveTimeout发生时', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              type: DioExceptionType.receiveTimeout,
              message: 'Receive timeout',
            ),
          );

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<BusinessException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该抛出BusinessException当DioException.sendTimeout发生时', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              type: DioExceptionType.sendTimeout,
              message: 'Send timeout',
            ),
          );

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<BusinessException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该抛出BusinessException当DioException.connectionError发生时', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              type: DioExceptionType.connectionError,
              message: 'Connection error',
            ),
          );

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<BusinessException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该抛出BusinessException当DioException.badResponse发生时', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              type: DioExceptionType.badResponse,
              message: 'Bad response',
              response: Response(statusCode: 400, requestOptions: RequestOptions(path: GlobalVariable.secureLogout)),
            ),
          );

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<BusinessException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该抛出BusinessException当DioException.cancel发生时', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              type: DioExceptionType.cancel,
              message: 'Request cancelled',
            ),
          );

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<BusinessException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该抛出BusinessException当DioException.unknown发生时', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              type: DioExceptionType.unknown,
              message: 'Unknown error',
            ),
          );

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<BusinessException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该使用DioException消息作为BusinessException消息', () async {
          // Arrange
          final testMessage = 'Custom connection error';
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              type: DioExceptionType.connectionTimeout,
              message: testMessage,
            ),
          );

          // Act & Assert
          try {
            await repository.logout();
            fail('Expected BusinessException to be thrown');
          } catch (e) {
            expect(e, isA<BusinessException>());
            final businessException = e as BusinessException;
            expect(businessException.message, equals(testMessage));
          }
        });
      });

      // 1.3 系统异常场景测试
      group('1.3 系统异常场景测试', () {
        test('应该抛出SystemException当FormatException发生时', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(const FormatException('Invalid JSON format'));

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<SystemException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该抛出SystemException当StateError发生时', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(StateError('Invalid state'));

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<SystemException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该抛出SystemException当ArgumentError发生时', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(ArgumentError('Invalid argument'));

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<SystemException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该抛出SystemException当一般Exception发生时', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(Exception('Unexpected error'));

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<SystemException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该抛出SystemException当TypeError发生时', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(TypeError());

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<SystemException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该使用日语错误消息', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(Exception('Test error'));

          // Act & Assert
          try {
            await repository.logout();
            fail('Expected SystemException to be thrown');
          } catch (e) {
            expect(e, isA<SystemException>());
            final systemException = e as SystemException;
            expect(systemException.message, equals('システムエラーが発生しました。管理者にご連絡ください。'));
          }
        });
      });

      // 1.4 边界和特殊场景测试
      group('1.4 边界和特殊场景测试', () {
        test('应该处理重复调用logout', () async {
          // Arrange
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

          // Act - 多次调用
          await repository.logout();
          await repository.logout();
          await repository.logout();

          // Assert
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(3);
        });

        test('应该支持并发logout调用', () async {
          // Arrange
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

          // Act - 并发调用
          final futures = List.generate(5, (_) => repository.logout());
          await Future.wait(futures);

          // Assert
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(5);
        });

        test('应该处理快速连续的成功失败调用', () async {
          // Arrange - 第一次成功，第二次失败
          var callCount = 0;
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async {
            callCount++;
            if (callCount == 1) {
              return Response(
                data: {},
                statusCode: 200,
                requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              );
            } else {
              throw DioException(
                requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
                type: DioExceptionType.connectionTimeout,
              );
            }
          });

          // Act & Assert - 第一次成功
          await repository.logout();

          // Act & Assert - 第二次失败
          expect(() => repository.logout(), throwsA(isA<BusinessException>()));

          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(2);
        });

        test('应该确保executeRepositoryTask正确包装logout逻辑', () async {
          // Arrange
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

          // Act
          await repository.logout();

          // Assert - 验证正确的URL被调用，确认executeRepositoryTask包装正确工作
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
          verifyNoMoreInteractions(mockDioUtil);
        });

        test('应该确保方法签名与接口匹配', () async {
          // Arrange
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

          // Act
          final result = repository.logout();

          // Assert - 确保返回Future<void>
          expect(result, isA<Future<void>>());

          // 等待完成
          await result;

          // 验证没有返回值
          expect(result, completion(isNull));
        });

        test('应该正确处理BusinessException重新抛出', () async {
          // Arrange
          final originalBusinessException = BusinessException('Original business error');
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(originalBusinessException);

          // Act & Assert
          try {
            await repository.logout();
            fail('Expected BusinessException to be thrown');
          } catch (e) {
            expect(e, isA<BusinessException>());
            expect(identical(e, originalBusinessException), isTrue);
          }
        });
      });
    });

    // Phase 2: 边界条件和集成测试
    group('Phase 2: 边界条件和集成测试', () {
      // 2.1 高级并发和性能测试
      group('2.1 高级并发和性能测试', () {
        test('应该处理大量并发logout调用', () async {
          // Arrange - 模拟高并发场景
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async {
            // 模拟网络延迟
            await Future.delayed(const Duration(milliseconds: 10));
            return successResponse;
          });

          // Act - 100个并发调用
          final stopwatch = Stopwatch()..start();
          final futures = List.generate(100, (_) => repository.logout());
          await Future.wait(futures);
          stopwatch.stop();

          // Assert
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(100);
          // 验证并发性能（应该在合理时间内完成）
          expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 5秒内完成
        });

        test('应该处理混合成功失败的大量并发调用', () async {
          // Arrange - 50%成功，50%失败
          var callCount = 0;
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async {
            callCount++;
            if (callCount % 2 == 0) {
              // 偶数次调用失败
              throw DioException(
                requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
                type: DioExceptionType.connectionTimeout,
                message: 'Timeout $callCount',
              );
            } else {
              // 奇数次调用成功
              return Response(
                data: {},
                statusCode: 200,
                requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              );
            }
          });

          // Act - 并发调用并统计结果
          final futures = List.generate(
            50,
            (_) => repository.logout().then((_) => 'success').catchError((e) => 'error'),
          );
          final results = await Future.wait(futures);

          // Assert
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(50);

          // 验证结果分布
          final successes = results.where((r) => r == 'success').length;
          final failures = results.where((r) => r == 'error').length;

          expect(successes, equals(25)); // 25个成功
          expect(failures, equals(25)); // 25个失败
        });

        test('应该处理快速连续调用的内存使用', () async {
          // Arrange
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

          // Act - 快速连续调用1000次
          for (int i = 0; i < 1000; i++) {
            await repository.logout();
          }

          // Assert
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1000);
          // 验证没有内存泄漏（如果有内存泄漏，这个测试会变得很慢或失败）
        });

        test('应该处理长时间运行的并发场景', () async {
          // Arrange
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async {
            // 模拟较长的网络延迟
            await Future.delayed(const Duration(milliseconds: 100));
            return successResponse;
          });

          // Act - 分批并发调用
          final batchSize = 10;
          final numberOfBatches = 5;
          var totalCalls = 0;

          for (int batch = 0; batch < numberOfBatches; batch++) {
            final futures = List.generate(batchSize, (_) => repository.logout());
            await Future.wait(futures);
            totalCalls += batchSize;
          }

          // Assert
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(totalCalls);
          expect(totalCalls, equals(50));
        });
      });

      // 2.2 RepositoryErrorHandler深度集成测试
      group('2.2 RepositoryErrorHandler深度集成测试', () {
        test('应该正确传递技术错误消息给executeRepositoryTask', () async {
          // Arrange
          const technicalMessage = 'Failed to logout';
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(Exception('Low-level technical error'));

          // Act & Assert
          try {
            await repository.logout();
            fail('Expected SystemException to be thrown');
          } catch (e) {
            expect(e, isA<SystemException>());
            final systemException = e as SystemException;
            expect(systemException.technicalMessage, equals(technicalMessage));
          }
        });

        test('应该验证executeRepositoryTask的异常转换链', () async {
          // Arrange & Act & Assert - 测试DioException转换为BusinessException
          reset(mockDioUtil);
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: '/test'),
              type: DioExceptionType.connectionTimeout,
              message: 'Timeout',
            ),
          );
          expect(() => repository.logout(), throwsA(isA<BusinessException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);

          // 测试FormatException转换为SystemException
          reset(mockDioUtil);
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(const FormatException('Format error'));
          expect(() => repository.logout(), throwsA(isA<SystemException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);

          // 测试StateError转换为SystemException
          reset(mockDioUtil);
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(StateError('State error'));
          expect(() => repository.logout(), throwsA(isA<SystemException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);

          // 测试ArgumentError转换为SystemException
          reset(mockDioUtil);
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(ArgumentError('Argument error'));
          expect(() => repository.logout(), throwsA(isA<SystemException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);

          // 测试一般Exception转换为SystemException
          reset(mockDioUtil);
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(Exception('General error'));
          expect(() => repository.logout(), throwsA(isA<SystemException>()));
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该验证错误日志记录机制', () async {
          // Arrange
          final testException = Exception('Test logging error');
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(testException);

          // Act & Assert - 验证异常被正确抛出
          expect(() => repository.logout(), throwsA(isA<SystemException>()));

          // 注意：日志记录在实际代码中通过LogUtil.e()执行，
          // 这里我们验证异常处理流程正确，日志功能由LogUtil负责
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该验证BusinessException透传机制', () async {
          // Arrange
          final originalBusinessException = BusinessException('Original business error message');
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(originalBusinessException);

          // Act & Assert
          try {
            await repository.logout();
            fail('Expected BusinessException to be thrown');
          } catch (e) {
            // 验证是同一个异常实例被重新抛出
            expect(identical(e, originalBusinessException), isTrue);
            expect(e, isA<BusinessException>());
            expect((e as BusinessException).message, equals('Original business error message'));
          }
        });

        test('应该验证DioException错误消息提取优先级', () async {
          // Arrange - 测试不同的DioException消息提取场景

          // 场景1：有message的情况
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              type: DioExceptionType.connectionTimeout,
              message: 'Primary message',
              response: Response(
                data: {'msg': 'Secondary message'},
                requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              ),
            ),
          );

          try {
            await repository.logout();
            fail('Expected BusinessException to be thrown');
          } catch (e) {
            expect(e, isA<BusinessException>());
            expect((e as BusinessException).message, equals('Primary message'));
          }

          // 重置mock
          reset(mockDioUtil);

          // 场景2：没有message，但有response.data.msg的情况
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              type: DioExceptionType.badResponse,
              response: Response(
                data: {'msg': 'Response error message'},
                requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              ),
            ),
          );

          try {
            await repository.logout();
            fail('Expected BusinessException to be thrown');
          } catch (e) {
            expect(e, isA<BusinessException>());
            expect((e as BusinessException).message, equals('Response error message'));
          }

          // 重置mock
          reset(mockDioUtil);

          // 场景3：都没有的情况，使用默认消息
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              type: DioExceptionType.connectionError,
            ),
          );

          try {
            await repository.logout();
            fail('Expected BusinessException to be thrown');
          } catch (e) {
            expect(e, isA<BusinessException>());
            expect((e as BusinessException).message, contains('Network error occurred'));
            expect((e as BusinessException).message, contains('DioExceptionType.connectionError'));
          }
        });
      });

      // 2.3 Mock验证和测试隔离
      group('2.3 Mock验证和测试隔离', () {
        test('应该确保每个测试的Mock状态独立', () async {
          // Arrange - 这个测试验证tearDown()正确重置了mock状态
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

          // Act
          await repository.logout();

          // Assert - 验证调用次数
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);

          // 模拟其他测试运行后的状态验证
          // 在真实环境中，tearDown()会重置mock状态
        });

        test('应该验证Mock调用参数的精确性', () async {
          // Arrange
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

          // Act
          await repository.logout();

          // Assert - 验证精确的调用参数
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);

          // 验证没有其他意外的方法调用
          verifyNoMoreInteractions(mockDioUtil);
        });

        test('应该验证Mock异常抛出的精确性', () async {
          // Arrange
          final testException = DioException(
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
            type: DioExceptionType.connectionTimeout,
            message: 'Specific test timeout',
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(testException);

          // Act & Assert
          try {
            await repository.logout();
            fail('Expected BusinessException to be thrown');
          } catch (e) {
            expect(e, isA<BusinessException>());
            expect((e as BusinessException).message, equals('Specific test timeout'));
          }

          // 验证Mock被正确调用
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });

        test('应该验证Mock调用顺序在复杂场景中的正确性', () async {
          // Arrange - 设置复杂的调用序列
          var callCount = 0;
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async {
            callCount++;
            switch (callCount) {
              case 1:
                return Response(
                  data: {},
                  statusCode: 200,
                  requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
                );
              case 2:
                throw DioException(
                  requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
                  type: DioExceptionType.connectionTimeout,
                );
              case 3:
                return Response(
                  data: {},
                  statusCode: 200,
                  requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
                );
              default:
                return Response(
                  data: {},
                  statusCode: 200,
                  requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
                );
            }
          });

          // Act & Assert - 第一次调用成功
          await repository.logout();

          // 第二次调用失败
          expect(() => repository.logout(), throwsA(isA<BusinessException>()));

          // 第三次调用成功
          await repository.logout();

          // Assert - 验证调用次数和顺序
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(3);
        });

        test('应该验证Mock状态在异常情况下的清理', () async {
          // Arrange
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenThrow(Exception('Cleanup test exception'));

          // Act & Assert
          expect(() => repository.logout(), throwsA(isA<SystemException>()));

          // 验证即使在异常情况下，Mock调用也被正确记录
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);

          // 重置并验证状态已清理
          reset(mockDioUtil);

          // 新的mock设置应该生效
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer(
            (_) async => Response(
              data: {},
              statusCode: 200,
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
            ),
          );

          await repository.logout();
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
        });
      });

      // 2.4 生命周期和资源管理测试
      group('2.4 生命周期和资源管理测试', () {
        test('应该验证Repository实例之间的独立性', () async {
          // Arrange - 创建多个Repository实例
          final mockDioUtil1 = MockDioUtil();
          final mockDioUtil2 = MockDioUtil();
          final repository1 = LogoutRepositoryImpl(mockDioUtil1);
          final repository2 = LogoutRepositoryImpl(mockDioUtil2);

          final response1 = Response(
            data: {'instance': 1},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );
          final response2 = Response(
            data: {'instance': 2},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil1.post(GlobalVariable.secureLogout)).thenAnswer((_) async => response1);
          when(mockDioUtil2.post(GlobalVariable.secureLogout)).thenAnswer((_) async => response2);

          // Act
          await repository1.logout();
          await repository2.logout();

          // Assert - 验证每个实例使用自己的DioUtil
          verify(mockDioUtil1.post(GlobalVariable.secureLogout)).called(1);
          verify(mockDioUtil2.post(GlobalVariable.secureLogout)).called(1);

          // 验证实例间没有交叉调用
          verifyNoMoreInteractions(mockDioUtil1);
          verifyNoMoreInteractions(mockDioUtil2);
        });

        test('应该验证Repository在长期使用中的稳定性', () async {
          // Arrange
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

          // Act - 模拟长期使用场景
          const numberOfCycles = 100;
          for (int cycle = 0; cycle < numberOfCycles; cycle++) {
            // 每个周期执行多次logout
            for (int call = 0; call < 5; call++) {
              await repository.logout();
            }

            // 模拟短暂停顿
            await Future.delayed(const Duration(milliseconds: 1));
          }

          // Assert
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(numberOfCycles * 5);
        });

        test('应该验证Repository的内存使用稳定性', () async {
          // Arrange
          final successResponse = Response(
            data: {},
            statusCode: 200,
            requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
          );

          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

          // Act - 创建和销毁多个Repository实例
          const numberOfInstances = 50;
          final repositories = <LogoutRepositoryImpl>[];

          for (int i = 0; i < numberOfInstances; i++) {
            final tempMockDioUtil = MockDioUtil();
            when(tempMockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => successResponse);

            final tempRepository = LogoutRepositoryImpl(tempMockDioUtil);
            repositories.add(tempRepository);

            // 每个实例执行一次logout
            await tempRepository.logout();
          }

          // Assert - 验证所有实例都正常工作
          expect(repositories.length, equals(numberOfInstances));

          // 清理引用，让GC有机会回收
          repositories.clear();
        });

        test('应该验证异常恢复后的Repository状态', () async {
          // Arrange - 设置异常后恢复的场景
          var callCount = 0;
          when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async {
            callCount++;
            if (callCount <= 3) {
              // 前3次调用抛出异常
              throw DioException(
                requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
                type: DioExceptionType.connectionTimeout,
                message: 'Recovery test timeout $callCount',
              );
            } else {
              // 第4次开始恢复正常
              return Response(
                data: {},
                statusCode: 200,
                requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
              );
            }
          });

          // Act & Assert - 前3次失败
          for (int i = 1; i <= 3; i++) {
            expect(() => repository.logout(), throwsA(isA<BusinessException>()));
          }

          // 第4次成功
          await repository.logout();

          // 后续调用继续成功
          await repository.logout();
          await repository.logout();

          // Assert
          verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(6);
        });

        test('应该验证Repository在应用生命周期中的行为一致性', () async {
          // Arrange - 模拟应用不同生命周期阶段
          final responses = {
            'startup': Response(
              data: {'phase': 'startup'},
              statusCode: 200,
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
            ),
            'active': Response(
              data: {'phase': 'active'},
              statusCode: 200,
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
            ),
            'background': Response(
              data: {'phase': 'background'},
              statusCode: 200,
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
            ),
            'shutdown': Response(
              data: {'phase': 'shutdown'},
              statusCode: 200,
              requestOptions: RequestOptions(path: GlobalVariable.secureLogout),
            ),
          };

          // Act & Assert - 在不同生命周期阶段都能正常工作
          for (final phase in responses.keys) {
            reset(mockDioUtil);
            when(mockDioUtil.post(GlobalVariable.secureLogout)).thenAnswer((_) async => responses[phase]!);

            // 执行logout
            await repository.logout();

            // 验证调用
            verify(mockDioUtil.post(GlobalVariable.secureLogout)).called(1);
          }
        });
      });
    });
  });
}

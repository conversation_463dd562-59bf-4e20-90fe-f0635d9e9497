// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/mypage/presentation/controllers/biometrics_switch_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;
import 'dart:ui' as _i7;

import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i6;
import 'package:asset_force_mobile_v2/core/utils/biometrics_switch_util.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart'
    as _i8;
import 'package:flutter/services.dart' as _i9;
import 'package:local_auth/local_auth.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [BiometricsSwitchUtil].
///
/// See the documentation for Mockito's code generation for more information.
class MockBiometricsSwitchUtil extends _i1.Mock
    implements _i2.BiometricsSwitchUtil {
  MockBiometricsSwitchUtil() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<String?> isAccountWorkingProperly({required String? password}) =>
      (super.noSuchMethod(
            Invocation.method(#isAccountWorkingProperly, [], {
              #password: password,
            }),
            returnValue: _i3.Future<String?>.value(),
          )
          as _i3.Future<String?>);

  @override
  _i3.Future<void> closeOrOpenBiometrics({required bool? isOpen}) =>
      (super.noSuchMethod(
            Invocation.method(#closeOrOpenBiometrics, [], {#isOpen: isOpen}),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  bool getUserBiometricsOpenSwitch() =>
      (super.noSuchMethod(
            Invocation.method(#getUserBiometricsOpenSwitch, []),
            returnValue: false,
          )
          as bool);

  @override
  _i3.Future<bool> isBiometricsAvailable() =>
      (super.noSuchMethod(
            Invocation.method(#isBiometricsAvailable, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<List<_i4.BiometricType>> getAvailableBiometrics() =>
      (super.noSuchMethod(
            Invocation.method(#getAvailableBiometrics, []),
            returnValue: _i3.Future<List<_i4.BiometricType>>.value(
              <_i4.BiometricType>[],
            ),
          )
          as _i3.Future<List<_i4.BiometricType>>);

  @override
  _i3.Future<void> authenticateBiometricsSetting() =>
      (super.noSuchMethod(
            Invocation.method(#authenticateBiometricsSetting, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> setBiometricSecretPassword({
    required String? userName,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setBiometricSecretPassword, [], {
              #userName: userName,
              #password: password,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<String> getBiometricSecretPassword() =>
      (super.noSuchMethod(
            Invocation.method(#getBiometricSecretPassword, []),
            returnValue: _i3.Future<String>.value(
              _i5.dummyValue<String>(
                this,
                Invocation.method(#getBiometricSecretPassword, []),
              ),
            ),
          )
          as _i3.Future<String>);
}

/// A class which mocks [DialogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDialogService extends _i1.Mock implements _i6.DialogService {
  MockDialogService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> show({
    String? title,
    required String? content,
    String? confirmText,
    String? cancelText,
    _i7.VoidCallback? onConfirm,
    _i7.VoidCallback? onCancel,
    bool? barrierDismissible,
    _i8.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#show, [], {
              #title: title,
              #content: content,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> showInput({
    String? title,
    required String? contentText,
    String? hintText,
    String? initialValue,
    String? confirmText,
    String? cancelText,
    required dynamic Function(String)? onConfirm,
    _i7.VoidCallback? onCancel,
    String? Function(String?)? inputValidator,
    int? maxLength,
    _i9.TextInputType? keyboardType,
    bool? obscureText,
    bool? barrierDismissible,
    _i8.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showInput, [], {
              #title: title,
              #contentText: contentText,
              #hintText: hintText,
              #initialValue: initialValue,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #inputValidator: inputValidator,
              #maxLength: maxLength,
              #keyboardType: keyboardType,
              #obscureText: obscureText,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  void showToast(String? message) => super.noSuchMethod(
    Invocation.method(#showToast, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void showCustomToast(String? message) => super.noSuchMethod(
    Invocation.method(#showCustomToast, [message]),
    returnValueForMissingStub: null,
  );
}

import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:crypto/crypto.dart';

import 'package:asset_force_mobile_v2/core/constant/method_channel_constant.dart';
import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart';
import 'package:asset_force_mobile_v2/core/platform/method_channel.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/biometrics_switch_util.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/domain/usecase/logout_usercase.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/domain/usecase/mypage_usercase.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/controllers/biometrics_switch_controller.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/controllers/mypage_controller.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/models/mypage_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:matcher/matcher.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'mypage_controller_test.mocks.dart';

// 测试专用的 MyPageController，覆盖 handleException 方法避免 UI 调用
class TestMyPageController extends MyPageController {
  Exception? lastException;
  bool handleExceptionCalled = false;

  TestMyPageController({
    required super.meLoadDataCase,
    required super.logoutUseCase,
    required super.biometricsSwitchUtil,
    required super.storageUtils,
    required super.biometricsSwitchController,
    required super.dialogService,
    required super.navigationService,
    required super.envHelper,
    super.platformChannel,
  });

  @override
  Future<void> handleException(dynamic exception, [StackTrace? stackTrace, ErrorHandlingMode? mode]) async {
    handleExceptionCalled = true;
    lastException = exception as Exception?;
    // 不调用父类的 handleException，避免 CommonDialog 相关的问题
  }

  // 暴露私有方法用于测试
  Future<void> testLogout() async {
    try {
      await logoutUseCase.call(const NoParams());
      Future.delayed(const Duration(seconds: 1), () {
        storageUtils.removeValue(StorageKeys.token);
      });
      await navigationService.navigateOffAll(AutoRoutes.login);
    } catch (e, stackTrace) {
      await handleException(e, stackTrace);
      await navigationService.navigateOffAll(AutoRoutes.login);
    }
  }

  String testGetParamTicket() {
    final now = DateTime.now();
    final bytesToHash = utf8.encode('${now.year}${now.month}${now.day}');
    return sha1.convert(bytesToHash).toString();
  }

  // 将异步 void 方法转换为 Future 方法以便测试
  Future<void> testBiometricsChange(bool value) async {
    try {
      if (value) {
        await biometricsSwitchController.openBiometrics();
      } else {
        await biometricsSwitchController.disableBiometrics();
      }
      // 重新加载数据以获取最新状态
      await meLoadDataCase
          .call(const NoParams())
          .then((result) {
            user.value = result;
          })
          .catchError((error, stackTrace) {
            handleException(error, stackTrace);
          })
          .whenComplete(() {
            isLoading.value = false;
            user.refresh();
          });
    } catch (e, stackTrace) {
      await handleException(e, stackTrace);
    }
  }

  // 测试专用的 URL 构建方法，不触发实际的 launchUrl
  String testBuildTutorialUrl() {
    final guideBaseUrl = envHelper.getGuideBaseUrl();
    final ticket = testGetParamTicket();
    return '$guideBaseUrl/support/tutorial/?ticket=$ticket';
  }

  String testBuildStarterGuideUrl() {
    final guideBaseUrl = envHelper.getGuideBaseUrl();
    final ticket = testGetParamTicket();
    return '$guideBaseUrl/support/starterguide/?ticket=$ticket';
  }

  String? testGetSupportFaqUrl() {
    return envHelper.getSupportFaq();
  }
}

// 生成 Mock 类
@GenerateMocks([
  MyPageLoadDataCase,
  LogoutUseCase,
  BiometricsSwitchUtil,
  IStorageUtils,
  BiometricsSwitchController,
  DialogService,
  NavigationService,
  IEnvHelper,
  PlatformChannel,
])
void main() {
  // ==========================================
  // MyPageController 单元测试
  //
  // 测试架构：
  // - Phase 0: 测试基础设施验证
  // - Phase 1: 基础功能测试
  // - Phase 2: 核心业务逻辑测试
  // - Phase 3: 导航和外部服务测试
  // - Phase 4: 异常处理和边界情况
  // - Phase 5: 状态一致性和响应式测试
  // ==========================================

  group('🧪 MyPageController 单元测试', () {
    // Mock 依赖声明
    late MockMyPageLoadDataCase mockMyPageLoadDataCase;
    late MockLogoutUseCase mockLogoutUseCase;
    late MockBiometricsSwitchUtil mockBiometricsSwitchUtil;
    late MockIStorageUtils mockStorageUtils;
    late MockBiometricsSwitchController mockBiometricsSwitchController;
    late MockDialogService mockDialogService;
    late MockNavigationService mockNavigationService;
    late MockIEnvHelper mockEnvHelper;
    late MockPlatformChannel mockPlatformChannel;

    // 系统被测试对象
    late MyPageController controller;

    // 测试数据
    late MyPageUIModel testUserModel;

    setUpAll(() {
      // 初始化测试环境
      TestWidgetsFlutterBinding.ensureInitialized();

      // 初始化 LogUtil 避免测试失败
      LogUtil.initialize();
    });

    setUp(() {
      // 重置 GetX 状态
      Get.reset();
      Get.testMode = true;
      Get.config(enableLog: false);

      // 创建 Mock 对象
      mockMyPageLoadDataCase = MockMyPageLoadDataCase();
      mockLogoutUseCase = MockLogoutUseCase();
      mockBiometricsSwitchUtil = MockBiometricsSwitchUtil();
      mockStorageUtils = MockIStorageUtils();
      mockBiometricsSwitchController = MockBiometricsSwitchController();
      mockDialogService = MockDialogService();
      mockNavigationService = MockNavigationService();
      mockEnvHelper = MockIEnvHelper();
      mockPlatformChannel = MockPlatformChannel();

      // 创建测试数据
      testUserModel = MyPageUIModel(
        userName: 'testUser',
        firstName: 'Test',
        lastName: 'User',
        tenantName: 'Test Tenant',
        lastLoginTime: '2024-01-01 10:00:00',
        contractPeriodEndDate: '2025-01-01',
        plan: 'Standard',
      );
    });

    tearDown(() {
      // 清理资源
      reset(mockMyPageLoadDataCase);
      reset(mockLogoutUseCase);
      reset(mockBiometricsSwitchUtil);
      reset(mockStorageUtils);
      reset(mockBiometricsSwitchController);
      reset(mockDialogService);
      reset(mockNavigationService);
      reset(mockEnvHelper);
      reset(mockPlatformChannel);
      clearInteractions(mockMyPageLoadDataCase);
      clearInteractions(mockLogoutUseCase);
      clearInteractions(mockBiometricsSwitchUtil);
      clearInteractions(mockStorageUtils);
      clearInteractions(mockBiometricsSwitchController);
      clearInteractions(mockDialogService);
      clearInteractions(mockNavigationService);
      clearInteractions(mockEnvHelper);
      clearInteractions(mockPlatformChannel);
      Get.reset();
    });

    // Helper function to create controller instance
    MyPageController createController({PlatformChannel? platformChannel, bool useTestController = false}) {
      if (useTestController) {
        return TestMyPageController(
          meLoadDataCase: mockMyPageLoadDataCase,
          logoutUseCase: mockLogoutUseCase,
          biometricsSwitchUtil: mockBiometricsSwitchUtil,
          storageUtils: mockStorageUtils,
          biometricsSwitchController: mockBiometricsSwitchController,
          dialogService: mockDialogService,
          navigationService: mockNavigationService,
          envHelper: mockEnvHelper,
          platformChannel: platformChannel ?? mockPlatformChannel,
        );
      }
      return MyPageController(
        meLoadDataCase: mockMyPageLoadDataCase,
        logoutUseCase: mockLogoutUseCase,
        biometricsSwitchUtil: mockBiometricsSwitchUtil,
        storageUtils: mockStorageUtils,
        biometricsSwitchController: mockBiometricsSwitchController,
        dialogService: mockDialogService,
        navigationService: mockNavigationService,
        envHelper: mockEnvHelper,
        platformChannel: platformChannel ?? mockPlatformChannel,
      );
    }

    // Helper function to wait for data loading to complete
    Future<void> waitForDataLoading(MyPageController controller) async {
      // 等待 loading 状态变为 false，最多等待 5 秒
      var attempts = 0;
      const maxAttempts = 50; // 5 秒 (50 * 100ms)

      while (controller.isLoading.value && attempts < maxAttempts) {
        await Future.delayed(const Duration(milliseconds: 100));
        attempts++;
      }
    }

    // Helper function to trigger data loading and wait for completion
    Future<void> triggerAndWaitForDataLoading(MyPageController controller) async {
      controller.onReady();
      await waitForDataLoading(controller);
    }

    // ==========================================
    // Phase 0: 测试基础设施验证
    // ==========================================
    group('🏗️ Phase 0: 测试基础设施验证', () {
      group('0.1 Mock 依赖创建验证', () {
        test('应该正确创建所有 Mock 依赖对象', () {
          // Assert - 验证所有 Mock 对象都已创建
          expect(mockMyPageLoadDataCase, isNotNull);
          expect(mockMyPageLoadDataCase, isA<MockMyPageLoadDataCase>());
          expect(mockMyPageLoadDataCase, isA<MyPageLoadDataCase>());

          expect(mockLogoutUseCase, isNotNull);
          expect(mockLogoutUseCase, isA<MockLogoutUseCase>());
          expect(mockLogoutUseCase, isA<LogoutUseCase>());

          expect(mockBiometricsSwitchUtil, isNotNull);
          expect(mockBiometricsSwitchUtil, isA<MockBiometricsSwitchUtil>());
          expect(mockBiometricsSwitchUtil, isA<BiometricsSwitchUtil>());

          expect(mockStorageUtils, isNotNull);
          expect(mockStorageUtils, isA<MockIStorageUtils>());
          expect(mockStorageUtils, isA<IStorageUtils>());

          expect(mockBiometricsSwitchController, isNotNull);
          expect(mockBiometricsSwitchController, isA<MockBiometricsSwitchController>());
          expect(mockBiometricsSwitchController, isA<BiometricsSwitchController>());

          expect(mockDialogService, isNotNull);
          expect(mockDialogService, isA<MockDialogService>());
          expect(mockDialogService, isA<DialogService>());

          expect(mockNavigationService, isNotNull);
          expect(mockNavigationService, isA<MockNavigationService>());
          expect(mockNavigationService, isA<NavigationService>());

          expect(mockEnvHelper, isNotNull);
          expect(mockEnvHelper, isA<MockIEnvHelper>());
          expect(mockEnvHelper, isA<IEnvHelper>());

          expect(mockPlatformChannel, isNotNull);
          expect(mockPlatformChannel, isA<MockPlatformChannel>());
          expect(mockPlatformChannel, isA<PlatformChannel>());
        });

        test('Mock 对象应该是独立实例，不会相互干扰', () {
          // Arrange & Act
          final anotherMockLoadDataCase = MockMyPageLoadDataCase();
          final anotherMockDialogService = MockDialogService();

          // Assert - 验证不同的 Mock 实例是独立的
          expect(mockMyPageLoadDataCase, isNot(equals(anotherMockLoadDataCase)));
          expect(mockDialogService, isNot(equals(anotherMockDialogService)));
          // 使用 identical 来检查是否为同一个对象实例
          expect(identical(mockMyPageLoadDataCase, anotherMockLoadDataCase), isFalse);
        });

        test('应该能够对 Mock 对象进行方法调用验证', () {
          // Arrange
          when(mockEnvHelper.getGuideBaseUrl()).thenReturn('https://test.example.com');
          when(mockEnvHelper.getSupportFaq()).thenReturn('https://faq.example.com');

          // Act
          final guideUrl = mockEnvHelper.getGuideBaseUrl();
          final faqUrl = mockEnvHelper.getSupportFaq();

          // Assert
          expect(guideUrl, equals('https://test.example.com'));
          expect(faqUrl, equals('https://faq.example.com'));
          verify(mockEnvHelper.getGuideBaseUrl()).called(1);
          verify(mockEnvHelper.getSupportFaq()).called(1);
        });
      });

      group('0.2 Controller 实例化测试', () {
        test('应该能够成功创建 Controller 实例', () {
          // Act
          controller = createController();

          // Assert
          expect(controller, isNotNull);
          expect(controller, isA<MyPageController>());
        });

        test('应该正确注入所有必需的依赖', () {
          // Act
          controller = createController();

          // Assert
          expect(controller.meLoadDataCase, equals(mockMyPageLoadDataCase));
          expect(controller.logoutUseCase, equals(mockLogoutUseCase));
          expect(controller.biometricsSwitchUtil, equals(mockBiometricsSwitchUtil));
          expect(controller.storageUtils, equals(mockStorageUtils));
          expect(controller.biometricsSwitchController, equals(mockBiometricsSwitchController));
          expect(controller.dialogService, equals(mockDialogService));
          expect(controller.navigationService, equals(mockNavigationService));
          expect(controller.envHelper, equals(mockEnvHelper));
        });

        test('PlatformChannel 应该使用传入的实例而不是默认的 Get.find()', () {
          // Act
          controller = createController(platformChannel: mockPlatformChannel);

          // Assert
          expect(controller.platformChannel, equals(mockPlatformChannel));
        });

        test('不传入 PlatformChannel 时应该能正常创建（使用默认 Get.find()）', () {
          // Arrange - 注册一个 PlatformChannel 到 GetX
          final defaultPlatformChannel = MockPlatformChannel();
          Get.put<PlatformChannel>(defaultPlatformChannel);

          // Act
          controller = createController(); // 不传入 platformChannel，但默认会使用 mockPlatformChannel

          // Assert
          expect(controller, isNotNull);
          // 因为我们的 createController 默认使用 mockPlatformChannel，所以应该是 mockPlatformChannel
          expect(controller.platformChannel, equals(mockPlatformChannel));
        });

        test('应该正确初始化用户响应式状态', () {
          // Act
          controller = createController();

          // Assert
          expect(controller.user, isNotNull);
          expect(controller.user.value, isA<MyPageUIModel>());
          expect(controller.user.value.userName, equals(''));
          expect(controller.user.value.firstName, equals(''));
          expect(controller.user.value.lastName, equals(''));
          expect(controller.user.value.tenantName, equals(''));
          expect(controller.user.value.lastLoginTime, equals(''));
          expect(controller.user.value.contractPeriodEndDate, equals(''));
          expect(controller.user.value.plan, equals(''));
        });

        test('应该正确初始化加载状态', () {
          // Act
          controller = createController();

          // Assert
          expect(controller.isLoading, isNotNull);
          expect(controller.isLoading.value, isFalse);
        });
      });

      group('0.3 GetX 测试环境验证', () {
        test('GetX 测试模式应该正确配置', () {
          // Assert
          expect(Get.testMode, isTrue);
        });

        test('应该能够重置 GetX 状态', () {
          // Arrange
          Get.put<String>('test_value');
          expect(Get.isRegistered<String>(), isTrue);

          // Act
          Get.reset();

          // Assert
          expect(Get.isRegistered<String>(), isFalse);
        });

        test('Controller 生命周期方法应该能正常调用', () {
          // Arrange
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          controller = createController();

          // Act & Assert - 应该不抛出异常
          expect(() => controller.onInit(), returnsNormally);
          expect(() => controller.onReady(), returnsNormally);
          expect(() => controller.onClose(), returnsNormally);
        });
      });

      group('0.4 基础响应式状态测试', () {
        test('user 响应式变量应该能正确更新', () {
          // Arrange
          controller = createController();
          var updateCount = 0;

          // 监听响应式变化
          controller.user.listen((_) => updateCount++);

          // Act
          controller.user.value = testUserModel;

          // Assert
          expect(controller.user.value, equals(testUserModel));
          expect(controller.user.value.userName, equals('testUser'));
          expect(updateCount, equals(1));
        });

        test('isLoading 响应式变量应该能正确更新', () {
          // Arrange
          controller = createController();
          var updateCount = 0;

          // 监听响应式变化
          controller.isLoading.listen((_) => updateCount++);

          // Act
          controller.isLoading.value = true;

          // Assert
          expect(controller.isLoading.value, isTrue);
          expect(updateCount, equals(1));

          // Act again
          controller.isLoading.value = false;

          // Assert
          expect(controller.isLoading.value, isFalse);
          expect(updateCount, equals(2));
        });

        test('多个响应式变量应该能独立工作', () {
          // Arrange
          controller = createController();
          var userUpdateCount = 0;
          var loadingUpdateCount = 0;

          controller.user.listen((_) => userUpdateCount++);
          controller.isLoading.listen((_) => loadingUpdateCount++);

          // Act
          controller.user.value = testUserModel;
          controller.isLoading.value = true;
          final updatedUserModel = MyPageUIModel(
            userName: 'updatedUser',
            firstName: testUserModel.firstName,
            lastName: testUserModel.lastName,
            tenantName: testUserModel.tenantName,
            lastLoginTime: testUserModel.lastLoginTime,
            contractPeriodEndDate: testUserModel.contractPeriodEndDate,
            plan: testUserModel.plan,
          );
          controller.user.value = updatedUserModel;

          // Assert
          expect(userUpdateCount, equals(2)); // 更新了两次
          expect(loadingUpdateCount, equals(1)); // 只更新了一次
          expect(controller.user.value.userName, equals('updatedUser'));
          expect(controller.isLoading.value, isTrue);
        });

        test('响应式变量的初始状态应该正确', () {
          // Act
          controller = createController();

          // Assert
          expect(controller.user.value.userName, isEmpty);
          expect(controller.user.value.firstName, isEmpty);
          expect(controller.user.value.lastName, isEmpty);
          expect(controller.isLoading.value, isFalse);
        });
      });

      group('0.5 测试隔离性验证', () {
        test('不同测试间的 Mock 状态应该是隔离的', () {
          // Arrange
          when(mockEnvHelper.getGuideBaseUrl()).thenReturn('test1');

          // Act
          final result1 = mockEnvHelper.getGuideBaseUrl();

          // Reset for next "test"
          reset(mockEnvHelper);
          when(mockEnvHelper.getGuideBaseUrl()).thenReturn('test2');

          final result2 = mockEnvHelper.getGuideBaseUrl();

          // Assert
          expect(result1, equals('test1'));
          expect(result2, equals('test2'));
        });

        test('Controller 实例应该在每次创建时都是新的', () {
          // Act
          final controller1 = createController();
          final controller2 = createController();

          // Assert
          expect(controller1, isNot(equals(controller2)));
          // 使用 identical 来检查是否为同一个对象实例
          expect(identical(controller1, controller2), isFalse);
        });

        test('响应式状态应该在新实例中重置', () {
          // Arrange
          final controller1 = createController();
          controller1.user.value = testUserModel;
          controller1.isLoading.value = true;

          // Act
          final controller2 = createController();

          // Assert
          expect(controller2.user.value.userName, isEmpty);
          expect(controller2.isLoading.value, isFalse);
          expect(controller1.user.value.userName, equals('testUser'));
          expect(controller1.isLoading.value, isTrue);
        });
      });
    });

    // ==========================================
    // Phase 1: 基础功能测试
    // ==========================================
    group('🔧 Phase 1: 基础功能测试', () {
      group('1.1 数据加载功能测试', () {
        test('应该成功加载用户数据', () async {
          // Arrange
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          controller = createController();

          // Act
          await triggerAndWaitForDataLoading(controller);

          // Assert
          expect(controller.user.value, equals(testUserModel));
          expect(controller.user.value.userName, equals('testUser'));
          expect(controller.user.value.firstName, equals('Test'));
          expect(controller.user.value.lastName, equals('User'));
          expect(controller.user.value.tenantName, equals('Test Tenant'));
          verify(mockMyPageLoadDataCase.call(any)).called(1);
        });

        test('数据加载成功时应该更新 user 响应式变量', () async {
          // Arrange
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          controller = createController();
          var userUpdateCount = 0;
          controller.user.listen((_) => userUpdateCount++);

          // Act
          await triggerAndWaitForDataLoading(controller);

          // Assert
          expect(controller.user.value, equals(testUserModel));
          expect(userUpdateCount, greaterThan(0)); // 至少更新了一次
        });

        test('应该传递正确的参数给 UseCase', () async {
          // Arrange
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          controller = createController();

          // Act
          await triggerAndWaitForDataLoading(controller);

          // Assert
          verify(mockMyPageLoadDataCase.call(argThat(isA<NoParams>()))).called(1);
        });
      });

      group('1.2 加载状态管理测试', () {
        test('数据加载过程中 isLoading 应该为 true', () async {
          // Arrange
          final completer = Completer<MyPageUIModel>();
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) => completer.future);
          controller = createController();

          var loadingStates = <bool>[];
          controller.isLoading.listen((state) => loadingStates.add(state));

          // Act - 开始加载但不完成
          controller.onReady();

          // 给一点时间让状态变化
          await Future.delayed(const Duration(milliseconds: 10));

          // Assert - 此时应该正在加载
          expect(controller.isLoading.value, isTrue);
          expect(loadingStates, contains(true));

          // Cleanup - 完成加载
          completer.complete(testUserModel);
          await waitForDataLoading(controller);
        });

        test('数据加载完成后 isLoading 应该为 false', () async {
          // Arrange
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          controller = createController();

          // Act
          await triggerAndWaitForDataLoading(controller);

          // Assert
          expect(controller.isLoading.value, isFalse);
        });

        test('数据加载失败后 isLoading 应该为 false', () async {
          // Arrange
          when(mockMyPageLoadDataCase.call(any)).thenThrow(Exception('Load failed'));
          controller = createController(useTestController: true);

          // Act
          await triggerAndWaitForDataLoading(controller);

          // Assert
          expect(controller.isLoading.value, isFalse);
        });

        test('isLoading 状态变化序列应该正确', () async {
          // Arrange
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async {
            await Future.delayed(const Duration(milliseconds: 50));
            return testUserModel;
          });
          controller = createController();

          var loadingStates = <bool>[];
          // 先记录初始状态
          loadingStates.add(controller.isLoading.value);
          controller.isLoading.listen((state) => loadingStates.add(state));

          // Act
          await triggerAndWaitForDataLoading(controller);

          // Assert - 状态应该是：false(初始) -> true(加载中) -> false(完成)
          expect(loadingStates, orderedEquals([false, true, false]));
        });
      });

      group('1.3 初始化流程测试', () {
        test('onReady 应该触发数据加载', () async {
          // Arrange
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          controller = createController();

          // Act
          await triggerAndWaitForDataLoading(controller);

          // Assert
          verify(mockMyPageLoadDataCase.call(any)).called(1);
          expect(controller.user.value, equals(testUserModel));
        });

        test('onInit 不应该直接触发数据加载', () {
          // Arrange
          controller = createController();

          // Act
          controller.onInit();

          // Assert
          verifyNever(mockMyPageLoadDataCase.call(any));
          expect(controller.user.value.userName, isEmpty); // 保持初始状态
        });

        test('多次调用 onReady 应该多次加载数据', () async {
          // Arrange
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          controller = createController();

          // Act
          await triggerAndWaitForDataLoading(controller);
          await triggerAndWaitForDataLoading(controller);

          // Assert
          verify(mockMyPageLoadDataCase.call(any)).called(2);
        });

        test('Controller 初始状态应该正确', () {
          // Act
          controller = createController();

          // Assert
          expect(controller.isLoading.value, isFalse);
          expect(controller.user.value.userName, isEmpty);
          expect(controller.user.value.firstName, isEmpty);
          expect(controller.user.value.lastName, isEmpty);
          expect(controller.user.value.tenantName, isEmpty);
        });
      });

      group('1.4 错误处理测试', () {
        test('数据加载失败时应该调用 handleException', () async {
          // Arrange
          final exception = Exception('Network error');
          when(mockMyPageLoadDataCase.call(any)).thenThrow(exception);
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          await triggerAndWaitForDataLoading(controller);

          // Assert
          verify(mockMyPageLoadDataCase.call(any)).called(1);
          expect(testController.handleExceptionCalled, isTrue);
          expect(testController.lastException?.toString(), contains('Network error'));
          expect(controller.user.value.userName, isEmpty);
        });

        test('数据加载失败时 user 数据应该保持不变', () async {
          // Arrange
          when(mockMyPageLoadDataCase.call(any)).thenThrow(Exception('Load failed'));
          controller = createController(useTestController: true);
          final originalUser = controller.user.value;

          // Act
          await triggerAndWaitForDataLoading(controller);

          // Assert
          expect(controller.user.value, equals(originalUser));
          expect(controller.user.value.userName, isEmpty);
        });

        test('数据加载异常后应该能够重新加载', () async {
          // Arrange
          controller = createController(useTestController: true);

          // 第一次失败
          when(mockMyPageLoadDataCase.call(any)).thenThrow(Exception('First failure'));
          await triggerAndWaitForDataLoading(controller);

          // 第二次成功
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);

          // Act
          await triggerAndWaitForDataLoading(controller);

          // Assert
          expect(controller.user.value, equals(testUserModel));
          expect(controller.isLoading.value, isFalse);
          verify(mockMyPageLoadDataCase.call(any)).called(2);
        });

        test('异常处理不应该影响 isLoading 状态重置', () async {
          // Arrange
          when(mockMyPageLoadDataCase.call(any)).thenThrow(Exception('Load failed'));
          controller = createController(useTestController: true);

          // Act
          await triggerAndWaitForDataLoading(controller);

          // Assert
          expect(controller.isLoading.value, isFalse);
        });

        test('异常处理后应该调用 user.refresh()', () async {
          // Arrange
          when(mockMyPageLoadDataCase.call(any)).thenThrow(Exception('Load failed'));
          controller = createController(useTestController: true);
          var refreshCallCount = 0;

          // 监听 user 变化来间接验证 refresh 调用
          controller.user.listen((_) => refreshCallCount++);

          // Act
          await triggerAndWaitForDataLoading(controller);

          // Assert
          // refresh() 会触发监听器，即使值没变
          expect(refreshCallCount, greaterThan(0));
        });
      });

      group('1.5 数据一致性测试', () {
        test('连续加载应该保持数据一致性', () async {
          // Arrange
          final userData1 = MyPageUIModel(
            userName: 'user1',
            firstName: 'First1',
            lastName: 'Last1',
            tenantName: 'Tenant1',
            lastLoginTime: '2024-01-01',
            contractPeriodEndDate: '2025-01-01',
            plan: 'Plan1',
          );
          final userData2 = MyPageUIModel(
            userName: 'user2',
            firstName: 'First2',
            lastName: 'Last2',
            tenantName: 'Tenant2',
            lastLoginTime: '2024-01-02',
            contractPeriodEndDate: '2025-01-02',
            plan: 'Plan2',
          );

          controller = createController();

          // Act - 第一次加载
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => userData1);
          await triggerAndWaitForDataLoading(controller);
          expect(controller.user.value, equals(userData1));

          // Act - 第二次加载
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => userData2);
          await triggerAndWaitForDataLoading(controller);

          // Assert
          expect(controller.user.value, equals(userData2));
          expect(controller.user.value.userName, equals('user2'));
          verify(mockMyPageLoadDataCase.call(any)).called(2);
        });

        test('并发加载应该正确处理', () async {
          // Arrange
          final completer1 = Completer<MyPageUIModel>();
          final completer2 = Completer<MyPageUIModel>();
          var callCount = 0;

          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) {
            callCount++;
            if (callCount == 1) return completer1.future;
            return completer2.future;
          });

          controller = createController();

          // Act - 启动两个并发加载
          controller.onReady();
          controller.onReady();

          // 让第二个请求先完成
          completer2.complete(testUserModel);
          await Future.delayed(const Duration(milliseconds: 10)); // 给时间处理

          // 然后完成第一个请求
          final laterUserModel = MyPageUIModel(
            userName: 'laterUser',
            firstName: 'Later',
            lastName: 'User',
            tenantName: 'Later Tenant',
            lastLoginTime: '2024-01-03',
            contractPeriodEndDate: '2025-01-03',
            plan: 'Later Plan',
          );
          completer1.complete(laterUserModel);
          await waitForDataLoading(controller);

          // Assert - 应该使用最后完成的数据
          expect(controller.user.value, equals(laterUserModel));
          expect(controller.isLoading.value, isFalse);
        });
      });
    });

    // ==========================================
    // Phase 2: 核心业务逻辑测试
    // ==========================================
    group('🔧 Phase 2: 核心业务逻辑测试', () {
      group('2.1 生物识别开关功能测试', () {
        test('开启生物识别应该调用 openBiometrics', () async {
          // Arrange
          when(mockBiometricsSwitchController.openBiometrics()).thenAnswer((_) async {});
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          await testController.testBiometricsChange(true);

          // Assert
          verify(mockBiometricsSwitchController.openBiometrics()).called(1);
          verifyNever(mockBiometricsSwitchController.disableBiometrics());
        });

        test('关闭生物识别应该调用 disableBiometrics', () async {
          // Arrange
          when(mockBiometricsSwitchController.disableBiometrics()).thenAnswer((_) async {});
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          await testController.testBiometricsChange(false);

          // Assert
          verify(mockBiometricsSwitchController.disableBiometrics()).called(1);
          verifyNever(mockBiometricsSwitchController.openBiometrics());
        });

        test('生物识别切换后应该重新加载数据', () async {
          // Arrange
          when(mockBiometricsSwitchController.openBiometrics()).thenAnswer((_) async {});
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          await testController.testBiometricsChange(true);

          // Assert
          verify(mockMyPageLoadDataCase.call(any)).called(1);
          expect(controller.user.value, equals(testUserModel));
        });

        test('生物识别操作异常时应该正确处理', () async {
          // Arrange
          when(mockBiometricsSwitchController.openBiometrics()).thenThrow(Exception('Biometrics failed'));
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          await testController.testBiometricsChange(true);

          // Assert
          verify(mockBiometricsSwitchController.openBiometrics()).called(1);
          expect(testController.handleExceptionCalled, isTrue);
          expect(testController.lastException?.toString(), contains('Biometrics failed'));
        });
      });

      group('2.2 数量扫描模式切换测试', () {
        test('切换数量扫描模式应该更新用户状态', () async {
          // Arrange
          when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn('tenant123');
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(456);
          when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {});
          controller = createController();
          var userUpdateCount = 0;
          controller.user.listen((_) => userUpdateCount++);

          // Act
          await controller.quantityCountScanChange(true);

          // Assert
          expect(controller.user.value.isQuantityCountScan, isTrue);
          expect(userUpdateCount, greaterThan(0)); // user.refresh() 被调用
        });

        test('切换数量扫描模式应该保存设置到存储', () async {
          // Arrange
          when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn('tenant123');
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(456);
          when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {});
          controller = createController();

          // Act
          await controller.quantityCountScanChange(true);

          // Assert
          verify(
            mockStorageUtils.setValue(
              StorageKeys.isNumberNeedAutoIncrease,
              '{"isQuantityCountScan":true,"tenantId":"tenant123","userId":456}',
            ),
          ).called(1);
        });

        test('关闭数量扫描模式应该正确处理', () async {
          // Arrange
          when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn('tenant123');
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(456);
          when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {});
          controller = createController();

          // Act
          await controller.quantityCountScanChange(false);

          // Assert
          expect(controller.user.value.isQuantityCountScan, isFalse);
          verify(
            mockStorageUtils.setValue(
              StorageKeys.isNumberNeedAutoIncrease,
              '{"isQuantityCountScan":false,"tenantId":"tenant123","userId":456}',
            ),
          ).called(1);
        });

        test('数量扫描设置中 tenantId 或 userId 为空时应该正确处理', () async {
          // Arrange
          when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn(null);
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(null);
          when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {});
          controller = createController();

          // Act
          await controller.quantityCountScanChange(true);

          // Assert
          expect(controller.user.value.isQuantityCountScan, isTrue);
          verify(
            mockStorageUtils.setValue(
              StorageKeys.isNumberNeedAutoIncrease,
              '{"isQuantityCountScan":true,"tenantId":null,"userId":null}',
            ),
          ).called(1);
        });
      });

      group('2.3 登出流程测试', () {
        test('logout 应该显示确认对话框', () async {
          // Arrange
          when(
            mockDialogService.show(
              content: anyNamed('content'),
              confirmText: anyNamed('confirmText'),
              cancelText: anyNamed('cancelText'),
              onConfirm: anyNamed('onConfirm'),
            ),
          ).thenAnswer((_) async {});
          controller = createController();

          // Act
          await controller.logout();

          // Assert
          verify(
            mockDialogService.show(
              content: 'ログアウトしますか？',
              confirmText: 'はい',
              cancelText: 'いいえ',
              onConfirm: anyNamed('onConfirm'),
            ),
          ).called(1);
        });

        test('_logout 应该调用 logoutUseCase', () async {
          // Arrange
          when(mockLogoutUseCase.call(any)).thenAnswer((_) async {});
          when(mockStorageUtils.removeValue(any)).thenAnswer((_) async {});
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          await testController.testLogout();

          // Assert
          verify(mockLogoutUseCase.call(argThat(isA<NoParams>()))).called(1);
        });

        test('_logout 应该移除 token', () async {
          // Arrange
          when(mockLogoutUseCase.call(any)).thenAnswer((_) async {});
          when(mockStorageUtils.removeValue(any)).thenAnswer((_) async {});
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          await testController.testLogout();
          // 等待延迟操作完成
          await Future.delayed(const Duration(seconds: 2));

          // Assert
          verify(mockStorageUtils.removeValue(StorageKeys.token)).called(1);
        });

        test('_logout 应该导航到登录页面', () async {
          // Arrange
          when(mockLogoutUseCase.call(any)).thenAnswer((_) async {});
          when(mockStorageUtils.removeValue(any)).thenAnswer((_) async {});
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          await testController.testLogout();

          // Assert
          verify(mockNavigationService.navigateOffAll(AutoRoutes.login)).called(1);
        });

        test('_logout 流程应该按正确顺序执行', () async {
          // Arrange
          final callOrder = <String>[];
          when(mockLogoutUseCase.call(any)).thenAnswer((_) async {
            callOrder.add('logoutUseCase');
          });
          when(mockStorageUtils.removeValue(any)).thenAnswer((_) async {
            callOrder.add('removeToken');
          });
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {
            callOrder.add('navigateToLogin');
          });
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          await testController.testLogout();
          await Future.delayed(const Duration(seconds: 2));

          // Assert
          expect(callOrder, orderedEquals(['logoutUseCase', 'navigateToLogin', 'removeToken']));
        });

        test('登出过程中发生异常应该正确处理', () async {
          // Arrange
          when(mockLogoutUseCase.call(any)).thenThrow(Exception('Logout failed'));
          when(mockStorageUtils.removeValue(any)).thenAnswer((_) async {});
          when(mockNavigationService.navigateOffAll(any)).thenAnswer((_) async {});
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          await testController.testLogout();

          // Assert
          expect(testController.handleExceptionCalled, isTrue);
          expect(testController.lastException?.toString(), contains('Logout failed'));
          // 即使出错也应该尝试导航
          verify(mockNavigationService.navigateOffAll(AutoRoutes.login)).called(1);
        });
      });

      group('2.4 平台通道和导航测试', () {
        test('toRFIDSettingPage 应该调用平台方法', () {
          // Arrange
          when(mockPlatformChannel.invokeMethod(any)).thenAnswer((_) async {});
          controller = createController();

          // Act
          controller.toRFIDSettingPage();

          // Assert
          verify(mockPlatformChannel.invokeMethod(MethodChannelConstant.toRFIDSettingPage)).called(1);
        });

        test('toAccountPage 应该导航到账户设置页面', () async {
          // Arrange
          when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async {});
          controller = createController();
          controller.user.value = testUserModel;

          // Act
          await controller.toAccountPage();

          // Assert
          verify(mockNavigationService.navigateTo(AutoRoutes.meAccountSetting, arguments: testUserModel)).called(1);
        });

        test('平台方法调用异常应该正确处理', () {
          // Arrange
          when(mockPlatformChannel.invokeMethod(any)).thenThrow(Exception('Platform error'));
          controller = createController();

          // Act & Assert - 平台方法异常会直接抛出
          expect(() => controller.toRFIDSettingPage(), throwsA(isA<Exception>()));
          verify(mockPlatformChannel.invokeMethod(MethodChannelConstant.toRFIDSettingPage)).called(1);
        });

        test('导航异常应该正确处理', () async {
          // Arrange
          when(
            mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
          ).thenThrow(Exception('Navigation error'));
          controller = createController();
          controller.user.value = testUserModel;

          // Act & Assert - toAccountPage 方法没有内置异常处理，所以异常会直接抛出
          expect(() => controller.toAccountPage(), throwsA(isA<Exception>()));
          verify(mockNavigationService.navigateTo(AutoRoutes.meAccountSetting, arguments: testUserModel)).called(1);
        });
      });

      group('2.5 URL 导航和票据生成测试', () {
        test('toTutorial 应该生成正确的教程 URL', () {
          // Arrange
          when(mockEnvHelper.getGuideBaseUrl()).thenReturn('https://guide.example.com');
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          final tutorialUrl = testController.testBuildTutorialUrl();
          final ticket = testController.testGetParamTicket();

          // Assert
          expect(tutorialUrl, equals('https://guide.example.com/support/tutorial/?ticket=$ticket'));
          expect(tutorialUrl, contains('https://guide.example.com/support/tutorial/?ticket='));
          expect(ticket, isNotEmpty);
          expect(ticket.length, equals(40)); // SHA1 哈希长度
          verify(mockEnvHelper.getGuideBaseUrl()).called(1);
        });

        test('openSupportFaq 应该检查 FAQ URL 是否为空', () {
          // Arrange - 空 URL
          when(mockEnvHelper.getSupportFaq()).thenReturn('');
          controller = createController();

          // Act
          controller.openSupportFaq();

          // Assert
          verify(mockEnvHelper.getSupportFaq()).called(1);
          // URL 为空时不应该调用 _openUrl（无法直接验证，但可以确保没有异常）
        });

        test('openSupportFaq 应该使用非空 FAQ URL', () {
          // Arrange - 非空 URL
          when(mockEnvHelper.getSupportFaq()).thenReturn('https://faq.example.com');
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          final faqUrl = testController.testGetSupportFaqUrl();

          // Assert
          expect(faqUrl, equals('https://faq.example.com'));
          expect(faqUrl?.isNotEmpty, isTrue);
          verify(mockEnvHelper.getSupportFaq()).called(1);
        });

        test('toStarterGuide 应该生成正确的入门指南 URL', () {
          // Arrange
          when(mockEnvHelper.getGuideBaseUrl()).thenReturn('https://guide.example.com');
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          final starterGuideUrl = testController.testBuildStarterGuideUrl();
          final ticket = testController.testGetParamTicket();

          // Assert
          expect(starterGuideUrl, equals('https://guide.example.com/support/starterguide/?ticket=$ticket'));
          expect(starterGuideUrl, contains('https://guide.example.com/support/starterguide/?ticket='));
          expect(ticket, isNotEmpty);
          expect(ticket.length, equals(40)); // SHA1 哈希长度
          verify(mockEnvHelper.getGuideBaseUrl()).called(1);
        });

        test('_getParamTicket 应该生成基于日期的哈希票据', () {
          // Arrange
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          final ticket1 = testController.testGetParamTicket();
          final ticket2 = testController.testGetParamTicket();

          // Assert
          expect(ticket1, isNotEmpty);
          expect(ticket1, isA<String>());
          expect(ticket1.length, equals(40)); // SHA1 哈希长度
          expect(ticket1, equals(ticket2)); // 同一天内应该相同
        });

        test('不同日期应该生成不同的票据', () {
          // 这个测试比较难实现，因为需要模拟时间，暂时跳过
          // 实际项目中可能需要将时间获取抽象为可注入的依赖
        });
      });

      group('2.6 复杂业务场景测试', () {
        test('连续切换生物识别状态应该正确处理', () async {
          // Arrange
          when(mockBiometricsSwitchController.openBiometrics()).thenAnswer((_) async {});
          when(mockBiometricsSwitchController.disableBiometrics()).thenAnswer((_) async {});
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          await testController.testBiometricsChange(true);
          await testController.testBiometricsChange(false);
          await testController.testBiometricsChange(true);

          // Assert
          verify(mockBiometricsSwitchController.openBiometrics()).called(2);
          verify(mockBiometricsSwitchController.disableBiometrics()).called(1);
          verify(mockMyPageLoadDataCase.call(any)).called(3);
        });

        test('同时进行多个异步操作应该正确处理', () async {
          // Arrange
          when(mockBiometricsSwitchController.openBiometrics()).thenAnswer((_) async {
            await Future.delayed(const Duration(milliseconds: 50));
          });
          when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {
            await Future.delayed(const Duration(milliseconds: 30));
          });
          when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn('tenant123');
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(456);
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => testUserModel);
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act - 同时触发两个异步操作
          final biometricsFuture = testController.testBiometricsChange(true);
          final quantityFuture = controller.quantityCountScanChange(true);

          await Future.wait([biometricsFuture, quantityFuture]);

          // Assert
          verify(mockBiometricsSwitchController.openBiometrics()).called(1);
          verify(
            mockStorageUtils.setValue(
              StorageKeys.isNumberNeedAutoIncrease,
              '{"isQuantityCountScan":true,"tenantId":"tenant123","userId":456}',
            ),
          ).called(1);
          verify(mockMyPageLoadDataCase.call(any)).called(1);
        });

        test('业务操作后状态应该保持一致', () async {
          // Arrange
          when(mockBiometricsSwitchController.openBiometrics()).thenAnswer((_) async {});
          final updatedUserModel = MyPageUIModel(
            userName: testUserModel.userName,
            firstName: testUserModel.firstName,
            lastName: testUserModel.lastName,
            tenantName: testUserModel.tenantName,
            lastLoginTime: testUserModel.lastLoginTime,
            contractPeriodEndDate: testUserModel.contractPeriodEndDate,
            plan: testUserModel.plan,
            isBiometrics: true,
            isQuantityCountScan: true,
          );
          when(mockMyPageLoadDataCase.call(any)).thenAnswer((_) async => updatedUserModel);
          when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn('tenant123');
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(456);
          when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {});
          final testController = createController(useTestController: true) as TestMyPageController;
          controller = testController;

          // Act
          await testController.testBiometricsChange(true);
          await controller.quantityCountScanChange(true);

          // Assert
          expect(controller.user.value.isBiometrics, isTrue);
          expect(controller.user.value.isQuantityCountScan, isTrue);
        });

        test('大量并发操作不应该导致状态不一致', () async {
          // Arrange
          when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn('tenant123');
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(456);
          when(mockStorageUtils.setValue(any, any)).thenAnswer((_) async {
            await Future.delayed(const Duration(milliseconds: 10));
          });
          controller = createController();

          // Act - 快速连续切换状态
          final futures = <Future>[];
          for (int i = 0; i < 10; i++) {
            futures.add(controller.quantityCountScanChange(i % 2 == 0));
          }
          await Future.wait(futures);

          // Assert
          expect(controller.user.value.isQuantityCountScan, isFalse); // 最后一次是 false
          verify(
            mockStorageUtils.setValue(
              StorageKeys.isNumberNeedAutoIncrease,
              anyOf([
                '{"isQuantityCountScan":true,"tenantId":"tenant123","userId":456}',
                '{"isQuantityCountScan":false,"tenantId":"tenant123","userId":456}',
              ]),
            ),
          ).called(10);
        });
      });
    });
  });
}

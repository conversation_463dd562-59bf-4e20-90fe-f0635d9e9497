// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/mypage/presentation/controllers/mypage_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i13;
import 'dart:ui' as _i21;

import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart' as _i5;
import 'package:asset_force_mobile_v2/core/platform/method_channel.dart'
    as _i24;
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i19;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i8;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i9;
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i4;
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart' as _i14;
import 'package:asset_force_mobile_v2/core/utils/biometrics_switch_util.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i23;
import 'package:asset_force_mobile_v2/features/me/mypage/domain/repositories/logout_repository.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/me/mypage/domain/usecase/logout_usercase.dart'
    as _i15;
import 'package:asset_force_mobile_v2/features/me/mypage/domain/usecase/mypage_usercase.dart'
    as _i12;
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/controllers/biometrics_switch_controller.dart'
    as _i18;
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/models/mypage_ui_model.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart'
    as _i22;
import 'package:flutter/services.dart' as _i11;
import 'package:get/get.dart' as _i10;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i20;
import 'package:local_auth/local_auth.dart' as _i16;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i17;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUserRepository_0 extends _i1.SmartFake
    implements _i2.UserRepository {
  _FakeUserRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBiometricsSwitchUtil_1 extends _i1.SmartFake
    implements _i3.BiometricsSwitchUtil {
  _FakeBiometricsSwitchUtil_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIStorageUtils_2 extends _i1.SmartFake implements _i4.IStorageUtils {
  _FakeIStorageUtils_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIEnvHelper_3 extends _i1.SmartFake implements _i5.IEnvHelper {
  _FakeIEnvHelper_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMyPageUIModel_4 extends _i1.SmartFake implements _i6.MyPageUIModel {
  _FakeMyPageUIModel_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLogoutRepository_5 extends _i1.SmartFake
    implements _i7.LogoutRepository {
  _FakeLogoutRepository_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDialogService_6 extends _i1.SmartFake implements _i8.DialogService {
  _FakeDialogService_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_7 extends _i1.SmartFake
    implements _i9.NavigationService {
  _FakeNavigationService_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_8<T> extends _i1.SmartFake
    implements _i10.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMethodCodec_9 extends _i1.SmartFake implements _i11.MethodCodec {
  _FakeMethodCodec_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBinaryMessenger_10 extends _i1.SmartFake
    implements _i11.BinaryMessenger {
  _FakeBinaryMessenger_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [MyPageLoadDataCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockMyPageLoadDataCase extends _i1.Mock
    implements _i12.MyPageLoadDataCase {
  MockMyPageLoadDataCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.UserRepository get userRepository =>
      (super.noSuchMethod(
            Invocation.getter(#userRepository),
            returnValue: _FakeUserRepository_0(
              this,
              Invocation.getter(#userRepository),
            ),
          )
          as _i2.UserRepository);

  @override
  _i3.BiometricsSwitchUtil get biometricsSwitchUtil =>
      (super.noSuchMethod(
            Invocation.getter(#biometricsSwitchUtil),
            returnValue: _FakeBiometricsSwitchUtil_1(
              this,
              Invocation.getter(#biometricsSwitchUtil),
            ),
          )
          as _i3.BiometricsSwitchUtil);

  @override
  _i4.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_2(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i4.IStorageUtils);

  @override
  _i5.IEnvHelper get envHelper =>
      (super.noSuchMethod(
            Invocation.getter(#envHelper),
            returnValue: _FakeIEnvHelper_3(this, Invocation.getter(#envHelper)),
          )
          as _i5.IEnvHelper);

  @override
  _i13.Future<_i6.MyPageUIModel> call(_i14.NoParams? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i13.Future<_i6.MyPageUIModel>.value(
              _FakeMyPageUIModel_4(this, Invocation.method(#call, [params])),
            ),
          )
          as _i13.Future<_i6.MyPageUIModel>);
}

/// A class which mocks [LogoutUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLogoutUseCase extends _i1.Mock implements _i15.LogoutUseCase {
  MockLogoutUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.LogoutRepository get logoutRepository =>
      (super.noSuchMethod(
            Invocation.getter(#logoutRepository),
            returnValue: _FakeLogoutRepository_5(
              this,
              Invocation.getter(#logoutRepository),
            ),
          )
          as _i7.LogoutRepository);

  @override
  _i13.Future<void> call(_i14.NoParams? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);
}

/// A class which mocks [BiometricsSwitchUtil].
///
/// See the documentation for Mockito's code generation for more information.
class MockBiometricsSwitchUtil extends _i1.Mock
    implements _i3.BiometricsSwitchUtil {
  MockBiometricsSwitchUtil() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i13.Future<String?> isAccountWorkingProperly({required String? password}) =>
      (super.noSuchMethod(
            Invocation.method(#isAccountWorkingProperly, [], {
              #password: password,
            }),
            returnValue: _i13.Future<String?>.value(),
          )
          as _i13.Future<String?>);

  @override
  _i13.Future<void> closeOrOpenBiometrics({required bool? isOpen}) =>
      (super.noSuchMethod(
            Invocation.method(#closeOrOpenBiometrics, [], {#isOpen: isOpen}),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  bool getUserBiometricsOpenSwitch() =>
      (super.noSuchMethod(
            Invocation.method(#getUserBiometricsOpenSwitch, []),
            returnValue: false,
          )
          as bool);

  @override
  _i13.Future<bool> isBiometricsAvailable() =>
      (super.noSuchMethod(
            Invocation.method(#isBiometricsAvailable, []),
            returnValue: _i13.Future<bool>.value(false),
          )
          as _i13.Future<bool>);

  @override
  _i13.Future<List<_i16.BiometricType>> getAvailableBiometrics() =>
      (super.noSuchMethod(
            Invocation.method(#getAvailableBiometrics, []),
            returnValue: _i13.Future<List<_i16.BiometricType>>.value(
              <_i16.BiometricType>[],
            ),
          )
          as _i13.Future<List<_i16.BiometricType>>);

  @override
  _i13.Future<void> authenticateBiometricsSetting() =>
      (super.noSuchMethod(
            Invocation.method(#authenticateBiometricsSetting, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> setBiometricSecretPassword({
    required String? userName,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setBiometricSecretPassword, [], {
              #userName: userName,
              #password: password,
            }),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<String> getBiometricSecretPassword() =>
      (super.noSuchMethod(
            Invocation.method(#getBiometricSecretPassword, []),
            returnValue: _i13.Future<String>.value(
              _i17.dummyValue<String>(
                this,
                Invocation.method(#getBiometricSecretPassword, []),
              ),
            ),
          )
          as _i13.Future<String>);
}

/// A class which mocks [IStorageUtils].
///
/// See the documentation for Mockito's code generation for more information.
class MockIStorageUtils extends _i1.Mock implements _i4.IStorageUtils {
  MockIStorageUtils() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i13.Future<void> init() =>
      (super.noSuchMethod(
            Invocation.method(#init, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> initPersonalStorage({required String? cryptedName}) =>
      (super.noSuchMethod(
            Invocation.method(#initPersonalStorage, [], {
              #cryptedName: cryptedName,
            }),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  T? getValue<T>(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#getValue, [key], {#personal: personal}),
          )
          as T?);

  @override
  _i13.Future<void> setValue<T>(
    String? key,
    T? value, {
    bool? personal = false,
    int? durationInSeconds,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #setValue,
              [key, value],
              {#personal: personal, #durationInSeconds: durationInSeconds},
            ),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> removeValue(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#removeValue, [key], {#personal: personal}),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  bool containsKey(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#containsKey, [key], {#personal: personal}),
            returnValue: false,
          )
          as bool);

  @override
  _i13.Future<void> clearAll({bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#clearAll, [], {#personal: personal}),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  List<String> getAllKeys({bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#getAllKeys, [], {#personal: personal}),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  String getAssetScanLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getAssetScanLocation, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getAssetScanLocation, []),
            ),
          )
          as String);

  @override
  _i13.Future<void> setAssetScanLocation({required String? assetLocation}) =>
      (super.noSuchMethod(
            Invocation.method(#setAssetScanLocation, [], {
              #assetLocation: assetLocation,
            }),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);
}

/// A class which mocks [BiometricsSwitchController].
///
/// See the documentation for Mockito's code generation for more information.
class MockBiometricsSwitchController extends _i1.Mock
    implements _i18.BiometricsSwitchController {
  MockBiometricsSwitchController() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.BiometricsSwitchUtil get biometricsSwitchUtil =>
      (super.noSuchMethod(
            Invocation.getter(#biometricsSwitchUtil),
            returnValue: _FakeBiometricsSwitchUtil_1(
              this,
              Invocation.getter(#biometricsSwitchUtil),
            ),
          )
          as _i3.BiometricsSwitchUtil);

  @override
  _i8.DialogService get dialogService =>
      (super.noSuchMethod(
            Invocation.getter(#dialogService),
            returnValue: _FakeDialogService_6(
              this,
              Invocation.getter(#dialogService),
            ),
          )
          as _i8.DialogService);

  @override
  _i9.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_7(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i9.NavigationService);

  @override
  _i10.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_8<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i10.InternalFinalCallback<void>);

  @override
  _i10.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_8<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i10.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(Invocation.getter(#initialized), returnValue: false)
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(Invocation.getter(#listeners), returnValue: 0)
          as int);

  @override
  _i13.Future<void> disableBiometrics() =>
      (super.noSuchMethod(
            Invocation.method(#disableBiometrics, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> openBiometrics() =>
      (super.noSuchMethod(
            Invocation.method(#openBiometrics, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i19.ErrorHandlingMode? mode = _i19.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i20.Disposer addListener(_i20.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
          )
          as _i20.Disposer);

  @override
  void removeListener(_i21.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i21.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i20.Disposer addListenerId(Object? key, _i20.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
          )
          as _i20.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [DialogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDialogService extends _i1.Mock implements _i8.DialogService {
  MockDialogService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i13.Future<void> show({
    String? title,
    required String? content,
    String? confirmText,
    String? cancelText,
    _i21.VoidCallback? onConfirm,
    _i21.VoidCallback? onCancel,
    bool? barrierDismissible,
    _i22.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#show, [], {
              #title: title,
              #content: content,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> showInput({
    String? title,
    required String? contentText,
    String? hintText,
    String? initialValue,
    String? confirmText,
    String? cancelText,
    required dynamic Function(String)? onConfirm,
    _i21.VoidCallback? onCancel,
    String? Function(String?)? inputValidator,
    int? maxLength,
    _i11.TextInputType? keyboardType,
    bool? obscureText,
    bool? barrierDismissible,
    _i22.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showInput, [], {
              #title: title,
              #contentText: contentText,
              #hintText: hintText,
              #initialValue: initialValue,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #inputValidator: inputValidator,
              #maxLength: maxLength,
              #keyboardType: keyboardType,
              #obscureText: obscureText,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  void showToast(String? message) => super.noSuchMethod(
    Invocation.method(#showToast, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void showCustomToast(String? message) => super.noSuchMethod(
    Invocation.method(#showCustomToast, [message]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i9.NavigationService {
  MockNavigationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i13.Future<dynamic> navigateTo(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i13.Future<dynamic>.value(),
          )
          as _i13.Future<dynamic>);

  @override
  _i13.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i13.Future<dynamic>.value(),
          )
          as _i13.Future<dynamic>);

  @override
  _i13.Future<bool> navigateUntil(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i13.Future<bool>.value(false),
          )
          as _i13.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Future<dynamic> toAssetDetail(_i23.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i13.Future<dynamic>.value(),
          )
          as _i13.Future<dynamic>);
}

/// A class which mocks [IEnvHelper].
///
/// See the documentation for Mockito's code generation for more information.
class MockIEnvHelper extends _i1.Mock implements _i5.IEnvHelper {
  MockIEnvHelper() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set environment(String? value) => super.noSuchMethod(
    Invocation.setter(#environment, value),
    returnValueForMissingStub: null,
  );

  @override
  bool isAndroid() =>
      (super.noSuchMethod(Invocation.method(#isAndroid, []), returnValue: false)
          as bool);

  @override
  bool isIOS() =>
      (super.noSuchMethod(Invocation.method(#isIOS, []), returnValue: false)
          as bool);

  @override
  bool isWindows() =>
      (super.noSuchMethod(Invocation.method(#isWindows, []), returnValue: false)
          as bool);

  @override
  bool isLinux() =>
      (super.noSuchMethod(Invocation.method(#isLinux, []), returnValue: false)
          as bool);

  @override
  bool isMacOS() =>
      (super.noSuchMethod(Invocation.method(#isMacOS, []), returnValue: false)
          as bool);

  @override
  String getEnvironment() =>
      (super.noSuchMethod(
            Invocation.method(#getEnvironment, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getEnvironment, []),
            ),
          )
          as String);

  @override
  bool isProd() =>
      (super.noSuchMethod(Invocation.method(#isProd, []), returnValue: false)
          as bool);

  @override
  bool isDev() =>
      (super.noSuchMethod(Invocation.method(#isDev, []), returnValue: false)
          as bool);

  @override
  bool isQa() =>
      (super.noSuchMethod(Invocation.method(#isQa, []), returnValue: false)
          as bool);

  @override
  bool isStg() =>
      (super.noSuchMethod(Invocation.method(#isStg, []), returnValue: false)
          as bool);

  @override
  String getAppVersion() =>
      (super.noSuchMethod(
            Invocation.method(#getAppVersion, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getAppVersion, []),
            ),
          )
          as String);

  @override
  String getApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getApiHost, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getApiHost, []),
            ),
          )
          as String);

  @override
  String getAuthApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getAuthApiHost, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getAuthApiHost, []),
            ),
          )
          as String);

  @override
  String getApiGatewayHost() =>
      (super.noSuchMethod(
            Invocation.method(#getApiGatewayHost, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getApiGatewayHost, []),
            ),
          )
          as String);

  @override
  String getSocketApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getSocketApiHost, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getSocketApiHost, []),
            ),
          )
          as String);

  @override
  String getReportApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getReportApiHost, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getReportApiHost, []),
            ),
          )
          as String);

  @override
  String getAppNativeName() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativeName, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeName, []),
            ),
          )
          as String);

  @override
  String getAppNativePackageName() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativePackageName, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getAppNativePackageName, []),
            ),
          )
          as String);

  @override
  String getAppNativeVersion() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativeVersion, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeVersion, []),
            ),
          )
          as String);

  @override
  String getAppNativeBuildNumber() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativeBuildNumber, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeBuildNumber, []),
            ),
          )
          as String);

  @override
  String getGuideBaseUrl() =>
      (super.noSuchMethod(
            Invocation.method(#getGuideBaseUrl, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getGuideBaseUrl, []),
            ),
          )
          as String);

  @override
  String getSupportFaq() =>
      (super.noSuchMethod(
            Invocation.method(#getSupportFaq, []),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.method(#getSupportFaq, []),
            ),
          )
          as String);

  @override
  _i13.Future<void> updateAuthHost() =>
      (super.noSuchMethod(
            Invocation.method(#updateAuthHost, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> updateServerEnv() =>
      (super.noSuchMethod(
            Invocation.method(#updateServerEnv, []),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> setHost(String? zoneId) =>
      (super.noSuchMethod(
            Invocation.method(#setHost, [zoneId]),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);
}

/// A class which mocks [PlatformChannel].
///
/// See the documentation for Mockito's code generation for more information.
class MockPlatformChannel extends _i1.Mock implements _i24.PlatformChannel {
  MockPlatformChannel() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get name =>
      (super.noSuchMethod(
            Invocation.getter(#name),
            returnValue: _i17.dummyValue<String>(
              this,
              Invocation.getter(#name),
            ),
          )
          as String);

  @override
  _i11.MethodCodec get codec =>
      (super.noSuchMethod(
            Invocation.getter(#codec),
            returnValue: _FakeMethodCodec_9(this, Invocation.getter(#codec)),
          )
          as _i11.MethodCodec);

  @override
  _i11.BinaryMessenger get binaryMessenger =>
      (super.noSuchMethod(
            Invocation.getter(#binaryMessenger),
            returnValue: _FakeBinaryMessenger_10(
              this,
              Invocation.getter(#binaryMessenger),
            ),
          )
          as _i11.BinaryMessenger);

  @override
  _i13.Future<void> syncStorage(Map<String, Object>? data) =>
      (super.noSuchMethod(
            Invocation.method(#syncStorage, [data]),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<void> sendEventToIonic(Map<String, Object>? data) =>
      (super.noSuchMethod(
            Invocation.method(#sendEventToIonic, [data]),
            returnValue: _i13.Future<void>.value(),
            returnValueForMissingStub: _i13.Future<void>.value(),
          )
          as _i13.Future<void>);

  @override
  _i13.Future<T?> invokeMethod<T>(String? method, [dynamic arguments]) =>
      (super.noSuchMethod(
            Invocation.method(#invokeMethod, [method, arguments]),
            returnValue: _i13.Future<T?>.value(),
          )
          as _i13.Future<T?>);

  @override
  _i13.Future<List<T>?> invokeListMethod<T>(
    String? method, [
    dynamic arguments,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#invokeListMethod, [method, arguments]),
            returnValue: _i13.Future<List<T>?>.value(),
          )
          as _i13.Future<List<T>?>);

  @override
  _i13.Future<Map<K, V>?> invokeMapMethod<K, V>(
    String? method, [
    dynamic arguments,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#invokeMapMethod, [method, arguments]),
            returnValue: _i13.Future<Map<K, V>?>.value(),
          )
          as _i13.Future<Map<K, V>?>);

  @override
  void setMethodCallHandler(
    _i13.Future<dynamic> Function(_i11.MethodCall)? handler,
  ) => super.noSuchMethod(
    Invocation.method(#setMethodCallHandler, [handler]),
    returnValueForMissingStub: null,
  );
}

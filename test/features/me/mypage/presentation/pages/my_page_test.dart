import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/controllers/mypage_controller.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/models/mypage_ui_model.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/pages/my_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'my_page_test.mocks.dart';

// 生成 Mock 类
@GenerateNiceMocks([MockSpec<MyPageController>()])
class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  group('🧪 MyPage Widget 测试', () {
    final mockInternalFinalCallback = MockInternalFinalCallback<void>();
    late MockMyPageController mockController;
    late MyPageUIModel testUserModel;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    // 创建测试 Widget 环境的辅助方法
    Widget createWidgetUnderTest() {
      return GetMaterialApp(theme: AppTheme.lightTheme, home: const MyPage());
    }

    // 设置 Mock Controller 的辅助方法
    void setupMockController() {
      // 创建测试用户数据
      testUserModel = MyPageUIModel(
        userName: 'testuser',
        firstName: 'テスト',
        lastName: 'ユーザー',
        tenantName: 'テストテナント',
        lastLoginTime: '2024-01-01 12:00',
        appVersion: '1.0.0',
        isQuantityCountScan: true,
        isBiometrics: true,
        isUserBiometricsOpenSwitch: false,
        anyNew: true,
        isShowSupportFaq: true,
        contractPeriodEndDate: '2024-12-31',
        plan: '6', // 试用模式
      );

      // Mock 响应式变量
      when(mockController.user).thenReturn(testUserModel.obs);
      when(mockController.isLoading).thenReturn(false.obs);

      // 关键：Mock GetX 生命周期方法
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onInit()).thenAnswer((_) async {});
      when(mockController.onClose()).thenAnswer((_) async {});
      when(mockController.onReady()).thenAnswer((_) async {});

      // Mock 导航和业务方法
      when(mockController.goBack()).thenReturn(null);
      when(mockController.logout()).thenAnswer((_) async {});
      when(mockController.toAccountPage()).thenAnswer((_) async {});
      when(mockController.toRFIDSettingPage()).thenReturn(null);
      when(mockController.toScanAudioSetting()).thenReturn(null);
      when(mockController.toFontSizeSetting()).thenReturn(null);
      when(mockController.toMessagePage()).thenReturn(null);
      when(mockController.toUserGuide()).thenReturn(null);
      when(mockController.toStarterGuide()).thenReturn(null);
      when(mockController.toTutorial()).thenReturn(null);
      when(mockController.openSupportFaq()).thenReturn(null);
      when(mockController.quantityCountScanChange(any)).thenAnswer((_) async {});
      when(mockController.biometricsChange(any)).thenAnswer((_) async {});
    }

    setUp(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      Get.testMode = true; // 关键：启用 GetX 测试模式
      Get.reset(); // 确保干净的测试环境

      // 创建 Mock 实例
      mockController = MockMyPageController();

      // 配置 Mock Controller
      setupMockController();

      // 注册 Mock Controller
      Get.put<MyPageController>(mockController);
    });

    tearDown(() {
      // 清理资源
      reset(mockController);
      clearInteractions(mockController);
      Get.reset();
    });

    group('🏗️ Phase 0: 测试基础设施验证', () {
      group('0.1 Mock 依赖创建验证', () {
        test('应该能够创建 MockMyPageController', () {
          // Assert
          expect(mockController, isNotNull);
          expect(mockController, isA<MyPageController>());
        });

        test('测试用户数据应该正确初始化', () {
          // Assert
          expect(testUserModel.displayName, equals('ユーザー テスト'));
          expect(testUserModel.tenantName, equals('テストテナント'));
          expect(testUserModel.userName, equals('testuser'));
          expect(testUserModel.isQuantityCountScan, isTrue);
          expect(testUserModel.isBiometrics, isTrue);
          expect(testUserModel.isTrial(), isTrue); // plan = '6'
        });

        test('Mock Controller 的响应式变量应该正确配置', () {
          // Assert
          expect(mockController.user.value, equals(testUserModel));
          expect(mockController.isLoading.value, isFalse);
        });

        test('GetX 依赖注入应该正常工作', () {
          // Act
          final controller = Get.find<MyPageController>();

          // Assert
          expect(controller, equals(mockController));
          expect(identical(controller, mockController), isTrue);
        });

        test('应该能够对 Mock 方法进行 verify', () {
          // Act
          mockController.goBack();

          // Assert
          verify(mockController.goBack()).called(1);
        });
      });

      group('0.2 Widget 构建测试', () {
        testWidgets('应该能够成功构建 MyPage Widget', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 50));

          // Assert
          expect(find.byType(MyPage), findsOneWidget);
          expect(find.byType(Scaffold), findsOneWidget);
        });

        testWidgets('应该能够构建 AppBar', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 50));

          // Assert
          expect(find.byType(AppBar), findsOneWidget);
          expect(find.text('マイページ'), findsOneWidget);
        });

        testWidgets('应该能够构建主要的 ListView', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 50));

          // Assert
          expect(find.byType(ListView), findsOneWidget);
        });

        testWidgets('Controller 依赖注入验证 - 正常状态下应该有 Controller', (tester) async {
          // Arrange - 确保有正确的 controller
          setupMockController();
          Get.put<MyPageController>(mockController);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证正常构建成功
          expect(find.byType(MyPage), findsOneWidget);
          expect(find.byType(Scaffold), findsOneWidget);
          expect(find.byType(AppBar), findsOneWidget);

          // 验证 Controller 被正确注入
          final injectedController = Get.find<MyPageController>();
          expect(injectedController, isNotNull);
          expect(injectedController, equals(mockController));
        });

        testWidgets('重复构建不应该造成内存泄漏', (tester) async {
          // Act - 多次构建和销毁
          for (int i = 0; i < 3; i++) {
            await tester.pumpWidget(createWidgetUnderTest());
            await tester.pump(const Duration(milliseconds: 50));
            await tester.pumpWidget(const SizedBox());
          }

          // Assert - 重新构建应该成功
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 50));
          expect(find.byType(MyPage), findsOneWidget);
        });
      });

      group('0.3 核心元素定位测试', () {
        testWidgets('返回按钮应该可以通过 Key 定位', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 50));

          // Assert
          expect(find.byKey(const Key('mypage_back_button')), findsOneWidget);
        });

        testWidgets('用户信息区域应该可以通过 Key 定位', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 50));

          // Assert
          expect(find.byKey(const Key('mypage_user_info')), findsOneWidget);
        });

        testWidgets('账户设置按钮应该可以通过 Key 定位', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 50));

          // Assert
          expect(find.byKey(const Key('mypage_account_button')), findsOneWidget);
        });

        testWidgets('RFID 设置按钮应该可以通过 Key 定位', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 50));

          // Assert
          expect(find.byKey(const Key('mypage_rfid_button')), findsOneWidget);
        });

        testWidgets('数量扫描开关应该可以通过 Key 定位', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 50));

          // Assert
          expect(find.byKey(const Key('mypage_quantity_switch')), findsOneWidget);
        });

        testWidgets('登出按钮应该可以通过 Key 定位', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle(); // 等待所有动画和响应式 UI 完成

          // 滚动到底部来找到登出按钮
          await tester.drag(find.byType(ListView), const Offset(0, -300));
          await tester.pumpAndSettle();

          // Assert
          expect(find.byKey(const Key('mypage_logout_button')), findsOneWidget);
        });

        testWidgets('所有基础 Key 元素应该都能定位到', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle(); // 等待所有响应式 UI 完成

          // Assert - 验证顶部 Key 元素
          final topKeyElements = [
            'mypage_back_button',
            'mypage_user_info',
            'mypage_account_button',
            'mypage_rfid_button',
            'mypage_scan_audio_button',
            'mypage_quantity_switch',
            'mypage_font_size_button',
          ];

          for (final key in topKeyElements) {
            expect(find.byKey(Key(key)), findsOneWidget, reason: 'Key "$key" 应该能被找到');
          }

          // 滚动到底部验证底部元素
          await tester.drag(find.byType(ListView), const Offset(0, -300));
          await tester.pumpAndSettle();

          expect(
            find.byKey(const Key('mypage_logout_button')),
            findsOneWidget,
            reason: 'Key "mypage_logout_button" 应该能被找到',
          );
        });

        testWidgets('条件渲染元素的 Key 应该根据条件显示', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle(); // 等待所有 Obx 响应式组件完成渲染

          // Assert - 根据测试数据的条件验证
          // isBiometrics = true，所以生物识别开关应该显示
          expect(find.byKey(const Key('mypage_biometrics_switch')), findsOneWidget);

          // 滚动到中间部分查找 FAQ 按钮
          await tester.drag(find.byType(ListView), const Offset(0, -150));
          await tester.pumpAndSettle();

          // isShowSupportFaq = true，所以 FAQ 按钮应该显示
          expect(find.byKey(const Key('mypage_faq_button')), findsOneWidget);
        });

        testWidgets('不应该显示的条件元素应该找不到', (tester) async {
          // Arrange - 设置不显示试用期的用户数据
          final normalUserModel = MyPageUIModel(
            userName: 'normaluser',
            firstName: '通常',
            lastName: 'ユーザー',
            tenantName: 'テストテナント',
            lastLoginTime: '2024-01-01 12:00',
            appVersion: '1.0.0',
            isQuantityCountScan: true,
            isBiometrics: false, // 不显示生物识别
            isUserBiometricsOpenSwitch: false,
            anyNew: false,
            isShowSupportFaq: false, // 不显示 FAQ
            contractPeriodEndDate: '', // 不是试用用户
            plan: '1', // 正式用户，不是试用模式
          );

          when(mockController.user).thenReturn(normalUserModel.obs);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle(); // 等待所有响应式 UI 完成

          // Assert - 条件不满足的元素不应该显示
          expect(find.byKey(const Key('mypage_biometrics_switch')), findsNothing);
          expect(find.byKey(const Key('mypage_faq_button')), findsNothing);
          expect(find.byKey(const Key('mypage_trial_period_item')), findsNothing);
        });
      });
    });

    // ========================================
    // Phase 1: UI 渲染和状态测试
    // ========================================
    group('🎯 Phase 1: UI 渲染和状态测试', () {
      group('1.1 用户数据渲染测试', () {
        testWidgets('应该正确显示用户基本信息', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证用户名显示
          expect(find.text('ユーザー テスト 様'), findsOneWidget);

          // Assert - 验证租户信息
          expect(find.text('テナント：テストテナント'), findsOneWidget);

          // Assert - 验证最后登录时间
          expect(find.text('最終ログイン: 2024-01-01 12:00'), findsOneWidget);
        });

        testWidgets('应该正确显示版本信息', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 滚动到版本信息区域
          await tester.drag(find.byType(ListView), const Offset(0, -200));
          await tester.pumpAndSettle();

          // Assert - 验证版本号显示
          expect(find.text('1.0.0'), findsOneWidget);
        });

        testWidgets('应该正确显示试用期信息', (tester) async {
          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 滚动到底部查看试用期信息
          await tester.drag(find.byType(ListView), const Offset(0, -300));
          await tester.pumpAndSettle();

          // Assert - 验证试用期提示显示
          expect(find.text('2024-12-31にトライアル期間が終了します。'), findsOneWidget);
        });

        testWidgets('不同用户数据应该正确显示', (tester) async {
          // Arrange - 创建不同的用户数据
          final differentUserModel = MyPageUIModel(
            userName: 'admin',
            firstName: '管理者',
            lastName: '佐藤',
            tenantName: '本社システム',
            lastLoginTime: '2024-02-15 09:30',
            appVersion: '2.1.0',
            isQuantityCountScan: false,
            isBiometrics: false,
            isUserBiometricsOpenSwitch: false,
            anyNew: false,
            isShowSupportFaq: false,
            contractPeriodEndDate: '',
            plan: '1', // 正式版用户
          );

          when(mockController.user).thenReturn(differentUserModel.obs);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证不同用户信息正确显示
          expect(find.text('佐藤 管理者 様'), findsOneWidget);
          expect(find.text('テナント：本社システム'), findsOneWidget);
          expect(find.text('最終ログイン: 2024-02-15 09:30'), findsOneWidget);

          // 滚动查看版本信息
          await tester.drag(find.byType(ListView), const Offset(0, -200));
          await tester.pumpAndSettle();
          expect(find.text('2.1.0'), findsOneWidget);
        });
      });

      group('1.2 响应式状态变化测试', () {
        testWidgets('用户数据变化应该立即反映在 UI 上', (tester) async {
          // Arrange - 初始用户数据
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证初始状态
          expect(find.text('ユーザー テスト 様'), findsOneWidget);

          // Act - 创建新的用户数据并重新设置 Mock
          final updatedUserModel = MyPageUIModel(
            userName: 'updated',
            firstName: '更新後',
            lastName: 'ユーザー',
            tenantName: '新しいテナント',
            lastLoginTime: '2024-03-01 15:45',
            appVersion: '1.0.0',
            isQuantityCountScan: true,
            isBiometrics: true,
            isUserBiometricsOpenSwitch: false,
            anyNew: true,
            isShowSupportFaq: true,
            contractPeriodEndDate: '2024-12-31',
            plan: '6',
          );

          // 重新创建 Mock Controller 和 Widget
          reset(mockController);
          setupMockController();
          when(mockController.user).thenReturn(updatedUserModel.obs);
          Get.reset();
          Get.put<MyPageController>(mockController);

          // 重新构建 Widget
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证 UI 已更新
          expect(find.text('ユーザー 更新後 様'), findsOneWidget);
          expect(find.text('テナント：新しいテナント'), findsOneWidget);
          expect(find.text('最終ログイン: 2024-03-01 15:45'), findsOneWidget);
        });

        testWidgets('加载状态变化应该正确反映', (tester) async {
          // Arrange - 设置加载状态
          setupMockController();
          when(mockController.isLoading).thenReturn(true.obs);
          Get.reset();
          Get.put<MyPageController>(mockController);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 100));

          // Assert - 验证加载状态显示
          expect(find.byType(CircularProgressIndicator), findsOneWidget);
          expect(find.text('    '), findsOneWidget); // 加载时显示空白

          // Act - 切换到非加载状态 (重新创建 Widget)
          reset(mockController);
          setupMockController();
          when(mockController.isLoading).thenReturn(false.obs);
          Get.reset();
          Get.put<MyPageController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 100));

          // Assert - 验证正常内容显示
          expect(find.byType(CircularProgressIndicator), findsNothing);
          expect(find.text('ユーザー テスト 様'), findsOneWidget);
        });

        testWidgets('开关状态变化应该立即反映', (tester) async {
          // Arrange - 初始状态 (isQuantityCountScan = true)
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 找到数量扫描开关 (key 直接在 Switch 上)
          final quantitySwitch = find.byKey(const Key('mypage_quantity_switch'));
          expect(quantitySwitch, findsOneWidget);

          // 验证初始开关状态 (isQuantityCountScan = true)
          final switchWidget = tester.widget<Switch>(quantitySwitch);
          expect(switchWidget.value, isTrue);

          // Act - 创建新的用户数据并重新构建
          final updatedUserModel = MyPageUIModel(
            userName: 'testuser',
            firstName: 'テスト',
            lastName: 'ユーザー',
            tenantName: 'テストテナント',
            lastLoginTime: '2024-01-01 12:00',
            appVersion: '1.0.0',
            isQuantityCountScan: false, // 改变开关状态
            isBiometrics: true,
            isUserBiometricsOpenSwitch: false,
            anyNew: true,
            isShowSupportFaq: true,
            contractPeriodEndDate: '2024-12-31',
            plan: '6',
          );

          reset(mockController);
          setupMockController();
          when(mockController.user).thenReturn(updatedUserModel.obs);
          Get.reset();
          Get.put<MyPageController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 验证开关状态已更新
          final updatedQuantitySwitch = find.byKey(const Key('mypage_quantity_switch'));
          expect(updatedQuantitySwitch, findsOneWidget);

          final updatedSwitchWidget = tester.widget<Switch>(updatedQuantitySwitch);
          expect(updatedSwitchWidget.value, isFalse);
        });
      });

      group('1.3 条件 UI 渲染测试', () {
        testWidgets('生物识别开关应该根据 isBiometrics 条件显示', (tester) async {
          // Test 1: isBiometrics = true 的用户应该显示开关
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 生物识别开关应该显示
          expect(find.byKey(const Key('mypage_biometrics_switch')), findsOneWidget);

          // Test 2: isBiometrics = false 的用户应该隐藏开关
          final userWithoutBiometrics = MyPageUIModel(
            userName: 'testuser',
            firstName: 'テスト',
            lastName: 'ユーザー',
            tenantName: 'テストテナント',
            lastLoginTime: '2024-01-01 12:00',
            appVersion: '1.0.0',
            isQuantityCountScan: true,
            isBiometrics: false, // 禁用生物识别
            isUserBiometricsOpenSwitch: false,
            anyNew: true,
            isShowSupportFaq: true,
            contractPeriodEndDate: '2024-12-31',
            plan: '6',
          );

          reset(mockController);
          setupMockController();
          when(mockController.user).thenReturn(userWithoutBiometrics.obs);
          Get.reset();
          Get.put<MyPageController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 生物识别开关应该隐藏
          expect(find.byKey(const Key('mypage_biometrics_switch')), findsNothing);
        });

        testWidgets('FAQ 按钮应该根据 isShowSupportFaq 条件显示', (tester) async {
          // Test 1: isShowSupportFaq = true 的用户应该显示 FAQ 按钮
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 滚动找到 FAQ 按钮
          await tester.drag(find.byType(ListView), const Offset(0, -150));
          await tester.pumpAndSettle();

          // Assert - FAQ 按钮应该显示
          expect(find.byKey(const Key('mypage_faq_button')), findsOneWidget);

          // Test 2: isShowSupportFaq = false 的用户应该隐藏 FAQ 按钮
          final userWithoutFaq = MyPageUIModel(
            userName: 'testuser',
            firstName: 'テスト',
            lastName: 'ユーザー',
            tenantName: 'テストテナント',
            lastLoginTime: '2024-01-01 12:00',
            appVersion: '1.0.0',
            isQuantityCountScan: true,
            isBiometrics: true,
            isUserBiometricsOpenSwitch: false,
            anyNew: true,
            isShowSupportFaq: false, // 禁用 FAQ
            contractPeriodEndDate: '2024-12-31',
            plan: '6',
          );

          reset(mockController);
          setupMockController();
          when(mockController.user).thenReturn(userWithoutFaq.obs);
          Get.reset();
          Get.put<MyPageController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 滚动到相同位置
          await tester.drag(find.byType(ListView), const Offset(0, -150));
          await tester.pumpAndSettle();

          // Assert - FAQ 按钮应该隐藏
          expect(find.byKey(const Key('mypage_faq_button')), findsNothing);
        });

        testWidgets('试用期信息应该根据 isTrial() 条件显示', (tester) async {
          // Test 1: 试用用户 (plan = '6') 应该显示试用期信息
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 滚动到底部
          await tester.drag(find.byType(ListView), const Offset(0, -300));
          await tester.pumpAndSettle();

          // Assert - 试用期信息应该显示
          expect(find.byKey(const Key('mypage_trial_period_item')), findsOneWidget);

          // Test 2: 正式用户 (plan = '1') 应该隐藏试用期信息
          final officialUser = MyPageUIModel(
            userName: 'testuser',
            firstName: 'テスト',
            lastName: 'ユーザー',
            tenantName: 'テストテナント',
            lastLoginTime: '2024-01-01 12:00',
            appVersion: '1.0.0',
            isQuantityCountScan: true,
            isBiometrics: true,
            isUserBiometricsOpenSwitch: false,
            anyNew: true,
            isShowSupportFaq: true,
            contractPeriodEndDate: '',
            plan: '1', // 正式用户
          );

          reset(mockController);
          setupMockController();
          when(mockController.user).thenReturn(officialUser.obs);
          Get.reset();
          Get.put<MyPageController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 滚动到相同位置
          await tester.drag(find.byType(ListView), const Offset(0, -300));
          await tester.pumpAndSettle();

          // Assert - 试用期信息应该隐藏
          expect(find.byKey(const Key('mypage_trial_period_item')), findsNothing);
        });

        testWidgets('消息红点应该根据 anyNew 条件显示', (tester) async {
          // Test 1: anyNew = true 的用户应该显示红点
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 滚动到消息区域
          await tester.drag(find.byType(ListView), const Offset(0, -150));
          await tester.pumpAndSettle();

          // Assert - 应该找到消息按钮
          expect(find.byKey(const Key('mypage_message_button')), findsOneWidget);

          // 验证红点显示 (anyNew = true)
          final redDotFinder = find.byWidgetPredicate(
            (widget) =>
                widget is Container &&
                widget.decoration is BoxDecoration &&
                (widget.decoration as BoxDecoration).color == Colors.red &&
                (widget.decoration as BoxDecoration).shape == BoxShape.circle,
          );
          expect(redDotFinder, findsOneWidget);

          // Test 2: anyNew = false 的用户应该隐藏红点
          final userWithoutNewMessage = MyPageUIModel(
            userName: 'testuser',
            firstName: 'テスト',
            lastName: 'ユーザー',
            tenantName: 'テストテナント',
            lastLoginTime: '2024-01-01 12:00',
            appVersion: '1.0.0',
            isQuantityCountScan: true,
            isBiometrics: true,
            isUserBiometricsOpenSwitch: false,
            anyNew: false, // 没有新消息
            isShowSupportFaq: true,
            contractPeriodEndDate: '2024-12-31',
            plan: '6',
          );

          reset(mockController);
          setupMockController();
          when(mockController.user).thenReturn(userWithoutNewMessage.obs);
          Get.reset();
          Get.put<MyPageController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 滚动到相同位置
          await tester.drag(find.byType(ListView), const Offset(0, -150));
          await tester.pumpAndSettle();

          // Assert - 红点应该隐藏
          expect(redDotFinder, findsNothing);
        });
      });

      group('1.4 加载状态测试', () {
        testWidgets('加载状态下应该显示进度指示器并隐藏用户名', (tester) async {
          // Arrange - 设置加载状态
          setupMockController();
          when(mockController.isLoading).thenReturn(true.obs);
          Get.reset();
          Get.put<MyPageController>(mockController);

          // Act
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 100));

          // Assert - 验证加载指示器显示
          expect(find.byType(CircularProgressIndicator), findsOneWidget);

          // 验证用户名被占位符替换
          expect(find.text('    '), findsOneWidget);
          expect(find.text('ユーザー テスト 様'), findsNothing);

          // 验证其他信息仍然显示
          expect(find.text('テナント：テストテナント'), findsOneWidget);
          expect(find.text('最終ログイン: 2024-01-01 12:00'), findsOneWidget);
        });

        testWidgets('从加载状态切换到正常状态应该正确显示', (tester) async {
          // Arrange - 初始加载状态
          setupMockController();
          when(mockController.isLoading).thenReturn(true.obs);
          Get.reset();
          Get.put<MyPageController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 100));

          // 验证加载状态
          expect(find.byType(CircularProgressIndicator), findsOneWidget);
          expect(find.text('    '), findsOneWidget);

          // Act - 切换到非加载状态 (重新创建 Widget)
          reset(mockController);
          setupMockController();
          when(mockController.isLoading).thenReturn(false.obs);
          Get.reset();
          Get.put<MyPageController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pump(const Duration(milliseconds: 100));

          // Assert - 验证正常状态
          expect(find.byType(CircularProgressIndicator), findsNothing);
          expect(find.text('ユーザー テスト 様'), findsOneWidget);
          expect(find.text('    '), findsNothing);
        });

        testWidgets('加载状态切换应该保持其他 UI 元素稳定', (tester) async {
          // Arrange - 正常状态
          when(mockController.isLoading).thenReturn(false.obs);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 验证基础 UI 元素存在
          expect(find.byType(AppBar), findsOneWidget);
          expect(find.byType(ListView), findsOneWidget);
          expect(find.byKey(const Key('mypage_user_info')), findsOneWidget);

          // Act - 切换到加载状态
          when(mockController.isLoading).thenReturn(true.obs);
          await tester.pump();
          await tester.pumpAndSettle();

          // Assert - 基础 UI 元素仍然存在
          expect(find.byType(AppBar), findsOneWidget);
          expect(find.byType(ListView), findsOneWidget);
          expect(find.byKey(const Key('mypage_user_info')), findsOneWidget);

          // Act - 切换回正常状态
          when(mockController.isLoading).thenReturn(false.obs);
          await tester.pump();
          await tester.pumpAndSettle();

          // Assert - UI 结构保持稳定
          expect(find.byType(AppBar), findsOneWidget);
          expect(find.byType(ListView), findsOneWidget);
          expect(find.byKey(const Key('mypage_user_info')), findsOneWidget);
        });
      });
    });

    // ========================================
    // Phase 2: 用户交互测试
    // ========================================
    group('🎯 Phase 2: 用户交互测试', () {
      group('2.1 按钮点击交互测试', () {
        testWidgets('返回按钮点击应该调用 controller.goBack()', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 点击返回按钮
          final backButton = find.byKey(const Key('mypage_back_button'));
          expect(backButton, findsOneWidget);

          await tester.tap(backButton);
          await tester.pump();

          // Assert - 验证 goBack 方法被调用
          verify(mockController.goBack()).called(1);
        });

        testWidgets('账户按钮点击应该调用 controller.toAccountPage()', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 点击账户按钮
          final accountButton = find.byKey(const Key('mypage_account_button'));
          expect(accountButton, findsOneWidget);

          await tester.tap(accountButton);
          await tester.pump();

          // Assert - 验证 toAccountPage 方法被调用
          verify(mockController.toAccountPage()).called(1);
        });

        testWidgets('RFID 设置按钮点击应该调用 controller.toRFIDSettingPage()', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 点击 RFID 设置按钮
          final rfidButton = find.byKey(const Key('mypage_rfid_button'));
          expect(rfidButton, findsOneWidget);

          await tester.tap(rfidButton);
          await tester.pump();

          // Assert - 验证 toRFIDSettingPage 方法被调用
          verify(mockController.toRFIDSettingPage()).called(1);
        });

        testWidgets('扫描音设置按钮点击应该调用 controller.toScanAudioSetting()', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 点击扫描音设置按钮
          final scanAudioButton = find.byKey(const Key('mypage_scan_audio_button'));
          expect(scanAudioButton, findsOneWidget);

          await tester.tap(scanAudioButton);
          await tester.pump();

          // Assert - 验证 toScanAudioSetting 方法被调用
          verify(mockController.toScanAudioSetting()).called(1);
        });

        testWidgets('字体大小设置按钮点击应该调用 controller.toFontSizeSetting()', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 点击字体大小设置按钮
          final fontSizeButton = find.byKey(const Key('mypage_font_size_button'));
          expect(fontSizeButton, findsOneWidget);

          await tester.tap(fontSizeButton);
          await tester.pump();

          // Assert - 验证 toFontSizeSetting 方法被调用
          verify(mockController.toFontSizeSetting()).called(1);
        });

        testWidgets('消息按钮点击应该调用 controller.toMessagePage()', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 滚动到消息按钮
          await tester.drag(find.byType(ListView), const Offset(0, -150));
          await tester.pumpAndSettle();

          // Act - 点击消息按钮
          final messageButton = find.byKey(const Key('mypage_message_button'));
          expect(messageButton, findsOneWidget);

          await tester.tap(messageButton);
          await tester.pump();

          // Assert - 验证 toMessagePage 方法被调用
          verify(mockController.toMessagePage()).called(1);
        });

        testWidgets('登出按钮点击应该调用 controller.logout()', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 滚动到登出按钮
          await tester.drag(find.byType(ListView), const Offset(0, -400));
          await tester.pumpAndSettle();

          // Act - 点击登出按钮
          final logoutButton = find.byKey(const Key('mypage_logout_button'));
          expect(logoutButton, findsOneWidget);

          // 使用 scrollable 确保元素在可点击区域内
          await tester.ensureVisible(logoutButton);
          await tester.pumpAndSettle();

          await tester.tap(logoutButton, warnIfMissed: false);
          await tester.pump();

          // Assert - 验证 logout 方法被调用
          verify(mockController.logout()).called(1);
        });

        testWidgets('用户指导按钮点击应该调用相应方法', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 滚动到指导按钮区域
          await tester.drag(find.byType(ListView), const Offset(0, -150));
          await tester.pumpAndSettle();

          // Act & Assert - 功能全般を知る
          final userGuideButton = find.byKey(const Key('mypage_user_guide_button'));
          expect(userGuideButton, findsOneWidget);
          await tester.tap(userGuideButton);
          await tester.pump();
          verify(mockController.toUserGuide()).called(1);

          // Act & Assert - 設定・操作手順を知る
          final starterGuideButton = find.byKey(const Key('mypage_starter_guide_button'));
          expect(starterGuideButton, findsOneWidget);
          await tester.tap(starterGuideButton);
          await tester.pump();
          verify(mockController.toStarterGuide()).called(1);

          // Act & Assert - トライアルの方はこちら
          final tutorialButton = find.byKey(const Key('mypage_tutorial_button'));
          expect(tutorialButton, findsOneWidget);
          await tester.tap(tutorialButton);
          await tester.pump();
          verify(mockController.toTutorial()).called(1);

          // Act & Assert - よくあるご質問 (如果显示)
          final faqButton = find.byKey(const Key('mypage_faq_button'));
          if (faqButton.evaluate().isNotEmpty) {
            // 确保 FAQ 按钮在可点击区域内
            await tester.ensureVisible(faqButton);
            await tester.pumpAndSettle();

            await tester.tap(faqButton, warnIfMissed: false);
            await tester.pump();
            verify(mockController.openSupportFaq()).called(1);
          }
        });
      });

      group('2.2 开关切换交互测试', () {
        testWidgets('数量扫描开关切换应该调用 controller.quantityCountScanChange()', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 点击数量扫描开关
          final quantitySwitch = find.byKey(const Key('mypage_quantity_switch'));
          expect(quantitySwitch, findsOneWidget);

          await tester.tap(quantitySwitch);
          await tester.pump();

          // Assert - 验证 quantityCountScanChange 方法被调用
          verify(mockController.quantityCountScanChange(any)).called(1);
        });

        testWidgets('生物识别开关切换应该调用 controller.biometricsChange()', (tester) async {
          // Arrange - 确保生物识别开关显示
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 点击生物识别开关
          final biometricsSwitch = find.byKey(const Key('mypage_biometrics_switch'));
          expect(biometricsSwitch, findsOneWidget);

          await tester.tap(biometricsSwitch);
          await tester.pump();

          // Assert - 验证 biometricsChange 方法被调用
          verify(mockController.biometricsChange(any)).called(1);
        });

        testWidgets('开关状态变化应该传递正确的值', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 获取当前开关状态
          final quantitySwitch = find.byKey(const Key('mypage_quantity_switch'));
          final currentSwitchWidget = tester.widget<Switch>(quantitySwitch);
          final currentValue = currentSwitchWidget.value;

          // Act - 切换开关
          await tester.tap(quantitySwitch);
          await tester.pump();

          // Assert - 验证传递的值是相反的
          verify(mockController.quantityCountScanChange(!currentValue)).called(1);
        });

        testWidgets('开关在禁用状态下不应该响应点击', (tester) async {
          // Arrange - 创建禁用生物识别的用户
          final userWithoutBiometrics = MyPageUIModel(
            userName: 'testuser',
            firstName: 'テスト',
            lastName: 'ユーザー',
            tenantName: 'テストテナント',
            lastLoginTime: '2024-01-01 12:00',
            appVersion: '1.0.0',
            isQuantityCountScan: true,
            isBiometrics: false, // 禁用生物识别
            isUserBiometricsOpenSwitch: false,
            anyNew: true,
            isShowSupportFaq: true,
            contractPeriodEndDate: '2024-12-31',
            plan: '6',
          );

          reset(mockController);
          setupMockController();
          when(mockController.user).thenReturn(userWithoutBiometrics.obs);
          Get.reset();
          Get.put<MyPageController>(mockController);

          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Assert - 生物识别开关不应该存在
          expect(find.byKey(const Key('mypage_biometrics_switch')), findsNothing);

          // 数量扫描开关仍然应该可用
          final quantitySwitch = find.byKey(const Key('mypage_quantity_switch'));
          expect(quantitySwitch, findsOneWidget);

          await tester.tap(quantitySwitch);
          await tester.pump();

          verify(mockController.quantityCountScanChange(any)).called(1);
        });
      });

      group('2.3 导航交互测试', () {
        testWidgets('导航方法应该被正确调用', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 测试导航到扫描音设置
          final scanAudioButton = find.byKey(const Key('mypage_scan_audio_button'));
          await tester.tap(scanAudioButton);
          await tester.pump();
          verify(mockController.toScanAudioSetting()).called(1);

          // 测试导航到字体大小设置
          final fontSizeButton = find.byKey(const Key('mypage_font_size_button'));
          await tester.tap(fontSizeButton);
          await tester.pump();
          verify(mockController.toFontSizeSetting()).called(1);

          // 滚动到消息区域测试导航
          await tester.drag(find.byType(ListView), const Offset(0, -150));
          await tester.pumpAndSettle();

          final messageButton = find.byKey(const Key('mypage_message_button'));
          await tester.tap(messageButton);
          await tester.pump();
          verify(mockController.toMessagePage()).called(1);
        });

        testWidgets('返回导航应该正确工作', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 点击返回按钮
          final backButton = find.byKey(const Key('mypage_back_button'));
          await tester.tap(backButton);
          await tester.pump();

          // Assert - 验证导航方法被调用
          verify(mockController.goBack()).called(1);
        });

        testWidgets('外部链接导航应该正确处理', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 滚动到指导区域
          await tester.drag(find.byType(ListView), const Offset(0, -150));
          await tester.pumpAndSettle();

          // Act - 点击用户指导按钮
          final userGuideButton = find.byKey(const Key('mypage_user_guide_button'));
          await tester.tap(userGuideButton);
          await tester.pump();

          // Assert - 验证外部链接方法被调用
          verify(mockController.toUserGuide()).called(1);
        });
      });

      group('2.4 手势交互测试', () {
        testWidgets('ListView 滚动应该正常工作', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // 记录初始位置的元素可见性
          final initialVisibleAccount = find.byKey(const Key('mypage_account_button'));
          expect(initialVisibleAccount, findsOneWidget);

          final initialInvisibleLogout = find.byKey(const Key('mypage_logout_button'));
          expect(initialInvisibleLogout, findsNothing);

          // Act - 向上滚动
          await tester.drag(find.byType(ListView), const Offset(0, -300));
          await tester.pumpAndSettle();

          // Assert - 验证滚动后的可见性变化
          final scrolledVisibleLogout = find.byKey(const Key('mypage_logout_button'));
          expect(scrolledVisibleLogout, findsOneWidget);
        });

        testWidgets('滚动应该不影响用户交互', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 滚动后点击按钮
          await tester.drag(find.byType(ListView), const Offset(0, -400));
          await tester.pumpAndSettle();

          final logoutButton = find.byKey(const Key('mypage_logout_button'));
          expect(logoutButton, findsOneWidget);

          // 确保元素在可点击区域内
          await tester.ensureVisible(logoutButton);
          await tester.pumpAndSettle();

          await tester.tap(logoutButton, warnIfMissed: false);
          await tester.pump();

          // Assert - 验证滚动后的交互仍然有效
          verify(mockController.logout()).called(1);
        });

        testWidgets('快速滚动应该稳定处理', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 快速多次滚动
          await tester.fling(find.byType(ListView), const Offset(0, -500), 1000);
          await tester.pumpAndSettle();

          await tester.fling(find.byType(ListView), const Offset(0, 500), 1000);
          await tester.pumpAndSettle();

          // Assert - 应用应该仍然稳定，基础元素可访问
          expect(find.byType(MyPage), findsOneWidget);
          expect(find.byType(ListView), findsOneWidget);
          expect(find.byType(AppBar), findsOneWidget);
        });

        testWidgets('滚动边界应该正确处理', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 尝试滚动超出边界
          await tester.drag(find.byType(ListView), const Offset(0, 1000)); // 向下滚动超出顶部
          await tester.pumpAndSettle();

          await tester.drag(find.byType(ListView), const Offset(0, -2000)); // 向上滚动超出底部
          await tester.pumpAndSettle();

          // Assert - 应用应该处理边界情况而不崩溃
          expect(find.byType(MyPage), findsOneWidget);
          expect(find.byType(ListView), findsOneWidget);
        });

        testWidgets('触摸反馈应该正常工作', (tester) async {
          // Arrange
          await tester.pumpWidget(createWidgetUnderTest());
          await tester.pumpAndSettle();

          // Act - 测试点击反馈
          final accountButton = find.byKey(const Key('mypage_account_button'));
          expect(accountButton, findsOneWidget);

          // 点击按钮
          await tester.tap(accountButton);
          await tester.pump(const Duration(milliseconds: 100));

          // Assert - 验证点击事件被正确处理
          verify(mockController.toAccountPage()).called(1);

          // 验证按钮可以重复点击
          clearInteractions(mockController);
          await tester.tap(accountButton);
          await tester.pump(const Duration(milliseconds: 100));

          // Assert - 验证第二次点击也被正确处理
          verify(mockController.toAccountPage()).called(1);
        });
      });
    });
  });
}

// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/mypage/presentation/pages/my_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i14;
import 'dart:ui' as _i17;

import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart' as _i10;
import 'package:asset_force_mobile_v2/core/platform/method_channel.dart'
    as _i11;
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i15;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i8;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i9;
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i6;
import 'package:asset_force_mobile_v2/core/utils/biometrics_switch_util.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/me/mypage/domain/usecase/logout_usercase.dart'
    as _i4;
import 'package:asset_force_mobile_v2/features/me/mypage/domain/usecase/mypage_usercase.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/controllers/biometrics_switch_controller.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/controllers/mypage_controller.dart'
    as _i12;
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/models/mypage_ui_model.dart'
    as _i13;
import 'package:get/get.dart' as _i2;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i16;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeRx_0<T> extends _i1.SmartFake implements _i2.Rx<T> {
  _FakeRx_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMyPageLoadDataCase_1 extends _i1.SmartFake
    implements _i3.MyPageLoadDataCase {
  _FakeMyPageLoadDataCase_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLogoutUseCase_2 extends _i1.SmartFake implements _i4.LogoutUseCase {
  _FakeLogoutUseCase_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBiometricsSwitchUtil_3 extends _i1.SmartFake
    implements _i5.BiometricsSwitchUtil {
  _FakeBiometricsSwitchUtil_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIStorageUtils_4 extends _i1.SmartFake implements _i6.IStorageUtils {
  _FakeIStorageUtils_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBiometricsSwitchController_5 extends _i1.SmartFake
    implements _i7.BiometricsSwitchController {
  _FakeBiometricsSwitchController_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDialogService_6 extends _i1.SmartFake implements _i8.DialogService {
  _FakeDialogService_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_7 extends _i1.SmartFake
    implements _i9.NavigationService {
  _FakeNavigationService_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIEnvHelper_8 extends _i1.SmartFake implements _i10.IEnvHelper {
  _FakeIEnvHelper_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePlatformChannel_9 extends _i1.SmartFake
    implements _i11.PlatformChannel {
  _FakePlatformChannel_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxBool_10 extends _i1.SmartFake implements _i2.RxBool {
  _FakeRxBool_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_11<T> extends _i1.SmartFake
    implements _i2.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [MyPageController].
///
/// See the documentation for Mockito's code generation for more information.
class MockMyPageController extends _i1.Mock implements _i12.MyPageController {
  @override
  _i2.Rx<_i13.MyPageUIModel> get user =>
      (super.noSuchMethod(
            Invocation.getter(#user),
            returnValue: _FakeRx_0<_i13.MyPageUIModel>(
              this,
              Invocation.getter(#user),
            ),
            returnValueForMissingStub: _FakeRx_0<_i13.MyPageUIModel>(
              this,
              Invocation.getter(#user),
            ),
          )
          as _i2.Rx<_i13.MyPageUIModel>);

  @override
  set user(_i2.Rx<_i13.MyPageUIModel>? _user) => super.noSuchMethod(
    Invocation.setter(#user, _user),
    returnValueForMissingStub: null,
  );

  @override
  _i3.MyPageLoadDataCase get meLoadDataCase =>
      (super.noSuchMethod(
            Invocation.getter(#meLoadDataCase),
            returnValue: _FakeMyPageLoadDataCase_1(
              this,
              Invocation.getter(#meLoadDataCase),
            ),
            returnValueForMissingStub: _FakeMyPageLoadDataCase_1(
              this,
              Invocation.getter(#meLoadDataCase),
            ),
          )
          as _i3.MyPageLoadDataCase);

  @override
  _i4.LogoutUseCase get logoutUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#logoutUseCase),
            returnValue: _FakeLogoutUseCase_2(
              this,
              Invocation.getter(#logoutUseCase),
            ),
            returnValueForMissingStub: _FakeLogoutUseCase_2(
              this,
              Invocation.getter(#logoutUseCase),
            ),
          )
          as _i4.LogoutUseCase);

  @override
  _i5.BiometricsSwitchUtil get biometricsSwitchUtil =>
      (super.noSuchMethod(
            Invocation.getter(#biometricsSwitchUtil),
            returnValue: _FakeBiometricsSwitchUtil_3(
              this,
              Invocation.getter(#biometricsSwitchUtil),
            ),
            returnValueForMissingStub: _FakeBiometricsSwitchUtil_3(
              this,
              Invocation.getter(#biometricsSwitchUtil),
            ),
          )
          as _i5.BiometricsSwitchUtil);

  @override
  _i6.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_4(
              this,
              Invocation.getter(#storageUtils),
            ),
            returnValueForMissingStub: _FakeIStorageUtils_4(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i6.IStorageUtils);

  @override
  _i7.BiometricsSwitchController get biometricsSwitchController =>
      (super.noSuchMethod(
            Invocation.getter(#biometricsSwitchController),
            returnValue: _FakeBiometricsSwitchController_5(
              this,
              Invocation.getter(#biometricsSwitchController),
            ),
            returnValueForMissingStub: _FakeBiometricsSwitchController_5(
              this,
              Invocation.getter(#biometricsSwitchController),
            ),
          )
          as _i7.BiometricsSwitchController);

  @override
  _i8.DialogService get dialogService =>
      (super.noSuchMethod(
            Invocation.getter(#dialogService),
            returnValue: _FakeDialogService_6(
              this,
              Invocation.getter(#dialogService),
            ),
            returnValueForMissingStub: _FakeDialogService_6(
              this,
              Invocation.getter(#dialogService),
            ),
          )
          as _i8.DialogService);

  @override
  _i9.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_7(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_7(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i9.NavigationService);

  @override
  _i10.IEnvHelper get envHelper =>
      (super.noSuchMethod(
            Invocation.getter(#envHelper),
            returnValue: _FakeIEnvHelper_8(this, Invocation.getter(#envHelper)),
            returnValueForMissingStub: _FakeIEnvHelper_8(
              this,
              Invocation.getter(#envHelper),
            ),
          )
          as _i10.IEnvHelper);

  @override
  _i11.PlatformChannel get platformChannel =>
      (super.noSuchMethod(
            Invocation.getter(#platformChannel),
            returnValue: _FakePlatformChannel_9(
              this,
              Invocation.getter(#platformChannel),
            ),
            returnValueForMissingStub: _FakePlatformChannel_9(
              this,
              Invocation.getter(#platformChannel),
            ),
          )
          as _i11.PlatformChannel);

  @override
  _i2.RxBool get isLoading =>
      (super.noSuchMethod(
            Invocation.getter(#isLoading),
            returnValue: _FakeRxBool_10(this, Invocation.getter(#isLoading)),
            returnValueForMissingStub: _FakeRxBool_10(
              this,
              Invocation.getter(#isLoading),
            ),
          )
          as _i2.RxBool);

  @override
  set isLoading(_i2.RxBool? _isLoading) => super.noSuchMethod(
    Invocation.setter(#isLoading, _isLoading),
    returnValueForMissingStub: null,
  );

  @override
  _i2.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_11<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_11<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_11<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_11<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void biometricsChange(bool? value) => super.noSuchMethod(
    Invocation.method(#biometricsChange, [value]),
    returnValueForMissingStub: null,
  );

  @override
  _i14.Future<void> quantityCountScanChange(bool? value) =>
      (super.noSuchMethod(
            Invocation.method(#quantityCountScanChange, [value]),
            returnValue: _i14.Future<void>.value(),
            returnValueForMissingStub: _i14.Future<void>.value(),
          )
          as _i14.Future<void>);

  @override
  _i14.Future<void> logout() =>
      (super.noSuchMethod(
            Invocation.method(#logout, []),
            returnValue: _i14.Future<void>.value(),
            returnValueForMissingStub: _i14.Future<void>.value(),
          )
          as _i14.Future<void>);

  @override
  void toRFIDSettingPage() => super.noSuchMethod(
    Invocation.method(#toRFIDSettingPage, []),
    returnValueForMissingStub: null,
  );

  @override
  void goBack() => super.noSuchMethod(
    Invocation.method(#goBack, []),
    returnValueForMissingStub: null,
  );

  @override
  _i14.Future<void> toAccountPage() =>
      (super.noSuchMethod(
            Invocation.method(#toAccountPage, []),
            returnValue: _i14.Future<void>.value(),
            returnValueForMissingStub: _i14.Future<void>.value(),
          )
          as _i14.Future<void>);

  @override
  void toScanAudioSetting() => super.noSuchMethod(
    Invocation.method(#toScanAudioSetting, []),
    returnValueForMissingStub: null,
  );

  @override
  void toFontSizeSetting() => super.noSuchMethod(
    Invocation.method(#toFontSizeSetting, []),
    returnValueForMissingStub: null,
  );

  @override
  void toMessagePage() => super.noSuchMethod(
    Invocation.method(#toMessagePage, []),
    returnValueForMissingStub: null,
  );

  @override
  void toTutorial() => super.noSuchMethod(
    Invocation.method(#toTutorial, []),
    returnValueForMissingStub: null,
  );

  @override
  void toUserGuide() => super.noSuchMethod(
    Invocation.method(#toUserGuide, []),
    returnValueForMissingStub: null,
  );

  @override
  void openSupportFaq() => super.noSuchMethod(
    Invocation.method(#openSupportFaq, []),
    returnValueForMissingStub: null,
  );

  @override
  void toStarterGuide() => super.noSuchMethod(
    Invocation.method(#toStarterGuide, []),
    returnValueForMissingStub: null,
  );

  @override
  _i14.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i15.ErrorHandlingMode? mode = _i15.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i14.Future<void>.value(),
            returnValueForMissingStub: _i14.Future<void>.value(),
          )
          as _i14.Future<void>);

  @override
  _i14.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i14.Future<void>.value(),
            returnValueForMissingStub: _i14.Future<void>.value(),
          )
          as _i14.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i16.Disposer addListener(_i16.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i16.Disposer);

  @override
  void removeListener(_i17.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i17.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i16.Disposer addListenerId(Object? key, _i16.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i16.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}

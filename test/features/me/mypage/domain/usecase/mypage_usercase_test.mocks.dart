// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/mypage/domain/usecase/mypage_usercase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart' as _i15;
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i14;
import 'package:asset_force_mobile_v2/core/utils/biometrics_switch_util.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/data/model/un_permission_response_entity.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_user_tenant_model.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/tenant_info_response.dart'
    as _i9;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_my_account_model.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart'
    as _i10;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/user_role_response.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart'
    as _i4;
import 'package:local_auth/local_auth.dart' as _i12;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i13;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeSharedMyAccountModel_0 extends _i1.SmartFake
    implements _i2.SharedMyAccountModel {
  _FakeSharedMyAccountModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSharedUserTenantModel_1 extends _i1.SmartFake
    implements _i3.SharedUserTenantModel {
  _FakeSharedUserTenantModel_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [UserRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserRepository extends _i1.Mock implements _i4.UserRepository {
  MockUserRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.SharedMyAccountModel> getUserInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getUserInfo, []),
            returnValue: _i5.Future<_i2.SharedMyAccountModel>.value(
              _FakeSharedMyAccountModel_0(
                this,
                Invocation.method(#getUserInfo, []),
              ),
            ),
          )
          as _i5.Future<_i2.SharedMyAccountModel>);

  @override
  _i5.Future<_i3.SharedUserTenantModel> getUserTenant() =>
      (super.noSuchMethod(
            Invocation.method(#getUserTenant, []),
            returnValue: _i5.Future<_i3.SharedUserTenantModel>.value(
              _FakeSharedUserTenantModel_1(
                this,
                Invocation.method(#getUserTenant, []),
              ),
            ),
          )
          as _i5.Future<_i3.SharedUserTenantModel>);

  @override
  _i5.Future<bool> getAuthorityInfo(int? functionId) =>
      (super.noSuchMethod(
            Invocation.method(#getAuthorityInfo, [functionId]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<List<_i6.UserRoleResponseData>> getUserRole() =>
      (super.noSuchMethod(
            Invocation.method(#getUserRole, []),
            returnValue: _i5.Future<List<_i6.UserRoleResponseData>>.value(
              <_i6.UserRoleResponseData>[],
            ),
          )
          as _i5.Future<List<_i6.UserRoleResponseData>>);

  @override
  _i5.Future<bool> updateUserInfo(dynamic userInfo) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserInfo, [userInfo]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<List<_i7.SharedUserModel>> getUserList() =>
      (super.noSuchMethod(
            Invocation.method(#getUserList, []),
            returnValue: _i5.Future<List<_i7.SharedUserModel>>.value(
              <_i7.SharedUserModel>[],
            ),
          )
          as _i5.Future<List<_i7.SharedUserModel>>);

  @override
  _i5.Future<_i8.UnPermissionResponseEntity?> getUnPermission() =>
      (super.noSuchMethod(
            Invocation.method(#getUnPermission, []),
            returnValue: _i5.Future<_i8.UnPermissionResponseEntity?>.value(),
          )
          as _i5.Future<_i8.UnPermissionResponseEntity?>);

  @override
  _i5.Future<_i9.TenantInfoResponse?> getTenantInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getTenantInfo, []),
            returnValue: _i5.Future<_i9.TenantInfoResponse?>.value(),
          )
          as _i5.Future<_i9.TenantInfoResponse?>);

  @override
  _i5.Future<List<_i10.SharedRoleModel>> getGroupList(
    String? processDefinitionId,
    String? taskDefKey,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getGroupList, [processDefinitionId, taskDefKey]),
            returnValue: _i5.Future<List<_i10.SharedRoleModel>>.value(
              <_i10.SharedRoleModel>[],
            ),
          )
          as _i5.Future<List<_i10.SharedRoleModel>>);
}

/// A class which mocks [BiometricsSwitchUtil].
///
/// See the documentation for Mockito's code generation for more information.
class MockBiometricsSwitchUtil extends _i1.Mock
    implements _i11.BiometricsSwitchUtil {
  MockBiometricsSwitchUtil() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<String?> isAccountWorkingProperly({required String? password}) =>
      (super.noSuchMethod(
            Invocation.method(#isAccountWorkingProperly, [], {
              #password: password,
            }),
            returnValue: _i5.Future<String?>.value(),
          )
          as _i5.Future<String?>);

  @override
  _i5.Future<void> closeOrOpenBiometrics({required bool? isOpen}) =>
      (super.noSuchMethod(
            Invocation.method(#closeOrOpenBiometrics, [], {#isOpen: isOpen}),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  bool getUserBiometricsOpenSwitch() =>
      (super.noSuchMethod(
            Invocation.method(#getUserBiometricsOpenSwitch, []),
            returnValue: false,
          )
          as bool);

  @override
  _i5.Future<bool> isBiometricsAvailable() =>
      (super.noSuchMethod(
            Invocation.method(#isBiometricsAvailable, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<List<_i12.BiometricType>> getAvailableBiometrics() =>
      (super.noSuchMethod(
            Invocation.method(#getAvailableBiometrics, []),
            returnValue: _i5.Future<List<_i12.BiometricType>>.value(
              <_i12.BiometricType>[],
            ),
          )
          as _i5.Future<List<_i12.BiometricType>>);

  @override
  _i5.Future<void> authenticateBiometricsSetting() =>
      (super.noSuchMethod(
            Invocation.method(#authenticateBiometricsSetting, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setBiometricSecretPassword({
    required String? userName,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setBiometricSecretPassword, [], {
              #userName: userName,
              #password: password,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<String> getBiometricSecretPassword() =>
      (super.noSuchMethod(
            Invocation.method(#getBiometricSecretPassword, []),
            returnValue: _i5.Future<String>.value(
              _i13.dummyValue<String>(
                this,
                Invocation.method(#getBiometricSecretPassword, []),
              ),
            ),
          )
          as _i5.Future<String>);
}

/// A class which mocks [IStorageUtils].
///
/// See the documentation for Mockito's code generation for more information.
class MockIStorageUtils extends _i1.Mock implements _i14.IStorageUtils {
  MockIStorageUtils() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<void> init() =>
      (super.noSuchMethod(
            Invocation.method(#init, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> initPersonalStorage({required String? cryptedName}) =>
      (super.noSuchMethod(
            Invocation.method(#initPersonalStorage, [], {
              #cryptedName: cryptedName,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  T? getValue<T>(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#getValue, [key], {#personal: personal}),
          )
          as T?);

  @override
  _i5.Future<void> setValue<T>(
    String? key,
    T? value, {
    bool? personal = false,
    int? durationInSeconds,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #setValue,
              [key, value],
              {#personal: personal, #durationInSeconds: durationInSeconds},
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> removeValue(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#removeValue, [key], {#personal: personal}),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  bool containsKey(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#containsKey, [key], {#personal: personal}),
            returnValue: false,
          )
          as bool);

  @override
  _i5.Future<void> clearAll({bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#clearAll, [], {#personal: personal}),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  List<String> getAllKeys({bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#getAllKeys, [], {#personal: personal}),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  String getAssetScanLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getAssetScanLocation, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getAssetScanLocation, []),
            ),
          )
          as String);

  @override
  _i5.Future<void> setAssetScanLocation({required String? assetLocation}) =>
      (super.noSuchMethod(
            Invocation.method(#setAssetScanLocation, [], {
              #assetLocation: assetLocation,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [IEnvHelper].
///
/// See the documentation for Mockito's code generation for more information.
class MockIEnvHelper extends _i1.Mock implements _i15.IEnvHelper {
  MockIEnvHelper() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set environment(String? value) => super.noSuchMethod(
    Invocation.setter(#environment, value),
    returnValueForMissingStub: null,
  );

  @override
  bool isAndroid() =>
      (super.noSuchMethod(Invocation.method(#isAndroid, []), returnValue: false)
          as bool);

  @override
  bool isIOS() =>
      (super.noSuchMethod(Invocation.method(#isIOS, []), returnValue: false)
          as bool);

  @override
  bool isWindows() =>
      (super.noSuchMethod(Invocation.method(#isWindows, []), returnValue: false)
          as bool);

  @override
  bool isLinux() =>
      (super.noSuchMethod(Invocation.method(#isLinux, []), returnValue: false)
          as bool);

  @override
  bool isMacOS() =>
      (super.noSuchMethod(Invocation.method(#isMacOS, []), returnValue: false)
          as bool);

  @override
  String getEnvironment() =>
      (super.noSuchMethod(
            Invocation.method(#getEnvironment, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getEnvironment, []),
            ),
          )
          as String);

  @override
  bool isProd() =>
      (super.noSuchMethod(Invocation.method(#isProd, []), returnValue: false)
          as bool);

  @override
  bool isDev() =>
      (super.noSuchMethod(Invocation.method(#isDev, []), returnValue: false)
          as bool);

  @override
  bool isQa() =>
      (super.noSuchMethod(Invocation.method(#isQa, []), returnValue: false)
          as bool);

  @override
  bool isStg() =>
      (super.noSuchMethod(Invocation.method(#isStg, []), returnValue: false)
          as bool);

  @override
  String getAppVersion() =>
      (super.noSuchMethod(
            Invocation.method(#getAppVersion, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getAppVersion, []),
            ),
          )
          as String);

  @override
  String getApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getApiHost, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getApiHost, []),
            ),
          )
          as String);

  @override
  String getAuthApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getAuthApiHost, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getAuthApiHost, []),
            ),
          )
          as String);

  @override
  String getApiGatewayHost() =>
      (super.noSuchMethod(
            Invocation.method(#getApiGatewayHost, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getApiGatewayHost, []),
            ),
          )
          as String);

  @override
  String getSocketApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getSocketApiHost, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getSocketApiHost, []),
            ),
          )
          as String);

  @override
  String getReportApiHost() =>
      (super.noSuchMethod(
            Invocation.method(#getReportApiHost, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getReportApiHost, []),
            ),
          )
          as String);

  @override
  String getAppNativeName() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativeName, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeName, []),
            ),
          )
          as String);

  @override
  String getAppNativePackageName() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativePackageName, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getAppNativePackageName, []),
            ),
          )
          as String);

  @override
  String getAppNativeVersion() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativeVersion, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeVersion, []),
            ),
          )
          as String);

  @override
  String getAppNativeBuildNumber() =>
      (super.noSuchMethod(
            Invocation.method(#getAppNativeBuildNumber, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getAppNativeBuildNumber, []),
            ),
          )
          as String);

  @override
  String getGuideBaseUrl() =>
      (super.noSuchMethod(
            Invocation.method(#getGuideBaseUrl, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getGuideBaseUrl, []),
            ),
          )
          as String);

  @override
  String getSupportFaq() =>
      (super.noSuchMethod(
            Invocation.method(#getSupportFaq, []),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getSupportFaq, []),
            ),
          )
          as String);

  @override
  _i5.Future<void> updateAuthHost() =>
      (super.noSuchMethod(
            Invocation.method(#updateAuthHost, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> updateServerEnv() =>
      (super.noSuchMethod(
            Invocation.method(#updateServerEnv, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setHost(String? zoneId) =>
      (super.noSuchMethod(
            Invocation.method(#setHost, [zoneId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

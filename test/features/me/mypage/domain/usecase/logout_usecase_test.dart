import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/domain/repositories/logout_repository.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/domain/usecase/logout_usercase.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 mock 类
@GenerateMocks([LogoutRepository])
import 'logout_usecase_test.mocks.dart';

void main() {
  group('LogoutUseCase', () {
    late LogoutUseCase useCase;
    late MockLogoutRepository mockLogoutRepository;

    setUp(() {
      mockLogoutRepository = MockLogoutRepository();
      useCase = LogoutUseCase(mockLogoutRepository);
    });

    tearDown(() {
      reset(mockLogoutRepository);
    });

    // Phase 0: 基础架构验证
    group('Phase 0: 基础架构验证', () {
      // 0.1 构造函数和依赖注入测试
      group('0.1 构造函数和依赖注入测试', () {
        test('应该能够创建 LogoutUseCase 实例', () {
          // Assert
          expect(useCase, isNotNull);
          expect(useCase, isA<LogoutUseCase>());
        });

        test('应该正确注入 LogoutRepository 依赖', () {
          // Arrange
          final testRepository = MockLogoutRepository();

          // Act
          final testUseCase = LogoutUseCase(testRepository);

          // Assert
          expect(testUseCase.logoutRepository, equals(testRepository));
        });

        test('构造函数应该要求必需的 LogoutRepository 参数', () {
          // Act & Assert - 验证构造函数参数为required
          expect(() => LogoutUseCase(mockLogoutRepository), returnsNormally);
        });

        test('依赖应该存储在正确的实例变量中', () {
          // Assert - 验证依赖正确存储
          expect(useCase.logoutRepository, isNotNull);
          expect(useCase.logoutRepository, equals(mockLogoutRepository));
          expect(useCase.logoutRepository, isA<LogoutRepository>());
        });
      });

      // 0.2 接口实现验证
      group('0.2 接口实现验证', () {
        test('应该实现 UseCase<void, NoParams> 接口', () {
          // Assert
          expect(useCase, isA<UseCase<void, NoParams>>());
        });

        test('应该具有正确的 call 方法签名', () {
          // Assert - 验证call方法存在且签名正确
          expect(useCase.call, isA<Future<void> Function(NoParams)>());
        });

        test('call 方法应该返回 Future<void>', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act
          final result = useCase.call(NoParams());

          // Assert - 验证返回类型
          expect(result, isA<Future<void>>());

          // 等待完成
          await result;
        });

        test('call 方法应该接受 NoParams 参数', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});
          final noParams = NoParams();

          // Act & Assert - 验证能够接受NoParams参数
          expect(() => useCase.call(noParams), returnsNormally);

          // 等待完成
          await useCase.call(noParams);
        });
      });

      // 0.3 类型和泛型验证
      group('0.3 类型和泛型验证', () {
        test('应该具有正确的泛型类型参数', () {
          // Assert - 验证泛型类型正确
          expect(useCase, isA<UseCase<void, NoParams>>());

          // 更具体的类型验证
          final UseCase<void, NoParams> typedUseCase = useCase;
          expect(typedUseCase, isNotNull);
        });

        test('方法签名应该与接口契约匹配', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act
          final Future<void> result = useCase.call(NoParams());

          // Assert - 验证方法签名完全匹配
          expect(result, isA<Future<void>>());

          // 验证能够正常完成
          await expectLater(result, completes);
        });

        test('应该正确处理异步方法标记', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act
          final result = useCase.call(NoParams());

          // Assert - 验证是真正的异步方法
          expect(result, isA<Future<void>>());
          expect(result.runtimeType.toString(), contains('Future'));

          // 验证异步完成
          await result;
        });

        test('应该能够处理 NoParams 的不同实例', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});
          final noParams1 = NoParams();
          final noParams2 = NoParams();

          // Act & Assert - 验证能够处理不同的NoParams实例
          await useCase.call(noParams1);
          await useCase.call(noParams2);

          // 验证Repository被调用了两次
          verify(mockLogoutRepository.logout()).called(2);
        });
      });
    });

    // Phase 1: 核心功能测试
    group('Phase 1: 核心功能测试', () {
      // 1.1 成功场景测试
      group('1.1 成功场景测试', () {
        test('应该成功调用 logoutRepository.logout()', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act
          await useCase.call(NoParams());

          // Assert
          verify(mockLogoutRepository.logout()).called(1);
        });

        test('应该使用正确的Mock验证Repository调用', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act
          await useCase.call(NoParams());

          // Assert - 精确验证Mock调用
          verify(mockLogoutRepository.logout()).called(1);
          verifyNoMoreInteractions(mockLogoutRepository);
        });

        test('应该确保Repository方法被调用且仅调用一次', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act
          await useCase.call(NoParams());

          // Assert - 验证调用次数
          verify(mockLogoutRepository.logout()).called(1);
          verifyNoMoreInteractions(mockLogoutRepository);
        });

        test('方法应该正常完成不抛出异常', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act & Assert - 验证正常完成
          await expectLater(useCase.call(NoParams()), completes);
        });

        test('应该返回正确的void类型', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act & Assert - 验证方法正常完成（void方法不返回值）
          await expectLater(useCase.call(NoParams()), completes);

          // 验证Repository被正确调用
          verify(mockLogoutRepository.logout()).called(1);
        });
      });

      // 1.2 调用行为验证
      group('1.2 调用行为验证', () {
        test('应该验证Repository方法调用的精确性', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act
          await useCase.call(NoParams());

          // Assert - 精确验证方法调用
          verify(mockLogoutRepository.logout()).called(1);
        });

        test('应该确保没有额外的Repository调用', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act
          await useCase.call(NoParams());

          // Assert - 仅验证没有额外的调用（logout调用是预期的）
          verify(mockLogoutRepository.logout()).called(1);
        });

        test('应该验证调用时机在call方法内部', () async {
          // Arrange
          var repositoryCalled = false;
          when(mockLogoutRepository.logout()).thenAnswer((_) async {
            repositoryCalled = true;
          });

          // Act
          expect(repositoryCalled, isFalse, reason: 'Repository不应该在call之前被调用');

          await useCase.call(NoParams());

          // Assert
          expect(repositoryCalled, isTrue, reason: 'Repository应该在call之后被调用');
          verify(mockLogoutRepository.logout()).called(1);
        });

        test('应该确保参数传递正确（虽然没有参数）', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act
          await useCase.call(NoParams());

          // Assert - 验证logout方法被调用且无参数
          verify(mockLogoutRepository.logout()).called(1);

          // 验证没有其他带参数的方法被调用
          verifyNoMoreInteractions(mockLogoutRepository);
        });

        test('应该支持多次调用UseCase', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act - 多次调用
          await useCase.call(NoParams());
          await useCase.call(NoParams());
          await useCase.call(NoParams());

          // Assert - 验证Repository被调用3次
          verify(mockLogoutRepository.logout()).called(3);
        });
      });

      // 1.3 异常传播测试
      group('1.3 异常传播测试', () {
        test('应该正确传播 BusinessException', () async {
          // Arrange
          final businessException = BusinessException('业务异常测试');
          when(mockLogoutRepository.logout()).thenThrow(businessException);

          // Act & Assert
          expect(() => useCase.call(NoParams()), throwsA(predicate((e) => identical(e, businessException))));

          verify(mockLogoutRepository.logout()).called(1);
        });

        test('应该正确传播 SystemException', () async {
          // Arrange
          final systemException = SystemException(technicalMessage: '系统异常测试');
          when(mockLogoutRepository.logout()).thenThrow(systemException);

          // Act & Assert
          expect(() => useCase.call(NoParams()), throwsA(predicate((e) => identical(e, systemException))));

          verify(mockLogoutRepository.logout()).called(1);
        });

        test('应该正确传播一般 Exception', () async {
          // Arrange
          final generalException = Exception('一般异常测试');
          when(mockLogoutRepository.logout()).thenThrow(generalException);

          // Act & Assert
          expect(() => useCase.call(NoParams()), throwsA(predicate((e) => identical(e, generalException))));

          verify(mockLogoutRepository.logout()).called(1);
        });

        test('异常不应该被意外捕获或转换', () async {
          // Arrange
          final originalException = BusinessException('原始异常');
          when(mockLogoutRepository.logout()).thenThrow(originalException);

          // Act & Assert
          try {
            await useCase.call(NoParams());
            fail('应该抛出异常');
          } catch (e) {
            expect(identical(e, originalException), isTrue, reason: '异常应该是同一个对象');
            expect(e, isA<BusinessException>());
            expect((e as BusinessException).message, equals('原始异常'));
          }

          verify(mockLogoutRepository.logout()).called(1);
        });

        test('异常对象应该保持原始状态', () async {
          // Arrange
          final originalException = SystemException(technicalMessage: '技术消息');
          when(mockLogoutRepository.logout()).thenThrow(originalException);

          // Act & Assert
          try {
            await useCase.call(NoParams());
            fail('应该抛出异常');
          } catch (e) {
            expect(identical(e, originalException), isTrue);
            expect(e, isA<SystemException>());
            final systemEx = e as SystemException;
            expect(systemEx.technicalMessage, equals('技术消息'));
            expect(systemEx.message, equals('システムエラーが発生しました。管理者にご連絡ください。'));
          }

          verify(mockLogoutRepository.logout()).called(1);
        });
      });

      // 1.4 参数处理验证
      group('1.4 参数处理验证', () {
        test('应该正确接收 NoParams 参数', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});
          final noParams = NoParams();

          // Act & Assert - 验证能够接收参数
          await expectLater(useCase.call(noParams), completes);
          verify(mockLogoutRepository.logout()).called(1);
        });

        test('对 NoParams 参数的处理应该正确（虽然不使用）', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});
          final noParams1 = NoParams();
          final noParams2 = NoParams();

          // Act - 使用不同的NoParams实例
          await useCase.call(noParams1);
          await useCase.call(noParams2);

          // Assert - 验证参数不影响调用行为
          verify(mockLogoutRepository.logout()).called(2);
        });

        test('方法调用不应该依赖参数内容', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act - 即使参数为null（理论上），也应该正常工作
          await useCase.call(NoParams());

          // Assert
          verify(mockLogoutRepository.logout()).called(1);
          verifyNoMoreInteractions(mockLogoutRepository);
        });

        test('应该能够处理连续的参数调用', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act - 连续调用多次
          for (int i = 0; i < 5; i++) {
            await useCase.call(NoParams());
          }

          // Assert
          verify(mockLogoutRepository.logout()).called(5);
        });

        test('参数类型应该严格匹配 NoParams', () async {
          // Arrange
          when(mockLogoutRepository.logout()).thenAnswer((_) async {});

          // Act
          final noParams = NoParams();
          await useCase.call(noParams);

          // Assert - 验证参数类型
          expect(noParams, isA<NoParams>());
          verify(mockLogoutRepository.logout()).called(1);
        });
      });
    });
  });
}

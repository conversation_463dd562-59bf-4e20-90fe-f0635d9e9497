import 'dart:async';

import 'package:asset_force_mobile_v2/core/env/env_helper_impl.dart';
import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/biometrics_switch_util.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/domain/usecase/mypage_usercase.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/models/mypage_ui_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_use_status_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_user_tenant_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_my_account_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 mock 类
@GenerateMocks([UserRepository, BiometricsSwitchUtil, IStorageUtils, IEnvHelper])
import 'mypage_usercase_test.mocks.dart';

void main() {
  group('MyPageLoadDataCase', () {
    late MyPageLoadDataCase useCase;
    late MockUserRepository mockUserRepository;
    late MockBiometricsSwitchUtil mockBiometricsSwitchUtil;
    late MockIStorageUtils mockStorageUtils;
    late MockIEnvHelper mockEnvHelper;

    setUp(() {
      mockUserRepository = MockUserRepository();
      mockBiometricsSwitchUtil = MockBiometricsSwitchUtil();
      mockStorageUtils = MockIStorageUtils();
      mockEnvHelper = MockIEnvHelper();
    });

    tearDown(() {
      reset(mockUserRepository);
      reset(mockBiometricsSwitchUtil);
      reset(mockStorageUtils);
      reset(mockEnvHelper);
    });

    // Phase 0: 基础架构验证
    group('Phase 0: 基础架构验证', () {
      // 0.1 构造函数和依赖注入测试
      group('0.1 构造函数和依赖注入测试', () {
        test('应该能够创建 MyPageLoadDataCase 实例', () {
          // Act
          useCase = MyPageLoadDataCase(
            userRepository: mockUserRepository,
            biometricsSwitchUtil: mockBiometricsSwitchUtil,
            storageUtils: mockStorageUtils,
            envHelper: mockEnvHelper,
          );

          // Assert
          expect(useCase, isNotNull);
          expect(useCase, isA<MyPageLoadDataCase>());
        });

        test('应该正确注入所有必需的依赖', () {
          // Act
          useCase = MyPageLoadDataCase(
            userRepository: mockUserRepository,
            biometricsSwitchUtil: mockBiometricsSwitchUtil,
            storageUtils: mockStorageUtils,
            envHelper: mockEnvHelper,
          );

          // Assert
          expect(useCase.userRepository, equals(mockUserRepository));
          expect(useCase.biometricsSwitchUtil, equals(mockBiometricsSwitchUtil));
          expect(useCase.storageUtils, equals(mockStorageUtils));
          expect(useCase.envHelper, equals(mockEnvHelper));
        });

        test('未提供 envHelper 时应使用默认实现', () {
          // Act
          useCase = MyPageLoadDataCase(
            userRepository: mockUserRepository,
            biometricsSwitchUtil: mockBiometricsSwitchUtil,
            storageUtils: mockStorageUtils,
            // envHelper 未提供，应使用默认实现
          );

          // Assert
          expect(useCase.envHelper, isNotNull);
          expect(useCase.envHelper, isA<EnvHelperImpl>());
        });

        test('提供 envHelper 时应使用注入的实现', () {
          // Act
          useCase = MyPageLoadDataCase(
            userRepository: mockUserRepository,
            biometricsSwitchUtil: mockBiometricsSwitchUtil,
            storageUtils: mockStorageUtils,
            envHelper: mockEnvHelper,
          );

          // Assert
          expect(useCase.envHelper, equals(mockEnvHelper));
          expect(useCase.envHelper, isNot(isA<EnvHelperImpl>()));
        });

        test('构造函数应该要求必需的参数', () {
          // 这个测试验证构造函数的必需参数在编译时就会检查
          // 如果缺少必需参数，代码将无法编译
          expect(() {
            useCase = MyPageLoadDataCase(
              userRepository: mockUserRepository,
              biometricsSwitchUtil: mockBiometricsSwitchUtil,
              storageUtils: mockStorageUtils,
              // 所有必需参数都存在
            );
          }, returnsNormally);
        });
      });

      // 0.2 接口实现验证
      group('0.2 接口实现验证', () {
        setUp(() {
          useCase = MyPageLoadDataCase(
            userRepository: mockUserRepository,
            biometricsSwitchUtil: mockBiometricsSwitchUtil,
            storageUtils: mockStorageUtils,
            envHelper: mockEnvHelper,
          );
        });

        test('应该正确实现 UseCase 接口', () {
          // Assert
          expect(useCase, isA<UseCase<MyPageUIModel, NoParams>>());
        });

        test('call 方法应该接受 NoParams 参数', () {
          // 这是一个编译时检查，确保方法签名正确
          expect(useCase.call, isA<Function>());

          // 验证方法可以接受NoParams类型
          const params = NoParams();
          expect(params, isA<NoParams>());
        });

        test('call 方法应该具有正确的方法签名', () {
          // 在Phase 0中，我们只验证方法签名，不实际调用
          // 这是一个编译时检查，确保方法签名正确
          expect(useCase.call, isA<Function>());

          // 验证NoParams参数类型存在
          const params = NoParams();
          expect(params, isA<NoParams>());

          // 验证UseCase接口的泛型类型正确
          expect(useCase, isA<UseCase<MyPageUIModel, NoParams>>());
        });
      });

      // 0.3 依赖关系验证
      group('0.3 依赖关系验证', () {
        setUp(() {
          useCase = MyPageLoadDataCase(
            userRepository: mockUserRepository,
            biometricsSwitchUtil: mockBiometricsSwitchUtil,
            storageUtils: mockStorageUtils,
            envHelper: mockEnvHelper,
          );
        });

        test('UserRepository 依赖应该正常工作', () {
          // Assert
          expect(useCase.userRepository, isNotNull);
          expect(useCase.userRepository, isA<UserRepository>());
        });

        test('BiometricsSwitchUtil 依赖应该正常工作', () {
          // Assert
          expect(useCase.biometricsSwitchUtil, isNotNull);
          expect(useCase.biometricsSwitchUtil, isA<BiometricsSwitchUtil>());
        });

        test('IStorageUtils 依赖应该正常工作', () {
          // Assert
          expect(useCase.storageUtils, isNotNull);
          expect(useCase.storageUtils, isA<IStorageUtils>());
        });

        test('IEnvHelper 依赖应该正常工作', () {
          // Assert
          expect(useCase.envHelper, isNotNull);
          expect(useCase.envHelper, isA<IEnvHelper>());
        });
      });

      // 0.4 类型安全验证
      group('0.4 类型安全验证', () {
        setUp(() {
          useCase = MyPageLoadDataCase(
            userRepository: mockUserRepository,
            biometricsSwitchUtil: mockBiometricsSwitchUtil,
            storageUtils: mockStorageUtils,
            envHelper: mockEnvHelper,
          );
        });

        test('应该具有正确的泛型类型', () {
          // Assert - 验证UseCase的泛型类型
          expect(useCase, isA<UseCase<MyPageUIModel, NoParams>>());
        });

        test('依赖字段应该是 final 的', () {
          // 虽然无法直接测试final关键字，但可以验证字段存在且不为null
          expect(useCase.userRepository, isNotNull);
          expect(useCase.biometricsSwitchUtil, isNotNull);
          expect(useCase.storageUtils, isNotNull);
          expect(useCase.envHelper, isNotNull);
        });
      });
    });

    // Phase 1: 核心业务逻辑测试
    group('Phase 1: 核心业务逻辑测试', () {
      setUp(() {
        useCase = MyPageLoadDataCase(
          userRepository: mockUserRepository,
          biometricsSwitchUtil: mockBiometricsSwitchUtil,
          storageUtils: mockStorageUtils,
          envHelper: mockEnvHelper,
        );
      });

      // 1.1 成功路径测试
      group('1.1 成功路径测试', () {
        test('应该成功获取完整的用户页面数据', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupBiometricsMocks(mockBiometricsSwitchUtil);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result, isA<MyPageUIModel>());
          expect(result.userName, equals('<EMAIL>'));
          expect(result.firstName, equals('Test'));
          expect(result.lastName, equals('User'));
          expect(result.tenantName, equals('Test Tenant'));
          expect(result.appVersion, equals('2.1.0'));
          expect(result.isBiometrics, isTrue);
        });

        test('应该正确处理用户信息和租户信息', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => false);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupBiometricsMocks(mockBiometricsSwitchUtil);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.userName, equals('<EMAIL>'));
          expect(result.firstName, equals('Test'));
          expect(result.lastName, equals('User'));
          expect(result.lastLoginTime, equals('2024-01-01 10:00:00'));
          expect(result.tenantName, equals('Test Tenant'));
          expect(result.isBiometrics, isFalse);
        });

        test('应该正确调用所有依赖方法', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupBiometricsMocks(mockBiometricsSwitchUtil);
          setupEnvMocks(mockEnvHelper);

          // Act
          await useCase.call(const NoParams());

          // Assert - 验证所有依赖方法都被调用
          verify(mockUserRepository.getUserInfo()).called(1);
          verify(mockUserRepository.getUserTenant()).called(1);
          verify(mockBiometricsSwitchUtil.isBiometricsAvailable()).called(1);
          verify(mockEnvHelper.getAppVersion()).called(1);
        });
      });

      // 1.2 数据映射验证
      group('1.2 数据映射验证', () {
        test('用户信息为空时应使用默认值', () async {
          // Arrange
          final mockUserInfo = SharedMyAccountModel(
            data: null, // 用户数据为空
            tenantName: 'Test Tenant',
            code: 200,
            msg: 'success',
          );
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupBiometricsMocks(mockBiometricsSwitchUtil);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert - 应该使用默认的空字符串
          expect(result.userName, equals(''));
          expect(result.firstName, equals(''));
          expect(result.lastName, equals(''));
          expect(result.lastLoginTime, equals(''));
          expect(result.tenantName, equals('Test Tenant')); // 来自 tenantName 字段
        });

        test('租户信息为空时应正确处理', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = SharedUserTenantModel(
            tenantUseStatesList: null, // 租户使用状态列表为空
            code: 200,
            msg: 'success',
          );

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupBiometricsMocks(mockBiometricsSwitchUtil);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert - plan和contractPeriodEndDate应该保持空字符串
          expect(result.plan, equals(''));
          expect(result.contractPeriodEndDate, equals(''));
        });

        test('生物识别不可用时应正确设置', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => false);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupBiometricsMocks(mockBiometricsSwitchUtil);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isBiometrics, isFalse);
        });
      });

      // 1.3 异步操作协调
      group('1.3 异步操作协调', () {
        test('多个异步调用应正确执行', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupBiometricsMocks(mockBiometricsSwitchUtil);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert - 确保所有异步操作都完成
          expect(result, isNotNull);

          // 验证调用顺序（基本的依赖方法应该被调用）
          verify(mockUserRepository.getUserInfo()).called(1);
          verify(mockUserRepository.getUserTenant()).called(1);
          verify(mockBiometricsSwitchUtil.isBiometricsAvailable()).called(1);
        });

        test('应该正确协调依赖方法的调用', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupBiometricsMocks(mockBiometricsSwitchUtil);
          setupEnvMocks(mockEnvHelper);

          // Act
          await useCase.call(const NoParams());

          // Assert - 验证正确的方法调用次数
          verify(mockUserRepository.getUserInfo()).called(1);
          verify(mockUserRepository.getUserTenant()).called(1);
          verify(mockBiometricsSwitchUtil.isBiometricsAvailable()).called(1);
          verify(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).called(1);
          verify(mockEnvHelper.getAppVersion()).called(1);
          verify(mockEnvHelper.isProd()).called(1);
          // 注意：由于 isProd() 返回 true，||运算符会短路，不会调用 isStg()
          verifyNever(mockEnvHelper.isStg());

          // 验证存储相关的调用
          verify(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).called(1);
          verify(mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease)).called(1);
          verify(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).called(1);
          verify(mockStorageUtils.getValue<int>(StorageKeys.userId)).called(1);

          // 验证没有额外的调用（需要在 verify 之后调用）
          verifyNoMoreInteractions(mockUserRepository);
          verifyNoMoreInteractions(mockBiometricsSwitchUtil);
          verifyNoMoreInteractions(mockEnvHelper);
          verifyNoMoreInteractions(mockStorageUtils);
        });
      });
    });

    // Phase 2: 私有方法单元测试
    group('Phase 2: 私有方法单元测试', () {
      setUp(() {
        useCase = MyPageLoadDataCase(
          userRepository: mockUserRepository,
          biometricsSwitchUtil: mockBiometricsSwitchUtil,
          storageUtils: mockStorageUtils,
          envHelper: mockEnvHelper,
        );
      });

      // 2.1 _setPlanAndContractInfo 方法测试
      group('2.1 _setPlanAndContractInfo 方法测试', () {
        test('tenantUseStatesList为空时不应设置计划信息', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = SharedUserTenantModel(tenantUseStatesList: null, code: 200, msg: 'success');

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.plan, equals(''));
          expect(result.contractPeriodEndDate, equals(''));
        });

        test('tenantUseStatesList为空列表时不应设置计划信息', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = SharedUserTenantModel(
            tenantUseStatesList: [], // 空列表
            code: 200,
            msg: 'success',
          );

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.plan, equals(''));
          expect(result.contractPeriodEndDate, equals(''));
        });

        test('plan为"6"时应正确设置计划和合同信息', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = SharedUserTenantModel(
            tenantUseStatesList: [SharedTenantUseStatusModel(plan: '6', contractPeriodEndDate: '2024-12-31')],
            code: 200,
            msg: 'success',
          );

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.plan, equals('6'));
          expect(result.contractPeriodEndDate, equals('2024/12/31'));
        });

        test('plan为"8"时应正确设置计划和合同信息', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = SharedUserTenantModel(
            tenantUseStatesList: [SharedTenantUseStatusModel(plan: '8', contractPeriodEndDate: '2025-06-15')],
            code: 200,
            msg: 'success',
          );

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.plan, equals('8'));
          expect(result.contractPeriodEndDate, equals('2025/06/15'));
        });

        test('plan为无效值时不应设置计划信息', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = SharedUserTenantModel(
            tenantUseStatesList: [
              SharedTenantUseStatusModel(
                plan: '5', // 无效值
                contractPeriodEndDate: '2024-12-31',
              ),
            ],
            code: 200,
            msg: 'success',
          );

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.plan, equals(''));
          expect(result.contractPeriodEndDate, equals(''));
        });

        test('plan为null时不应设置计划信息', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = SharedUserTenantModel(
            tenantUseStatesList: [SharedTenantUseStatusModel(plan: null, contractPeriodEndDate: '2024-12-31')],
            code: 200,
            msg: 'success',
          );

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.plan, equals(''));
          expect(result.contractPeriodEndDate, equals(''));
        });

        test('contractPeriodEndDate为null时应使用空字符串', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = SharedUserTenantModel(
            tenantUseStatesList: [SharedTenantUseStatusModel(plan: '6', contractPeriodEndDate: null)],
            code: 200,
            msg: 'success',
          );

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.plan, equals('6'));
          expect(result.contractPeriodEndDate, equals(''));
        });
      });

      // 2.2 _isValidPlan 方法测试（通过集成测试验证）
      group('2.2 _isValidPlan 方法验证', () {
        test('plan为"6"时应返回true', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfoWithPlan('6');

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert - 通过result验证私有方法的行为
          expect(result.plan, equals('6'));
        });

        test('plan为"8"时应返回true', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfoWithPlan('8');

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.plan, equals('8'));
        });

        test('plan为其他值时应返回false', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfoWithPlan('7');

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert - plan应该保持空字符串
          expect(result.plan, equals(''));
        });
      });

      // 2.3 _getIsUserBiometricsOpenSwitch 方法测试
      group('2.3 _getIsUserBiometricsOpenSwitch 方法测试', () {
        test('biometricsSwitchUtil返回true时应设置为true', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isUserBiometricsOpenSwitch, isTrue);
        });

        test('biometricsSwitchUtil返回false时应设置为false', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(false);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isUserBiometricsOpenSwitch, isFalse);
        });
      });

      // 2.4 _setQuantityCountScan 方法测试
      group('2.4 _setQuantityCountScan 方法测试', () {
        test('存储中不包含key时应设置为false', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(false);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isQuantityCountScan, isFalse);
        });

        test('存储值为null时应设置为false', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(true);
          when(mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(null);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isQuantityCountScan, isFalse);
        });

        test('存储值为空字符串时应设置为false', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(true);
          when(mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease)).thenReturn('');
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isQuantityCountScan, isFalse);
        });

        test('JSON解析失败时应设置为false', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(true);
          when(mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease)).thenReturn('invalid-json');
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isQuantityCountScan, isFalse);
        });

        test('tenantId和userId匹配时应返回正确的值', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(true);
          when(
            mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease),
          ).thenReturn('{"isQuantityCountScan": true, "tenantId": "test-tenant", "userId": 123}');
          when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn('test-tenant');
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(123);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isQuantityCountScan, isTrue);
        });

        test('tenantId不匹配时应设置为false', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(true);
          when(
            mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease),
          ).thenReturn('{"isQuantityCountScan": true, "tenantId": "different-tenant", "userId": 123}');
          when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn('test-tenant');
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(123);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isQuantityCountScan, isFalse);
        });

        test('userId不匹配时应设置为false', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(true);
          when(
            mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease),
          ).thenReturn('{"isQuantityCountScan": true, "tenantId": "test-tenant", "userId": 456}');
          when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn('test-tenant');
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(123);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isQuantityCountScan, isFalse);
        });
      });

      // 2.5 _getIsShowSupportFaq 方法测试
      group('2.5 _getIsShowSupportFaq 方法测试', () {
        test('生产环境应显示FAQ支持', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockEnvHelper.isProd()).thenReturn(true);
          when(mockEnvHelper.isStg()).thenReturn(false);
          setupStorageMocks(mockStorageUtils);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isShowSupportFaq, isTrue);
        });

        test('STG环境应显示FAQ支持', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockEnvHelper.isProd()).thenReturn(false);
          when(mockEnvHelper.isStg()).thenReturn(true);
          setupStorageMocks(mockStorageUtils);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isShowSupportFaq, isTrue);
        });

        test('开发环境不应显示FAQ支持', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockEnvHelper.isProd()).thenReturn(false);
          when(mockEnvHelper.isStg()).thenReturn(false);
          setupStorageMocks(mockStorageUtils);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isShowSupportFaq, isFalse);
        });

        test('QA环境不应显示FAQ支持', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockEnvHelper.isProd()).thenReturn(false);
          when(mockEnvHelper.isStg()).thenReturn(false);
          setupStorageMocks(mockStorageUtils);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isShowSupportFaq, isFalse);
        });
      });
    });

    // Phase 3: 异常处理和边界测试
    group('Phase 3: 异常处理和边界测试', () {
      setUp(() {
        useCase = MyPageLoadDataCase(
          userRepository: mockUserRepository,
          biometricsSwitchUtil: mockBiometricsSwitchUtil,
          storageUtils: mockStorageUtils,
          envHelper: mockEnvHelper,
        );
      });

      // 3.1 Repository 异常处理测试
      group('3.1 Repository 异常处理测试', () {
        test('getUserInfo 抛出异常时应传播异常', () async {
          // Arrange
          when(mockUserRepository.getUserInfo()).thenThrow(Exception('Network error'));

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<Exception>()));
        });

        test('getUserTenant 抛出异常时应传播异常', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenThrow(Exception('Service unavailable'));

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<Exception>()));
        });

        test('Repository 超时异常应正确处理', () async {
          // Arrange
          when(
            mockUserRepository.getUserInfo(),
          ).thenThrow(TimeoutException('Request timeout', const Duration(seconds: 30)));

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<TimeoutException>()));
        });

        test('Repository 状态异常应正确处理', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenThrow(StateError('Invalid state'));

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<StateError>()));
        });

        test('Repository 参数异常应正确处理', () async {
          // Arrange
          when(mockUserRepository.getUserInfo()).thenThrow(ArgumentError('Invalid argument'));

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<ArgumentError>()));
        });
      });

      // 3.2 BiometricsSwitchUtil 异常处理测试
      group('3.2 BiometricsSwitchUtil 异常处理测试', () {
        test('isBiometricsAvailable 抛出异常时应传播异常', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenThrow(Exception('Biometrics not supported'));

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<Exception>()));
        });

        test('getUserBiometricsOpenSwitch 抛出异常时应传播异常', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenThrow(StateError('Biometrics state error'));
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<StateError>()));
        });

        test('BiometricsSwitchUtil 平台异常应正确处理', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenThrow(UnsupportedError('Platform not supported'));

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<UnsupportedError>()));
        });
      });

      // 3.3 StorageUtils 异常处理测试
      group('3.3 StorageUtils 异常处理测试', () {
        test('containsKey 抛出异常时应传播异常', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(
            mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease),
          ).thenThrow(Exception('Storage access denied'));
          setupEnvMocks(mockEnvHelper);

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<Exception>()));
        });

        test('getValue 抛出异常时应传播异常', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(true);
          when(
            mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease),
          ).thenThrow(StateError('Storage corrupted'));
          setupEnvMocks(mockEnvHelper);

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<StateError>()));
        });

        test('Storage 权限异常应正确处理', () async {
          // Arrange - 测试在_setQuantityCountScan中的异常处理，应该设置为false而不是传播异常
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(true);
          when(
            mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease),
          ).thenReturn('{"isQuantityCountScan": true, "tenantId": "test-tenant", "userId": 123}');
          when(
            mockStorageUtils.getValue<String>(StorageKeys.tenantId),
          ).thenThrow(UnimplementedError('Permission denied'));
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(123);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert - 异常被捕获，isQuantityCountScan应设置为false
          expect(result.isQuantityCountScan, isFalse);
        });

        test('Storage 类型转换异常应正确处理', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenThrow(TypeError());

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<TypeError>()));
        });
      });

      // 3.4 EnvHelper 异常处理测试
      group('3.4 EnvHelper 异常处理测试', () {
        test('getAppVersion 抛出异常时应传播异常', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenThrow(UnsupportedError('Platform info not available'));

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<UnsupportedError>()));
        });

        test('isProd 抛出异常时应传播异常', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(false);
          when(mockEnvHelper.isProd()).thenThrow(StateError('Environment configuration error'));

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<StateError>()));
        });

        test('环境检测异常应正确处理', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(false);
          when(mockEnvHelper.isProd()).thenReturn(false);
          when(mockEnvHelper.isStg()).thenThrow(Exception('Environment detection failed'));

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<Exception>()));
        });
      });

      // 3.5 复杂异常场景组合测试
      group('3.5 复杂异常场景组合测试', () {
        test('多个异步操作同时失败时应传播第一个异常', () async {
          // Arrange
          when(mockUserRepository.getUserInfo()).thenThrow(Exception('Repository error'));
          when(mockUserRepository.getUserTenant()).thenThrow(Exception('Tenant error'));
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenThrow(Exception('Biometrics error'));

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<Exception>()));
        });

        test('部分依赖成功但关键依赖失败时应传播异常', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenThrow(Exception('Critical service down'));
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<Exception>()));
        });

        test('存储异常发生在后期处理阶段时应被正确处理', () async {
          // Arrange - 测试在_setQuantityCountScan中的异常处理，应该设置为false而不是传播异常
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(true);
          when(
            mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease),
          ).thenReturn('{"isQuantityCountScan": true, "tenantId": "test-tenant", "userId": 123}');
          when(
            mockStorageUtils.getValue<String>(StorageKeys.tenantId),
          ).thenThrow(Exception('Late stage storage failure'));
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(123);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert - 异常被捕获，isQuantityCountScan应设置为false
          expect(result.isQuantityCountScan, isFalse);
        });

        test('环境检测失败在最后阶段时应传播异常', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(false);
          when(mockEnvHelper.isProd()).thenThrow(Exception('Environment check failed at final stage'));

          // Act & Assert
          expect(() => useCase.call(const NoParams()), throwsA(isA<Exception>()));
        });
      });

      // 3.6 边界值和极限测试
      group('3.6 边界值和极限测试', () {
        test('处理超长字符串时应正常工作', () async {
          // Arrange
          final longString = 'a' * 10000; // 10K字符
          final mockUserInfo = SharedMyAccountModel(
            data: SharedUserModel(
              userName: longString,
              firstName: longString,
              lastName: longString,
              lastLoginTime: longString,
            ),
            tenantName: longString,
            code: 200,
            msg: 'success',
          );
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.userName, equals(longString));
          expect(result.firstName, equals(longString));
          expect(result.lastName, equals(longString));
          expect(result.tenantName, equals(longString));
        });

        test('处理包含特殊字符的数据时应正常工作', () async {
          // Arrange
          const specialChars = '!@#\$%^&*()_+-=[]{}|;:,.<>?~`"\'\\n\\r\\t';
          final mockUserInfo = SharedMyAccountModel(
            data: SharedUserModel(
              userName: specialChars,
              firstName: specialChars,
              lastName: specialChars,
              lastLoginTime: specialChars,
            ),
            tenantName: specialChars,
            code: 200,
            msg: 'success',
          );
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.userName, equals(specialChars));
          expect(result.firstName, equals(specialChars));
          expect(result.lastName, equals(specialChars));
          expect(result.tenantName, equals(specialChars));
        });

        test('处理Unicode字符时应正常工作', () async {
          // Arrange
          const unicodeChars = '中文测试 🚀 💻 ✨ 🎉 العربية Русский 日本語 한국어';
          final mockUserInfo = SharedMyAccountModel(
            data: SharedUserModel(
              userName: unicodeChars,
              firstName: unicodeChars,
              lastName: unicodeChars,
              lastLoginTime: '2024-01-01 10:00:00',
            ),
            tenantName: unicodeChars,
            code: 200,
            msg: 'success',
          );
          final mockTenantInfo = createMockTenantInfo();

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.userName, equals(unicodeChars));
          expect(result.firstName, equals(unicodeChars));
          expect(result.lastName, equals(unicodeChars));
          expect(result.tenantName, equals(unicodeChars));
        });

        test('处理超大JSON数据时应正常工作', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          // 创建一个大的JSON字符串
          final largeJsonData =
              '{"isQuantityCountScan": true, "tenantId": "test-tenant", "userId": 123, "extraData": "${'x' * 5000}"}';

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(true);
          when(mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(largeJsonData);
          when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn('test-tenant');
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(123);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isQuantityCountScan, isTrue);
        });

        test('处理嵌套JSON结构时应正确解析', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();
          final mockTenantInfo = createMockTenantInfo();

          // 创建嵌套JSON
          const nestedJson = '''
          {
            "isQuantityCountScan": true,
            "tenantId": "test-tenant",
            "userId": 123,
            "metadata": {
              "device": "mobile",
              "settings": {
                "theme": "dark",
                "notifications": true
              }
            }
          }''';

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(true);
          when(mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(nestedJson);
          when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn('test-tenant');
          when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(123);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert
          expect(result.isQuantityCountScan, isTrue);
        });

        test('处理大量租户使用状态列表时应正常工作', () async {
          // Arrange
          final mockUserInfo = createMockUserInfo();

          // 创建大量租户使用状态
          final tenantUseStatesList = List.generate(
            1000,
            (index) =>
                SharedTenantUseStatusModel(plan: index % 2 == 0 ? '6' : '8', contractPeriodEndDate: '2024-12-31'),
          );

          final mockTenantInfo = SharedUserTenantModel(
            tenantUseStatesList: tenantUseStatesList,
            code: 200,
            msg: 'success',
          );

          when(mockUserRepository.getUserInfo()).thenAnswer((_) async => mockUserInfo);
          when(mockUserRepository.getUserTenant()).thenAnswer((_) async => mockTenantInfo);
          when(mockBiometricsSwitchUtil.isBiometricsAvailable()).thenAnswer((_) async => true);
          when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
          when(mockEnvHelper.getAppVersion()).thenReturn('2.1.0');
          setupStorageMocks(mockStorageUtils);
          setupEnvMocks(mockEnvHelper);

          // Act
          final result = await useCase.call(const NoParams());

          // Assert - 应该使用第一个元素
          expect(result.plan, equals('6'));
          expect(result.contractPeriodEndDate, equals('2024/12/31'));
        });
      });
    });
  });
}

// 辅助方法：创建Mock数据

SharedMyAccountModel createMockUserInfo() {
  return SharedMyAccountModel(
    data: SharedUserModel(
      userName: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      lastLoginTime: '2024-01-01 10:00:00',
    ),
    tenantName: 'Test Tenant',
    code: 200,
    msg: 'success',
  );
}

SharedUserTenantModel createMockTenantInfo() {
  return SharedUserTenantModel(
    tenantUseStatesList: [
      SharedTenantUseStatusModel(
        plan: '6', // 有效的计划
        contractPeriodEndDate: '2024-12-31',
      ),
    ],
    code: 200,
    msg: 'success',
  );
}

SharedUserTenantModel createMockTenantInfoWithPlan(String plan) {
  return SharedUserTenantModel(
    tenantUseStatesList: [SharedTenantUseStatusModel(plan: plan, contractPeriodEndDate: '2024-12-31')],
    code: 200,
    msg: 'success',
  );
}

void setupStorageMocks(MockIStorageUtils mockStorageUtils) {
  when(mockStorageUtils.containsKey(StorageKeys.isNumberNeedAutoIncrease)).thenReturn(true);
  when(
    mockStorageUtils.getValue<String>(StorageKeys.isNumberNeedAutoIncrease),
  ).thenReturn('{"isQuantityCountScan": true, "tenantId": "test-tenant", "userId": 123}');
  when(mockStorageUtils.getValue<String>(StorageKeys.tenantId)).thenReturn('test-tenant');
  when(mockStorageUtils.getValue<int>(StorageKeys.userId)).thenReturn(123);
}

void setupBiometricsMocks(MockBiometricsSwitchUtil mockBiometricsSwitchUtil) {
  when(mockBiometricsSwitchUtil.getUserBiometricsOpenSwitch()).thenReturn(true);
}

void setupEnvMocks(MockIEnvHelper mockEnvHelper) {
  when(mockEnvHelper.isProd()).thenReturn(true);
  when(mockEnvHelper.isStg()).thenReturn(false);
}

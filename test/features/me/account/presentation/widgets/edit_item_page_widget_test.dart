import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/me/account/presentation/controllers/edit_item_controller.dart';
import 'package:asset_force_mobile_v2/features/me/account/presentation/widgets/edit_item_page_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'edit_item_page_widget_test.mocks.dart';

// Mock类注解定义
@GenerateNiceMocks([MockSpec<EditItemController>(), MockSpec<InternalFinalCallback>()])
void main() {
  group('EditItemPageWidget Tests', () {
    late MockEditItemController mockController;
    late MockInternalFinalCallback mockInternalFinalCallback;

    // 测试数据状态
    late String testItemTitle;
    late String testItemValue;
    late String emptyItemTitle;
    late String emptyItemValue;
    late String longItemTitle;
    late String longItemValue;

    // ========================================
    // 测试数据初始化
    // ========================================
    void setupTestData() {
      // 正常测试数据
      testItemTitle = '姓';
      testItemValue = '田中';

      // 空值测试数据
      emptyItemTitle = '';
      emptyItemValue = '';

      // 长文本测试数据
      longItemTitle = 'あいうえおかきくけこさしすせそたちつてと'; // 25个字符
      longItemValue = 'たなかたろうたなかたろうたなかたろう'; // 长名字
    }

    // ========================================
    // Mock Controller配置
    // ========================================
    void setupMockController() {
      // 配置基本的observable属性
      when(mockController.itemTitle).thenReturn(testItemTitle.obs);
      when(mockController.itemValue).thenReturn(testItemValue.obs);

      // 配置TextEditingController
      when(mockController.textController).thenReturn(TextEditingController(text: testItemValue));

      // 配置GetX生命周期方法
      when(mockController.onInit()).thenAnswer((_) async {});
      when(mockController.onClose()).thenAnswer((_) async {});
      when(mockController.onStart).thenReturn(mockInternalFinalCallback);
      when(mockController.onDelete).thenReturn(mockInternalFinalCallback);

      // 配置控制器方法（返回void，不需要特殊配置）
      // when(mockController.back()).thenReturn(null);
      // when(mockController.save()).thenReturn(null);
      // when(mockController.clearItemValue()).thenReturn(null);
    }

    setUp(() {
      // 重置GetX状态
      Get.reset();

      // 创建Mock实例
      mockController = MockEditItemController();
      mockInternalFinalCallback = MockInternalFinalCallback();

      // 初始化测试数据
      setupTestData();

      // 配置Mock Controller的基本行为
      setupMockController();
    });

    tearDown(() {
      // 清理资源
      reset(mockController);
      clearInteractions(mockController);
      Get.reset();
    });

    // ========================================
    // Widget创建辅助方法
    // ========================================
    Widget createWidgetUnderTest({String? itemTitle, String? itemValue, EditItemController? controller}) {
      // 使用传入参数或默认测试数据
      final title = itemTitle ?? testItemTitle;
      final value = itemValue ?? testItemValue;

      // 如果传入了自定义参数，需要重新配置Mock
      if (itemTitle != null || itemValue != null) {
        // 重新配置Mock的observable属性以匹配传入参数
        when(mockController.itemTitle).thenReturn(title.obs);
        when(mockController.itemValue).thenReturn(value.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: value));
      }

      // 如果需要自定义controller，重新配置
      if (controller != null) {
        Get.put<EditItemController>(controller);
      } else {
        // 使用Mock配置
        Get.put<EditItemController>(mockController);
      }

      return GetMaterialApp(theme: AppTheme.lightTheme, home: EditItemPageWidget(title, value));
    }

    // ========================================
    // 基础框架验证测试
    // ========================================
    group('测试基础设施验证', () {
      testWidgets('Mock设置验证 - 确保基础Mock配置正确', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证页面基本渲染
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);

        // Assert - 验证Mock controller被正确注册
        expect(Get.find<EditItemController>(), isA<MockEditItemController>());
      });

      testWidgets('GetX依赖注入验证 - 确保Controller正确注入', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证GetView可以正确访问controller
        expect(Get.isRegistered<EditItemController>(), isTrue);

        // 验证Widget可以正常构建
        expect(find.byType(EditItemPageWidget), findsOneWidget);
      });
    });

    // ========================================
    // Phase 1: UI结构和基础渲染测试
    // ========================================
    group('Phase 1: UI结构和基础渲染测试', () {
      testWidgets('Scaffold基础结构验证 - 验证页面基本布局组件', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证基础Scaffold结构
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(Padding), findsAtLeastNWidgets(1)); // Body中的Padding

        // Assert - 验证底部导航栏存在
        expect(find.byKey(const Key('edit_item_cancel_button')).first, findsOneWidget);
        expect(find.byKey(const Key('edit_item_confirm_button')).first, findsOneWidget);

        // Assert - 验证主要布局容器
        expect(find.byType(Column), findsAtLeastNWidgets(1));
        expect(find.byType(Row), findsAtLeastNWidgets(1));
      });

      testWidgets('AppBar配置验证 - 验证AppBar标题和返回按钮', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证AppBar基本配置
        final appBar = tester.widget<AppBar>(find.byType(AppBar));
        expect(appBar.backgroundColor, Colors.transparent);
        expect(appBar.elevation, 0);

        // Assert - 验证AppBar标题
        expect(find.text('編集'), findsOneWidget);

        // Assert - 验证返回按钮存在和配置
        expect(find.byKey(const Key('edit_item_back_button')), findsOneWidget);
        expect(find.byIcon(Icons.chevron_left), findsOneWidget);

        // Assert - 验证返回按钮在AppBar的leading位置
        final iconButton = tester.widget<IconButton>(find.byKey(const Key('edit_item_back_button')));
        expect(iconButton.icon, isA<Icon>());
      });

      testWidgets('主体表单布局验证 - 验证表单容器和TextField配置', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证表单主容器
        expect(find.byType(Container), findsAtLeastNWidgets(1));
        expect(find.byType(Obx), findsOneWidget); // 响应式容器

        // Assert - 验证Row布局（表单行）
        expect(find.byType(Row), findsAtLeastNWidgets(1));
        expect(find.byType(Expanded), findsAtLeastNWidgets(2)); // 左右两列的Expanded

        // Assert - 验证TextField存在和配置
        expect(find.byKey(const Key('edit_item_text_field')), findsOneWidget);
        expect(find.byType(TextField), findsOneWidget);

        // Assert - 验证TextField配置
        final textField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(textField.decoration?.hintText, '未設定');
        expect(textField.decoration?.border, InputBorder.none);
      });

      testWidgets('底部按钮栏验证 - 验证取消和确定按钮布局', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证底部导航栏容器
        expect(find.byType(OverflowBar), findsOneWidget);
        expect(find.byType(FractionallySizedBox), findsNWidgets(2)); // 两个按钮容器

        // Assert - 验证取消按钮
        expect(find.byKey(const Key('edit_item_cancel_button')), findsOneWidget);
        expect(find.text('キャンセル'), findsOneWidget);

        // Assert - 验证确定按钮
        expect(find.byKey(const Key('edit_item_confirm_button')), findsOneWidget);
        expect(find.text('確定'), findsOneWidget);

        // Assert - 验证按钮类型
        expect(find.byType(ElevatedButton), findsNWidgets(2));

        // Assert - 验证按钮宽度配置
        final cancelButtonContainer = tester.widget<FractionallySizedBox>(
          find
              .ancestor(
                of: find.byKey(const Key('edit_item_cancel_button')),
                matching: find.byType(FractionallySizedBox),
              )
              .first,
        );
        expect(cancelButtonContainer.widthFactor, 0.35);
      });

      testWidgets('所有Key存在性验证 - 验证所有定义Key的存在', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证所有必须的Key存在
        expect(find.byKey(const Key('edit_item_back_button')), findsOneWidget);
        expect(find.byKey(const Key('edit_item_text_field')), findsOneWidget);
        expect(find.byKey(const Key('edit_item_cancel_button')), findsOneWidget);
        expect(find.byKey(const Key('edit_item_confirm_button')), findsOneWidget);

        // Note: edit_item_clear_button只在有内容时显示，这里验证其不存在（初始状态）
        // 这个验证将在响应式测试中进行
      });

      testWidgets('初始文本显示验证 - 验证itemTitle和itemValue正确显示', (WidgetTester tester) async {
        // Act - 先测试默认数据
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证默认文本显示
        expect(find.text(testItemTitle), findsOneWidget); // '姓'

        // Assert - 验证TextField初始值
        final defaultTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(defaultTextField.controller?.text, testItemValue); // '田中'

        // 单独测试自定义参数 - 清理后重新创建
        await tester.pumpWidget(Container()); // 清空
        await tester.pumpAndSettle();

        // Arrange - 使用自定义测试数据
        const customTitle = '名前';
        const customValue = '太郎';

        // Act
        await tester.pumpWidget(createWidgetUnderTest(itemTitle: customTitle, itemValue: customValue));
        await tester.pumpAndSettle();

        // Assert - 验证自定义标题文本显示
        expect(find.text(customTitle), findsOneWidget);

        // Assert - 验证自定义TextField初始值
        final textField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(textField.controller?.text, customValue);
      });

      testWidgets('按钮样式和配置验证 - 验证按钮外观和样式配置', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证取消按钮样式
        final cancelButton = tester.widget<ElevatedButton>(find.byKey(const Key('edit_item_cancel_button')));
        expect(cancelButton.style?.backgroundColor?.resolve({}), Colors.white);
        expect(cancelButton.style?.side?.resolve({})?.color, AppTheme.lightTheme.primaryColor);

        // Assert - 验证确定按钮样式
        final confirmButton = tester.widget<ElevatedButton>(find.byKey(const Key('edit_item_confirm_button')));
        expect(confirmButton.child, isA<Text>());

        // Assert - 验证确定按钮文本样式
        final confirmText = tester.widget<Text>(find.text('確定'));
        expect(confirmText.style?.color, Colors.white);

        // Assert - 验证取消按钮文本（无特殊样式）
        expect(find.text('キャンセル'), findsOneWidget);

        // Assert - 验证容器背景色
        expect(find.byType(Container), findsAtLeastNWidgets(1));
      });
    });

    // ========================================
    // Phase 2: 响应式状态和数据绑定测试
    // ========================================
    group('Phase 2: 响应式状态和数据绑定测试', () {
      testWidgets('Obx响应性验证 - 验证Obx包装内容对状态变化的响应', (WidgetTester tester) async {
        // Arrange - 设置动态响应式数据
        final dynamicValue = testItemValue.obs;
        when(mockController.itemValue).thenReturn(dynamicValue);
        when(mockController.textController).thenReturn(TextEditingController(text: dynamicValue.value));

        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证Obx容器存在
        expect(find.byType(Obx), findsOneWidget);

        // Assert - 验证初始状态渲染
        final initialTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(initialTextField.controller?.text, testItemValue);

        // Act - 改变observable值
        const newValue = '響応性テスト';
        dynamicValue.value = newValue;
        when(mockController.textController).thenReturn(TextEditingController(text: newValue));

        // 重新pump来触发Obx响应式更新
        await tester.pumpWidget(createWidgetUnderTest(itemValue: newValue));
        await tester.pumpAndSettle();

        // Assert - 验证Obx响应变化
        final updatedTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(updatedTextField.controller?.text, newValue);
      });

      testWidgets('itemTitle状态绑定验证 - 验证标题状态变化时UI更新', (WidgetTester tester) async {
        // Arrange - 设置动态标题
        final dynamicTitle = testItemTitle.obs;
        when(mockController.itemTitle).thenReturn(dynamicTitle);

        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证初始标题显示
        expect(find.text(testItemTitle), findsOneWidget);

        // Act - 更改标题状态
        const newTitle = '新しいタイトル';
        dynamicTitle.value = newTitle;

        // 重新创建widget以反映状态变化
        await tester.pumpWidget(createWidgetUnderTest(itemTitle: newTitle));
        await tester.pumpAndSettle();

        // Assert - 验证UI响应标题变化
        expect(find.text(newTitle), findsOneWidget);
        expect(find.text(testItemTitle), findsNothing); // 旧标题不再显示
      });

      testWidgets('itemValue状态绑定验证 - 验证值状态变化时UI更新', (WidgetTester tester) async {
        // Arrange - 设置动态值
        final dynamicValue = testItemValue.obs;
        when(mockController.itemValue).thenReturn(dynamicValue);
        when(mockController.textController).thenReturn(TextEditingController(text: dynamicValue.value));

        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证初始值显示
        final initialTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(initialTextField.controller?.text, testItemValue);

        // Act - 通过controller更新值（模拟programmatic更新）
        const programmaticValue = 'プログラム更新';
        dynamicValue.value = programmaticValue;
        when(mockController.textController).thenReturn(TextEditingController(text: programmaticValue));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: programmaticValue));
        await tester.pumpAndSettle();

        // Assert - 验证UI反映programmatic更新
        final updatedTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(updatedTextField.controller?.text, programmaticValue);

        // Act - 再次更新验证连续响应
        const secondValue = '二回目更新';
        dynamicValue.value = secondValue;
        when(mockController.textController).thenReturn(TextEditingController(text: secondValue));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: secondValue));
        await tester.pumpAndSettle();

        // Assert - 验证连续状态更新
        final secondTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(secondTextField.controller?.text, secondValue);
      });

      testWidgets('TextField双向绑定验证 - 验证TextField与textController双向绑定', (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Phase 1: Controller -> TextField (单向绑定验证)
        final initialTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(initialTextField.controller?.text, testItemValue);

        // Phase 2: TextField -> Controller (用户输入绑定验证)
        const userInput = 'ユーザー双方向入力';
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), userInput);
        await tester.pumpAndSettle();

        // Assert - 验证TextField内容更新（用户输入直接体现在UI）
        final userInputTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(userInputTextField.controller?.text, userInput);

        // Phase 3: 验证controller programmatic更新能反映到UI
        const controllerUpdate = 'コントローラー更新';
        when(mockController.itemValue).thenReturn(controllerUpdate.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: controllerUpdate));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: controllerUpdate));
        await tester.pumpAndSettle();

        // Assert - 验证controller更新反映到UI
        final controllerUpdatedTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(controllerUpdatedTextField.controller?.text, controllerUpdate);
      });

      testWidgets('清除按钮条件显示验证 - 验证按钮显示/隐藏逻辑', (WidgetTester tester) async {
        // Phase 1: 验证空内容时清除按钮隐藏
        when(mockController.itemValue).thenReturn(''.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: ''));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: ''));
        await tester.pumpAndSettle();

        // Assert - 验证清除按钮不显示（当内容为空时）
        expect(find.byKey(const Key('edit_item_clear_button')), findsNothing);

        // Phase 2: 验证有内容时清除按钮显示
        const contentValue = '表示内容';
        when(mockController.itemValue).thenReturn(contentValue.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: contentValue));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: contentValue));
        await tester.pumpAndSettle();

        // Assert - 验证清除按钮显示（当有内容时）
        expect(find.byKey(const Key('edit_item_clear_button')), findsOneWidget);
        expect(find.byIcon(Icons.clear), findsOneWidget);

        // Phase 3: 验证清除按钮点击功能
        await tester.tap(find.byKey(const Key('edit_item_clear_button')));
        await tester.pumpAndSettle();

        // Assert - 验证清除功能调用
        verify(mockController.clearItemValue()).called(1);

        // Phase 4: 模拟清除后的状态验证
        when(mockController.itemValue).thenReturn(''.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: ''));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: ''));
        await tester.pumpAndSettle();

        // Assert - 验证清除后按钮重新隐藏
        expect(find.byKey(const Key('edit_item_clear_button')), findsNothing);
      });

      testWidgets('文本输入实时更新验证 - 验证输入文本时UI实时更新', (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Phase 1: 单个字符输入测试
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), 'A');
        await tester.pumpAndSettle();

        // Assert - 验证TextField显示输入内容
        final singleCharTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(singleCharTextField.controller?.text, 'A');

        // Phase 2: 连续输入测试
        const inputSequence = ['AB', 'ABC', 'ABCD'];
        for (final input in inputSequence) {
          await tester.enterText(find.byKey(const Key('edit_item_text_field')), input);
          await tester.pump(const Duration(milliseconds: 50)); // 短暂延迟模拟实时输入

          // Assert - 验证每次输入都正确显示
          final textField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
          expect(textField.controller?.text, input);
        }

        // Phase 3: 快速输入测试（性能验证）
        const rapidInputs = ['快速1', '快速12', '快速123', '快速1234', '快速12345'];
        for (final input in rapidInputs) {
          await tester.enterText(find.byKey(const Key('edit_item_text_field')), input);
          await tester.pump(); // 无延迟快速更新

          // Assert - 验证快速输入也能正确处理
          final textField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
          expect(textField.controller?.text, input);
        }

        // Phase 4: 特殊字符输入测试
        const specialChars = ['!@#', '日本語', '123数字', 'MIX混合テスト'];
        for (final input in specialChars) {
          await tester.enterText(find.byKey(const Key('edit_item_text_field')), input);
          await tester.pumpAndSettle();

          // Assert - 验证特殊字符正确显示
          final textField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
          expect(textField.controller?.text, input);
        }

        // Phase 5: 清空输入测试
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), '');
        await tester.pumpAndSettle();

        final emptyTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(emptyTextField.controller?.text, '');
      });
    });

    // ========================================
    // Phase 3: 用户交互和方法调用测试
    // ========================================
    group('Phase 3: 用户交互和方法调用测试', () {
      testWidgets('AppBar返回按钮交互 - 验证点击AppBar返回按钮触发controller.back()', (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证返回按钮存在
        expect(find.byKey(const Key('edit_item_back_button')), findsOneWidget);
        expect(find.byIcon(Icons.chevron_left), findsOneWidget);

        // Act - 点击AppBar返回按钮
        await tester.tap(find.byKey(const Key('edit_item_back_button')));
        await tester.pumpAndSettle();

        // Assert - 验证controller.back()被调用
        verify(mockController.back()).called(1);
        verifyNever(mockController.save()); // 确保save没有被误调用
      });

      testWidgets('取消按钮交互 - 验证点击取消按钮触发controller.back()', (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证取消按钮存在
        expect(find.byKey(const Key('edit_item_cancel_button')), findsOneWidget);
        expect(find.text('キャンセル'), findsOneWidget);

        // Act - 点击取消按钮
        await tester.tap(find.byKey(const Key('edit_item_cancel_button')));
        await tester.pumpAndSettle();

        // Assert - 验证controller.back()被调用
        verify(mockController.back()).called(1);
        verifyNever(mockController.save()); // 确保save没有被误调用

        // Assert - 验证按钮外观保持正确
        final cancelButton = tester.widget<ElevatedButton>(find.byKey(const Key('edit_item_cancel_button')));
        expect(cancelButton.style?.backgroundColor?.resolve({}), Colors.white);
      });

      testWidgets('确定按钮交互 - 验证点击确定按钮触发controller.save()', (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证确定按钮存在
        expect(find.byKey(const Key('edit_item_confirm_button')), findsOneWidget);
        expect(find.text('確定'), findsOneWidget);

        // Act - 点击确定按钮
        await tester.tap(find.byKey(const Key('edit_item_confirm_button')));
        await tester.pumpAndSettle();

        // Assert - 验证controller.save()被调用
        verify(mockController.save()).called(1);
        verifyNever(mockController.back()); // 确保back没有被误调用

        // Assert - 验证按钮外观正确
        final confirmButton = tester.widget<ElevatedButton>(find.byKey(const Key('edit_item_confirm_button')));
        expect(confirmButton.child, isA<Text>());
      });

      testWidgets('TextField文本输入交互 - 验证文本输入产生正确UI响应', (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证初始状态
        final initialTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(initialTextField.controller?.text, testItemValue);

        // Phase 1: 基本文本输入
        const basicInput = '基本テスト入力';
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), basicInput);
        await tester.pumpAndSettle();

        // Assert - 验证输入反映在UI
        final basicTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(basicTextField.controller?.text, basicInput);

        // Phase 2: 追加输入测试
        const appendedInput = basicInput + ' 追加';
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), appendedInput);
        await tester.pumpAndSettle();

        final appendedTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(appendedTextField.controller?.text, appendedInput);

        // Phase 3: 替换输入测试
        const replacedInput = 'まったく新しい入力';
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), replacedInput);
        await tester.pumpAndSettle();

        final replacedTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(replacedTextField.controller?.text, replacedInput);

        // Phase 4: 清空输入测试
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), '');
        await tester.pumpAndSettle();

        final emptyTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(emptyTextField.controller?.text, '');
      });

      testWidgets('清除按钮交互 - 验证点击清除按钮触发controller.clearItemValue()', (WidgetTester tester) async {
        // Arrange - 设置有内容的状态，确保清除按钮显示
        const initialContent = '削除対象コンテンツ';
        when(mockController.itemValue).thenReturn(initialContent.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: initialContent));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: initialContent));
        await tester.pumpAndSettle();

        // Assert - 验证清除按钮存在（因为有内容）
        expect(find.byKey(const Key('edit_item_clear_button')), findsOneWidget);
        expect(find.byIcon(Icons.clear), findsOneWidget);

        // Act - 点击清除按钮
        await tester.tap(find.byKey(const Key('edit_item_clear_button')));
        await tester.pumpAndSettle();

        // Assert - 验证controller.clearItemValue()被调用
        verify(mockController.clearItemValue()).called(1);

        // Assert - 验证其他方法没有被误调用
        verifyNever(mockController.save());
        verifyNever(mockController.back());
      });

      testWidgets('清除功能完整性验证 - 验证清除后文本清空且按钮隐藏', (WidgetTester tester) async {
        // Phase 1: 设置有内容的初始状态
        const initialContent = '完全性テストコンテンツ';
        when(mockController.itemValue).thenReturn(initialContent.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: initialContent));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: initialContent));
        await tester.pumpAndSettle();

        // Assert - 验证初始状态：内容存在，清除按钮显示
        expect(find.byKey(const Key('edit_item_clear_button')), findsOneWidget);
        final initialTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(initialTextField.controller?.text, initialContent);

        // Phase 2: 执行清除操作
        await tester.tap(find.byKey(const Key('edit_item_clear_button')));
        await tester.pumpAndSettle();

        // 模拟controller响应清除操作
        when(mockController.itemValue).thenReturn(''.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: ''));

        // Phase 3: 重新渲染以反映清除后状态
        await tester.pumpWidget(createWidgetUnderTest(itemValue: ''));
        await tester.pumpAndSettle();

        // Assert - 验证清除后状态：文本为空，清除按钮隐藏
        expect(find.byKey(const Key('edit_item_clear_button')), findsNothing);
        final clearedTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(clearedTextField.controller?.text, '');

        // Assert - 验证controller方法调用
        verify(mockController.clearItemValue()).called(1);
      });

      testWidgets('连续交互测试 - 验证输入-清除-再输入的循环操作', (WidgetTester tester) async {
        // Phase 1: 初始输入
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        const firstInput = '最初の入力';
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), firstInput);
        await tester.pumpAndSettle();

        // Assert - 验证第一次输入
        final firstTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(firstTextField.controller?.text, firstInput);

        // Phase 2: 模拟有内容时的清除操作
        when(mockController.itemValue).thenReturn(firstInput.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: firstInput));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: firstInput));
        await tester.pumpAndSettle();

        // 执行清除
        expect(find.byKey(const Key('edit_item_clear_button')), findsOneWidget);
        await tester.tap(find.byKey(const Key('edit_item_clear_button')));
        await tester.pumpAndSettle();

        // Phase 3: 模拟清除后状态
        when(mockController.itemValue).thenReturn(''.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: ''));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: ''));
        await tester.pumpAndSettle();

        // Assert - 验证清除状态
        expect(find.byKey(const Key('edit_item_clear_button')), findsNothing);

        // Phase 4: 第二次输入
        const secondInput = '二回目の入力';
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), secondInput);
        await tester.pumpAndSettle();

        // Assert - 验证第二次输入
        final secondTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(secondTextField.controller?.text, secondInput);

        // Phase 5: 再次清除
        when(mockController.itemValue).thenReturn(secondInput.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: secondInput));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: secondInput));
        await tester.pumpAndSettle();

        await tester.tap(find.byKey(const Key('edit_item_clear_button')));
        await tester.pumpAndSettle();

        // Phase 6: 最终输入
        const finalInput = '最終入力';
        when(mockController.itemValue).thenReturn(''.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: ''));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: ''));
        await tester.pumpAndSettle();

        await tester.enterText(find.byKey(const Key('edit_item_text_field')), finalInput);
        await tester.pumpAndSettle();

        // Assert - 验证最终状态
        final finalTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(finalTextField.controller?.text, finalInput);

        // Assert - 验证所有controller方法调用
        verify(mockController.clearItemValue()).called(2); // 两次清除操作

        // Phase 7: 测试保存和取消操作
        await tester.tap(find.byKey(const Key('edit_item_confirm_button')));
        await tester.pumpAndSettle();
        verify(mockController.save()).called(1);

        // 重新加载测试取消
        await tester.pumpWidget(createWidgetUnderTest(itemValue: finalInput));
        await tester.pumpAndSettle();

        await tester.tap(find.byKey(const Key('edit_item_cancel_button')));
        await tester.pumpAndSettle();
        verify(mockController.back()).called(1);
      });
    });

    // ========================================
    // Phase 4: 边界条件和异常处理测试
    // ========================================
    group('Phase 4: 边界条件和异常处理测试', () {
      testWidgets('空字符串参数处理 - 验证空itemTitle和itemValue的正确处理', (WidgetTester tester) async {
        // Phase 1: 测试空标题处理
        when(mockController.itemTitle).thenReturn(''.obs);
        when(mockController.itemValue).thenReturn(testItemValue.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: testItemValue));

        await tester.pumpWidget(createWidgetUnderTest(itemTitle: '', itemValue: testItemValue));
        await tester.pumpAndSettle();

        // Assert - 验证空标题不显示或显示占位符
        expect(find.text(''), findsAtLeastNWidgets(1)); // 可能有多个空Text
        expect(find.byType(Text), findsAtLeastNWidgets(3)); // 至少有AppBar标题等

        // Phase 2: 测试空值处理
        when(mockController.itemTitle).thenReturn(testItemTitle.obs);
        when(mockController.itemValue).thenReturn(''.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: ''));

        await tester.pumpWidget(createWidgetUnderTest(itemTitle: testItemTitle, itemValue: ''));
        await tester.pumpAndSettle();

        // Assert - 验证空值时TextField显示空白和占位符
        final emptyTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(emptyTextField.controller?.text, '');
        expect(emptyTextField.decoration?.hintText, '未設定');

        // Assert - 验证清除按钮不显示（因为内容为空）
        expect(find.byKey(const Key('edit_item_clear_button')), findsNothing);

        // Phase 3: 测试双空处理
        when(mockController.itemTitle).thenReturn(''.obs);
        when(mockController.itemValue).thenReturn(''.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: ''));

        await tester.pumpWidget(createWidgetUnderTest(itemTitle: '', itemValue: ''));
        await tester.pumpAndSettle();

        // Assert - 验证双空状态下Widget仍然正常渲染
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byKey(const Key('edit_item_text_field')), findsOneWidget);

        // Assert - 验证基本功能仍然可用
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), '新しい入力');
        await tester.pumpAndSettle();

        final newTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(newTextField.controller?.text, '新しい入力');
      });

      testWidgets('长文本输入处理 - 验证超长文本的显示和输入处理', (WidgetTester tester) async {
        // Arrange - 生成各种长度的测试文本
        const shortText = '短い';
        const mediumText = 'これは中程度の長さのテキストです。日本語の文字列テストを行います。';
        final longText = 'これは非常に長いテキストです。' * 10; // 300+ 字符
        final veryLongText = '超長テキスト：' + 'あ' * 1000; // 1000+ 字符
        final extremeLongText = '極端に長い：' + '字' * 10000; // 10000+ 字符

        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Phase 1: 短文本处理
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), shortText);
        await tester.pumpAndSettle();

        final shortTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(shortTextField.controller?.text, shortText);

        // Phase 2: 中等长度文本处理
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), mediumText);
        await tester.pumpAndSettle();

        final mediumTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(mediumTextField.controller?.text, mediumText);

        // Phase 3: 长文本处理
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), longText);
        await tester.pumpAndSettle();

        final longTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(longTextField.controller?.text, longText);

        // Assert - 验证UI仍然稳定，没有溢出错误
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byKey(const Key('edit_item_text_field')), findsOneWidget);

        // Phase 4: 超长文本处理
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), veryLongText);
        await tester.pumpAndSettle();

        final veryLongTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(veryLongTextField.controller?.text, veryLongText);

        // Phase 5: 极端长文本处理（性能测试）
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), extremeLongText);
        await tester.pump(); // 使用pump而不是pumpAndSettle以避免性能问题

        final extremeTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(extremeTextField.controller?.text, extremeLongText);

        // Assert - 验证即使极端长文本，基本功能仍可用
        expect(find.byKey(const Key('edit_item_confirm_button')), findsOneWidget);
        expect(find.byKey(const Key('edit_item_cancel_button')), findsOneWidget);

        // Phase 6: 长文本清除测试
        when(mockController.itemValue).thenReturn(extremeLongText.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: extremeLongText));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: extremeLongText));
        await tester.pump();

        // 验证长文本时清除按钮存在且可点击
        expect(find.byKey(const Key('edit_item_clear_button')), findsOneWidget);
        await tester.tap(find.byKey(const Key('edit_item_clear_button')));
        await tester.pump();

        verify(mockController.clearItemValue()).called(1);
      });

      testWidgets('特殊字符处理 - 验证特殊字符和符号的正确处理', (WidgetTester tester) async {
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 定义各种特殊字符测试用例
        final specialCharTests = [
          // 基本符号
          '!@#\\\$%^&*()_+-={}[]|\\\\:";\'<>?,./',
          // 数字组合
          '1234567890',
          // 日文字符
          'ひらがなカタカナ漢字',
          // 中文字符
          '中文测试字符串',
          // 韩文字符
          '한글테스트',
          // emoji表情
          '😀😂🎉🚀💖🌟',
          // 混合特殊字符
          'Test@123!あ中한😀',
          // 空白字符组合（TextField会过滤换行符）
          ' \t\r',
          // HTML特殊字符
          '<>&"\'',
          // URL字符
          'https://example.com/path?param=value&other=123',
          // JSON特殊字符
          '{"key": "value", "array": [1,2,3]}',
          // SQL注入字符（测试安全性）
          "'; DROP TABLE users; --",
          // 特殊空白（TextField不支持换行）
          'Line1 Line2\tTabbed Windows',
          // Unicode字符
          '™©®£¥€°±×÷',
          // 组合字符
          'café naïve résumé',
        ];

        // Phase 1: 逐个测试特殊字符
        for (int i = 0; i < specialCharTests.length; i++) {
          final testText = specialCharTests[i];

          await tester.enterText(find.byKey(const Key('edit_item_text_field')), testText);
          await tester.pumpAndSettle();

          // Assert - 验证特殊字符正确显示
          final textField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
          expect(
            textField.controller?.text,
            testText,
            reason: 'Failed to handle special characters at index $i: "$testText"',
          );

          // Assert - 验证UI保持稳定
          expect(find.byType(EditItemPageWidget), findsOneWidget);
          expect(find.byKey(const Key('edit_item_text_field')), findsOneWidget);
        }

        // Phase 2: 组合特殊字符测试（TextField过滤换行符）
        const combinedSpecialText = '😀Test@123!あ中한 \t <>&"\' {"key":1}';
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), combinedSpecialText);
        await tester.pumpAndSettle();

        final combinedTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(combinedTextField.controller?.text, combinedSpecialText);

        // Phase 3: 特殊字符初始值测试
        const specialInitialValue = '🎯初期値@特殊#文字\$テスト%';
        when(mockController.itemValue).thenReturn(specialInitialValue.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: specialInitialValue));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: specialInitialValue));
        await tester.pumpAndSettle();

        final specialInitialTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(specialInitialTextField.controller?.text, specialInitialValue);

        // Phase 4: 特殊字符标题测试
        const specialTitle = '🔧設定@項目#編集';
        when(mockController.itemTitle).thenReturn(specialTitle.obs);

        await tester.pumpWidget(createWidgetUnderTest(itemTitle: specialTitle));
        await tester.pumpAndSettle();

        expect(find.text(specialTitle), findsOneWidget);

        // Phase 5: 特殊字符操作测试
        when(mockController.itemValue).thenReturn(combinedSpecialText.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: combinedSpecialText));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: combinedSpecialText));
        await tester.pumpAndSettle();

        // 测试清除特殊字符
        await tester.tap(find.byKey(const Key('edit_item_clear_button')));
        await tester.pumpAndSettle();
        verify(mockController.clearItemValue()).called(1);

        // 测试保存特殊字符
        await tester.tap(find.byKey(const Key('edit_item_confirm_button')));
        await tester.pumpAndSettle();
        verify(mockController.save()).called(1);
      });

      testWidgets('Controller方法异常稳定性 - 验证Controller方法异常时UI保持稳定', (WidgetTester tester) async {
        // Phase 1: 模拟save()方法调用验证
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Act - 点击确定按钮，验证方法被调用
        await tester.tap(find.byKey(const Key('edit_item_confirm_button')));
        await tester.pumpAndSettle();

        // Assert - 验证正常情况下UI稳定
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byKey(const Key('edit_item_confirm_button')), findsOneWidget);
        expect(find.byKey(const Key('edit_item_cancel_button')), findsOneWidget);
        verify(mockController.save()).called(1);

        // Phase 2: 验证back()方法调用
        await tester.tap(find.byKey(const Key('edit_item_cancel_button')));
        await tester.pumpAndSettle();

        // Assert - 验证UI保持稳定
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byKey(const Key('edit_item_back_button')), findsOneWidget);
        verify(mockController.back()).called(1);

        // Phase 3: 验证clearItemValue()方法调用
        when(mockController.itemValue).thenReturn('测试内容'.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: '测试内容'));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: '测试内容'));
        await tester.pumpAndSettle();

        await tester.tap(find.byKey(const Key('edit_item_clear_button')));
        await tester.pumpAndSettle();

        // Assert - 验证UI保持稳定
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byKey(const Key('edit_item_text_field')), findsOneWidget);
        verify(mockController.clearItemValue()).called(1);

        // Phase 4: 连续方法调用测试
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 连续触发多个方法调用
        await tester.tap(find.byKey(const Key('edit_item_confirm_button')));
        await tester.pump();
        await tester.tap(find.byKey(const Key('edit_item_cancel_button')));
        await tester.pump();
        await tester.tap(find.byKey(const Key('edit_item_back_button')));
        await tester.pumpAndSettle();

        // Assert - 验证连续操作后UI仍然可用
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byKey(const Key('edit_item_text_field')), findsOneWidget);

        // Phase 5: 功能恢复和稳定性测试
        reset(mockController);
        setupMockController(); // 重新设置正常的Mock行为

        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 验证重置后功能正常
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), '稳定性测试');
        await tester.pumpAndSettle();

        final stabilityTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(stabilityTextField.controller?.text, '稳定性测试');

        // 验证所有基本功能仍然可用
        expect(find.byKey(const Key('edit_item_confirm_button')), findsOneWidget);
        expect(find.byKey(const Key('edit_item_cancel_button')), findsOneWidget);
        expect(find.byKey(const Key('edit_item_back_button')), findsOneWidget);
      });

      testWidgets('快速连续操作稳定性 - 验证快速连续点击和输入的稳定性', (WidgetTester tester) async {
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Phase 1: 快速连续文本输入
        const rapidInputs = ['1', '12', '123', '1234', '12345', '123456', '1234567', '12345678'];

        for (final input in rapidInputs) {
          await tester.enterText(find.byKey(const Key('edit_item_text_field')), input);
          await tester.pump(const Duration(milliseconds: 10)); // 非常短的延迟
        }
        await tester.pumpAndSettle();

        // Assert - 验证最终输入正确
        final rapidTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(rapidTextField.controller?.text, '12345678');

        // Phase 2: 快速连续按钮点击
        when(mockController.itemValue).thenReturn('点击测试'.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: '点击测试'));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: '点击测试'));
        await tester.pumpAndSettle();

        // 快速点击确定按钮多次
        for (int i = 0; i < 5; i++) {
          await tester.tap(find.byKey(const Key('edit_item_confirm_button')));
          await tester.pump(const Duration(milliseconds: 5));
        }
        await tester.pumpAndSettle();

        // Assert - 验证方法被多次调用
        verify(mockController.save()).called(5);

        // Phase 3: 快速切换输入和清除
        await tester.pumpWidget(createWidgetUnderTest(itemValue: '切换测试'));
        await tester.pumpAndSettle();

        for (int i = 0; i < 3; i++) {
          // 快速输入
          await tester.enterText(find.byKey(const Key('edit_item_text_field')), '快速$i');
          await tester.pump(const Duration(milliseconds: 20));

          // 重新设置mock以显示清除按钮
          when(mockController.itemValue).thenReturn('快速$i'.obs);
          when(mockController.textController).thenReturn(TextEditingController(text: '快速$i'));
          await tester.pumpWidget(createWidgetUnderTest(itemValue: '快速$i'));
          await tester.pump(const Duration(milliseconds: 10));

          // 快速清除
          if (find.byKey(const Key('edit_item_clear_button')).evaluate().isNotEmpty) {
            await tester.tap(find.byKey(const Key('edit_item_clear_button')));
            await tester.pump(const Duration(milliseconds: 10));
          }
        }
        await tester.pumpAndSettle();

        // Assert - 验证清除操作被调用
        verify(mockController.clearItemValue()).called(greaterThanOrEqualTo(1));

        // Phase 4: 极速交替操作
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 极快速度的交替操作：输入-取消-输入-确定
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), '极速1');
        await tester.pump();
        await tester.tap(find.byKey(const Key('edit_item_cancel_button')));
        await tester.pump();
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), '极速2');
        await tester.pump();
        await tester.tap(find.byKey(const Key('edit_item_confirm_button')));
        await tester.pumpAndSettle();

        // Assert - 验证所有操作都被正确处理
        verify(mockController.back()).called(greaterThanOrEqualTo(1));
        verify(mockController.save()).called(greaterThanOrEqualTo(1));

        // Phase 5: 压力测试 - 大量快速操作
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 100次快速文本变更
        for (int i = 0; i < 100; i++) {
          await tester.enterText(find.byKey(const Key('edit_item_text_field')), 'stress$i');
          if (i % 10 == 0) {
            await tester.pump(const Duration(milliseconds: 1)); // 偶尔pause一下
          }
        }
        await tester.pumpAndSettle();

        // Assert - 验证压力测试后UI仍然稳定
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byKey(const Key('edit_item_text_field')), findsOneWidget);

        final stressTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(stressTextField.controller?.text, 'stress99');
      });
    });

    // ========================================
    // Phase 5: 集成测试和完整流程验证
    // ========================================
    group('Phase 5: 集成测试和完整流程验证', () {
      testWidgets('完整编辑保存流程 - 验证从打开到保存的完整用户流程', (WidgetTester tester) async {
        // 模拟完整的编辑保存流程：初始化 → 编辑 → 保存

        // Phase 1: 初始化页面
        const initialTitle = '姓名';
        const initialValue = '田中太郎';

        await tester.pumpWidget(createWidgetUnderTest(itemTitle: initialTitle, itemValue: initialValue));
        await tester.pumpAndSettle();

        // Assert - 验证初始状态正确显示
        expect(find.text(initialTitle), findsOneWidget);
        final initialTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(initialTextField.controller?.text, initialValue);

        // Phase 2: 用户编辑操作
        const editedValue = '田中花子';
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), editedValue);
        await tester.pumpAndSettle();

        // Assert - 验证编辑后的状态
        final editedTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(editedTextField.controller?.text, editedValue);

        // Phase 3: 执行保存操作
        await tester.tap(find.byKey(const Key('edit_item_confirm_button')));
        await tester.pumpAndSettle();

        // Assert - 验证保存操作被执行
        verify(mockController.save()).called(1);
        verifyNever(mockController.back()); // 确保没有意外调用取消操作

        // Phase 4: 验证完整流程的一致性
        // 重新创建widget验证状态保持
        await tester.pumpWidget(
          createWidgetUnderTest(
            itemTitle: initialTitle,
            itemValue: editedValue, // 使用编辑后的值
          ),
        );
        await tester.pumpAndSettle();

        final finalTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(finalTextField.controller?.text, editedValue);

        // Phase 5: 验证UI组件状态完整性
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.text('編集'), findsOneWidget);
        expect(find.byKey(const Key('edit_item_back_button')), findsOneWidget);
        expect(find.byKey(const Key('edit_item_confirm_button')), findsOneWidget);
        expect(find.byKey(const Key('edit_item_cancel_button')), findsOneWidget);
      });

      testWidgets('完整取消流程 - 验证从打开到取消的完整用户流程', (WidgetTester tester) async {
        // 模拟完整的编辑取消流程：初始化 → 编辑 → 取消

        // Phase 1: 初始化页面
        const initialTitle = '住所';
        const initialValue = '東京都渋谷区';

        await tester.pumpWidget(createWidgetUnderTest(itemTitle: initialTitle, itemValue: initialValue));
        await tester.pumpAndSettle();

        // Assert - 验证初始状态
        expect(find.text(initialTitle), findsOneWidget);
        final initialTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(initialTextField.controller?.text, initialValue);

        // Phase 2: 用户编辑操作（多种编辑）
        const firstEdit = '大阪府大阪市';
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), firstEdit);
        await tester.pumpAndSettle();

        const secondEdit = '京都府京都市';
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), secondEdit);
        await tester.pumpAndSettle();

        // Assert - 验证编辑状态
        final editedTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(editedTextField.controller?.text, secondEdit);

        // Phase 3: 测试多种取消方式
        // 3a: 通过取消按钮取消
        await tester.tap(find.byKey(const Key('edit_item_cancel_button')));
        await tester.pumpAndSettle();

        verify(mockController.back()).called(1);
        verifyNever(mockController.save()); // 确保没有保存

        // Phase 4: 重新加载测试AppBar返回按钮取消
        reset(mockController);
        setupMockController();

        await tester.pumpWidget(createWidgetUnderTest(itemTitle: initialTitle, itemValue: initialValue));
        await tester.pumpAndSettle();

        // 再次编辑
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), '福岡県福岡市');
        await tester.pumpAndSettle();

        // 通过AppBar返回按钮取消
        await tester.tap(find.byKey(const Key('edit_item_back_button')));
        await tester.pumpAndSettle();

        verify(mockController.back()).called(1);
        verifyNever(mockController.save());

        // Phase 5: 验证取消后的原始值应该被保留（通过controller.back()的逻辑）
        // 这里我们验证UI在取消操作后仍然稳定
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byKey(const Key('edit_item_text_field')), findsOneWidget);
      });

      testWidgets('编辑-清除-重编辑循环 - 验证多次编辑清除的循环流程', (WidgetTester tester) async {
        // 简化的循环测试，验证编辑和清除功能的循环使用

        // Phase 1: 第一次编辑和清除循环
        const initialValue = '090-1234-5678';
        when(mockController.itemValue).thenReturn(initialValue.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: initialValue));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: initialValue));
        await tester.pumpAndSettle();

        // 第一次清除
        expect(find.byKey(const Key('edit_item_clear_button')), findsOneWidget);
        await tester.tap(find.byKey(const Key('edit_item_clear_button')));
        await tester.pumpAndSettle();

        // Phase 2: 第二次编辑和清除循环
        reset(mockController); // 重置计数器
        setupMockController();

        const secondValue = '080-9876-5432';
        when(mockController.itemValue).thenReturn(secondValue.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: secondValue));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: secondValue));
        await tester.pumpAndSettle();

        // 第二次清除
        expect(find.byKey(const Key('edit_item_clear_button')), findsOneWidget);
        await tester.tap(find.byKey(const Key('edit_item_clear_button')));
        await tester.pumpAndSettle();

        // Phase 3: 第三次编辑和清除循环
        reset(mockController); // 重置计数器
        setupMockController();

        const thirdValue = '070-1111-2222';
        when(mockController.itemValue).thenReturn(thirdValue.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: thirdValue));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: thirdValue));
        await tester.pumpAndSettle();

        // 第三次清除
        expect(find.byKey(const Key('edit_item_clear_button')), findsOneWidget);
        await tester.tap(find.byKey(const Key('edit_item_clear_button')));
        await tester.pumpAndSettle();

        // 验证清除功能正常工作
        verify(mockController.clearItemValue()).called(1);

        // Phase 4: 最终编辑和保存
        await tester.pumpWidget(createWidgetUnderTest(itemValue: ''));
        await tester.pumpAndSettle();

        const finalEdit = '03-5555-6666';
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), finalEdit);
        await tester.pumpAndSettle();

        // 最终保存
        await tester.tap(find.byKey(const Key('edit_item_confirm_button')));
        await tester.pumpAndSettle();

        // Assert - 验证保存功能正常工作
        verify(mockController.save()).called(1);

        // 验证UI在整个循环过程中保持稳定
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byKey(const Key('edit_item_text_field')), findsOneWidget);

        final finalTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(finalTextField.controller?.text, finalEdit);
      });

      testWidgets('不同参数组合流程 - 验证各种参数组合的完整流程', (WidgetTester tester) async {
        // 测试各种不同的参数组合，验证Widget在所有情况下都能正常工作

        // 测试用例定义
        final testCases = [
          // 基本日文组合
          {'title': '名前', 'value': '田中太郎', 'description': '基本日文姓名'},
          // 英文组合
          {'title': 'Name', 'value': 'John Smith', 'description': '英文姓名'},
          // 数字组合
          {'title': '年齢', 'value': '30', 'description': '数字值'},
          // 长文本组合
          {'title': '住所', 'value': '東京都渋谷区道玄坂一丁目2番3号渋谷ビル4階', 'description': '长地址'},
          // 空值组合
          {'title': 'メール', 'value': '', 'description': '空邮箱'},
          // 特殊字符组合
          {'title': 'パスワード', 'value': 'P@ssw0rd123!', 'description': '特殊字符密码'},
          // emoji组合
          {'title': '気分', 'value': '😊🎉✨', 'description': 'emoji表情'},
          // 混合语言组合
          {'title': 'Profile', 'value': 'エンジニア Engineer 工程师', 'description': '多语言混合'},
        ];

        for (int i = 0; i < testCases.length; i++) {
          final testCase = testCases[i];
          final title = testCase['title'] as String;
          final value = testCase['value'] as String;
          final description = testCase['description'] as String;

          // Phase 1: 测试初始化
          await tester.pumpWidget(createWidgetUnderTest(itemTitle: title, itemValue: value));
          await tester.pumpAndSettle();

          // Assert - 验证初始状态
          expect(find.text(title), findsOneWidget, reason: 'Failed for $description: title not found');

          final initialTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
          expect(initialTextField.controller?.text, value, reason: 'Failed for $description: initial value mismatch');

          // Phase 2: 测试编辑操作
          final editedValue = '$value-編集$i';
          await tester.enterText(find.byKey(const Key('edit_item_text_field')), editedValue);
          await tester.pumpAndSettle();

          final editedTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
          expect(
            editedTextField.controller?.text,
            editedValue,
            reason: 'Failed for $description: edited value mismatch',
          );

          // Phase 3: 测试保存操作
          await tester.tap(find.byKey(const Key('edit_item_confirm_button')));
          await tester.pumpAndSettle();

          // Assert - 验证基本UI组件在所有测试用例中都存在
          expect(find.byType(EditItemPageWidget), findsOneWidget, reason: 'Failed for $description: widget not found');
          expect(
            find.byKey(const Key('edit_item_text_field')),
            findsOneWidget,
            reason: 'Failed for $description: text field not found',
          );
        }

        // 验证所有测试用例都执行了保存操作
        verify(mockController.save()).called(testCases.length);

        // Phase 5后重置mock为特殊组合测试准备
        reset(mockController);
        setupMockController();

        // Phase 5: 特殊组合压力测试
        final extremeTitle = '🔧極端テスト項目@#\\\$%^&*()_+-={}[]|\\\\:";\'<>?,./' + 'あ' * 100;
        final extremeValue = '🚀極端値テスト：' + '字' * 500 + '@domain.com';

        await tester.pumpWidget(createWidgetUnderTest(itemTitle: extremeTitle, itemValue: extremeValue));
        await tester.pump(); // 使用pump而不是pumpAndSettle以避免极端内容的性能问题

        // Assert - 即使极端参数组合，UI也应该保持稳定
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(find.byKey(const Key('edit_item_text_field')), findsOneWidget);
        expect(find.byKey(const Key('edit_item_confirm_button')), findsOneWidget);
        expect(find.byKey(const Key('edit_item_cancel_button')), findsOneWidget);
      });

      testWidgets('可访问性验证 - 验证UI元素的可访问性和语义化', (WidgetTester tester) async {
        // 全面验证UI元素的可访问性、语义化和用户体验

        await tester.pumpWidget(createWidgetUnderTest(itemTitle: 'アクセシビリティテスト', itemValue: 'テスト値'));
        await tester.pumpAndSettle();

        // Phase 1: 基本语义化验证
        // 验证主要UI元素的存在和类型
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(TextField), findsOneWidget);
        expect(find.byType(ElevatedButton), findsNWidgets(2)); // 取消和确定按钮

        // Phase 2: Key可访问性验证
        // 验证所有关键元素都有唯一的Key用于自动化测试
        final criticalKeys = [
          'edit_item_back_button',
          'edit_item_text_field',
          'edit_item_cancel_button',
          'edit_item_confirm_button',
        ];

        for (final keyString in criticalKeys) {
          expect(find.byKey(Key(keyString)), findsOneWidget, reason: 'Accessibility key "$keyString" not found');
        }

        // Phase 3: 文本可读性验证
        // 验证所有用户可见文本都正确显示
        final userVisibleTexts = [
          '編集', // AppBar标题
          'アクセシビリティテスト', // 项目标题
          'キャンセル', // 取消按钮
          '確定', // 确定按钮
        ];

        for (final text in userVisibleTexts) {
          expect(find.text(text), findsOneWidget, reason: 'User-visible text "$text" not found');
        }

        // Phase 4: 交互元素可访问性验证
        // 验证所有交互元素都可以被正确识别和操作

        // 4a: 按钮可访问性
        final buttons = find.byType(ElevatedButton);
        expect(buttons, findsNWidgets(2));

        // 验证按钮有正确的文本内容
        expect(
          find.descendant(of: find.byKey(const Key('edit_item_cancel_button')), matching: find.text('キャンセル')),
          findsOneWidget,
        );

        expect(
          find.descendant(of: find.byKey(const Key('edit_item_confirm_button')), matching: find.text('確定')),
          findsOneWidget,
        );

        // 4b: TextField可访问性
        final textField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(textField.decoration, isNotNull); // 有装饰配置
        expect(textField.controller, isNotNull); // 有控制器
        if (textField.decoration != null) {
          expect(textField.decoration!.hintText, isNotNull); // 有提示文本
        }

        // Phase 5: 导航可访问性验证
        // 验证导航元素的可访问性

        // 5a: AppBar导航
        expect(find.byIcon(Icons.chevron_left), findsOneWidget); // 返回图标
        final backButton = find.byKey(const Key('edit_item_back_button'));
        expect(backButton, findsOneWidget);

        // 5b: 验证导航按钮可点击
        await tester.tap(backButton);
        await tester.pumpAndSettle();
        verify(mockController.back()).called(1);

        // Phase 6: 内容可访问性验证
        // 重新加载以测试其他交互
        reset(mockController);
        setupMockController();

        await tester.pumpWidget(createWidgetUnderTest(itemTitle: '可访问性验证', itemValue: '测试内容'));
        await tester.pumpAndSettle();

        // 6a: TextField输入可访问性
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), '可访问性输入测试');
        await tester.pumpAndSettle();

        final accessibilityTextField = tester.widget<TextField>(find.byKey(const Key('edit_item_text_field')));
        expect(accessibilityTextField.controller?.text, '可访问性输入测试');

        // 6b: 清除按钮可访问性（当有内容时）
        when(mockController.itemValue).thenReturn('可访问性输入测试'.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: '可访问性输入测试'));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: '可访问性输入测试'));
        await tester.pumpAndSettle();

        // 验证清除按钮的可访问性
        expect(find.byKey(const Key('edit_item_clear_button')), findsOneWidget);
        expect(find.byIcon(Icons.clear), findsOneWidget);

        await tester.tap(find.byKey(const Key('edit_item_clear_button')));
        await tester.pumpAndSettle();
        verify(mockController.clearItemValue()).called(1);

        // Phase 7: 完整流程可访问性验证
        // 验证完整用户流程的可访问性

        when(mockController.itemValue).thenReturn(''.obs);
        when(mockController.textController).thenReturn(TextEditingController(text: ''));

        await tester.pumpWidget(createWidgetUnderTest(itemValue: ''));
        await tester.pumpAndSettle();

        // 完整流程：输入 → 保存
        await tester.enterText(find.byKey(const Key('edit_item_text_field')), '完整流程测试');
        await tester.pumpAndSettle();

        await tester.tap(find.byKey(const Key('edit_item_confirm_button')));
        await tester.pumpAndSettle();

        verify(mockController.save()).called(1);

        // Phase 8: UI一致性和标准合规性验证
        // 验证UI遵循Flutter和Material Design的可访问性标准

        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 8a: 材质设计合规性
        expect(find.byType(Material), findsAtLeastNWidgets(1)); // 使用Material组件
        expect(find.byType(Scaffold), findsOneWidget); // 使用标准Scaffold

        // 8b: 主题一致性
        final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
        // 注意：Scaffold的backgroundColor可能为null（使用主题默认）这是正常的
        // expect(scaffold.backgroundColor, isNotNull); // 背景色可能使用主题默认值

        // 8c: 响应式布局验证
        expect(find.byType(Obx), findsOneWidget); // 使用响应式组件
        expect(find.byType(Column), findsAtLeastNWidgets(1)); // 合理的布局结构
        expect(find.byType(Row), findsAtLeastNWidgets(1));

        // 最终断言：验证整个组件的可访问性完整性
        expect(find.byType(EditItemPageWidget), findsOneWidget);
        expect(tester.testTextInput.isVisible, isTrue); // 文本输入可见
      });
    });
  });
}

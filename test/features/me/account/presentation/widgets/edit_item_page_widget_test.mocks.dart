// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/account/presentation/widgets/edit_item_page_widget_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:ui' as _i7;

import 'package:asset_force_mobile_v2/features/me/account/presentation/controllers/edit_item_controller.dart'
    as _i4;
import 'package:flutter/material.dart' as _i3;
import 'package:get/get.dart' as _i2;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeRxString_0 extends _i1.SmartFake implements _i2.RxString {
  _FakeRxString_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTextEditingController_1 extends _i1.SmartFake
    implements _i3.TextEditingController {
  _FakeTextEditingController_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_2<T> extends _i1.SmartFake
    implements _i2.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [EditItemController].
///
/// See the documentation for Mockito's code generation for more information.
class MockEditItemController extends _i1.Mock
    implements _i4.EditItemController {
  @override
  _i2.RxString get itemTitle =>
      (super.noSuchMethod(
            Invocation.getter(#itemTitle),
            returnValue: _FakeRxString_0(this, Invocation.getter(#itemTitle)),
            returnValueForMissingStub: _FakeRxString_0(
              this,
              Invocation.getter(#itemTitle),
            ),
          )
          as _i2.RxString);

  @override
  set itemTitle(_i2.RxString? _itemTitle) => super.noSuchMethod(
    Invocation.setter(#itemTitle, _itemTitle),
    returnValueForMissingStub: null,
  );

  @override
  _i2.RxString get itemValue =>
      (super.noSuchMethod(
            Invocation.getter(#itemValue),
            returnValue: _FakeRxString_0(this, Invocation.getter(#itemValue)),
            returnValueForMissingStub: _FakeRxString_0(
              this,
              Invocation.getter(#itemValue),
            ),
          )
          as _i2.RxString);

  @override
  set itemValue(_i2.RxString? _itemValue) => super.noSuchMethod(
    Invocation.setter(#itemValue, _itemValue),
    returnValueForMissingStub: null,
  );

  @override
  String get originalValue =>
      (super.noSuchMethod(
            Invocation.getter(#originalValue),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.getter(#originalValue),
            ),
            returnValueForMissingStub: _i5.dummyValue<String>(
              this,
              Invocation.getter(#originalValue),
            ),
          )
          as String);

  @override
  set originalValue(String? _originalValue) => super.noSuchMethod(
    Invocation.setter(#originalValue, _originalValue),
    returnValueForMissingStub: null,
  );

  @override
  _i3.TextEditingController get textController =>
      (super.noSuchMethod(
            Invocation.getter(#textController),
            returnValue: _FakeTextEditingController_1(
              this,
              Invocation.getter(#textController),
            ),
            returnValueForMissingStub: _FakeTextEditingController_1(
              this,
              Invocation.getter(#textController),
            ),
          )
          as _i3.TextEditingController);

  @override
  set textController(_i3.TextEditingController? _textController) =>
      super.noSuchMethod(
        Invocation.setter(#textController, _textController),
        returnValueForMissingStub: null,
      );

  @override
  _i2.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_2<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_2<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_2<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_2<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  void setItemValues(String? title, String? value) => super.noSuchMethod(
    Invocation.method(#setItemValues, [title, value]),
    returnValueForMissingStub: null,
  );

  @override
  void save() => super.noSuchMethod(
    Invocation.method(#save, []),
    returnValueForMissingStub: null,
  );

  @override
  void back() => super.noSuchMethod(
    Invocation.method(#back, []),
    returnValueForMissingStub: null,
  );

  @override
  void clearItemValue() => super.noSuchMethod(
    Invocation.method(#clearItemValue, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Disposer addListener(_i6.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i6.Disposer);

  @override
  void removeListener(_i7.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i7.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Disposer addListenerId(Object? key, _i6.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i6.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [InternalFinalCallback].
///
/// See the documentation for Mockito's code generation for more information.
class MockInternalFinalCallback<T> extends _i1.Mock
    implements _i2.InternalFinalCallback<T> {
  @override
  T call() =>
      (super.noSuchMethod(
            Invocation.method(#call, []),
            returnValue: _i5.dummyValue<T>(this, Invocation.method(#call, [])),
            returnValueForMissingStub: _i5.dummyValue<T>(
              this,
              Invocation.method(#call, []),
            ),
          )
          as T);
}

// Mocks generated by Mockito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/account/presentation/pages/account_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i9;
import 'dart:ui' as _i12;

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart'
    as _i10;
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i4;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i5;
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/me/account/domain/usecase/account_data_usercase.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/me/account/presentation/controllers/account_controller.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/me/account/presentation/models/account_ui_state.dart'
    as _i8;
import 'package:get/get.dart' as _i2;
import 'package:get/get_state_manager/src/simple/list_notifier.dart' as _i11;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeRx_0<T> extends _i1.SmartFake implements _i2.Rx<T> {
  _FakeRx_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAccountDataUseCase_1 extends _i1.SmartFake
    implements _i3.AccountDataUseCase {
  _FakeAccountDataUseCase_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDialogService_2 extends _i1.SmartFake implements _i4.DialogService {
  _FakeDialogService_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNavigationService_3 extends _i1.SmartFake
    implements _i5.NavigationService {
  _FakeNavigationService_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIStorageUtils_4 extends _i1.SmartFake implements _i6.IStorageUtils {
  _FakeIStorageUtils_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRxBool_5 extends _i1.SmartFake implements _i2.RxBool {
  _FakeRxBool_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInternalFinalCallback_6<T> extends _i1.SmartFake
    implements _i2.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AccountController].
///
/// See the documentation for Mockito's code generation for more information.
class MockAccountController extends _i1.Mock implements _i7.AccountController {
  @override
  _i2.Rx<_i8.AccountUIState> get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeRx_0<_i8.AccountUIState>(
              this,
              Invocation.getter(#state),
            ),
            returnValueForMissingStub: _FakeRx_0<_i8.AccountUIState>(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i2.Rx<_i8.AccountUIState>);

  @override
  set state(_i2.Rx<_i8.AccountUIState>? _state) => super.noSuchMethod(
    Invocation.setter(#state, _state),
    returnValueForMissingStub: null,
  );

  @override
  _i3.AccountDataUseCase get useCase =>
      (super.noSuchMethod(
            Invocation.getter(#useCase),
            returnValue: _FakeAccountDataUseCase_1(
              this,
              Invocation.getter(#useCase),
            ),
            returnValueForMissingStub: _FakeAccountDataUseCase_1(
              this,
              Invocation.getter(#useCase),
            ),
          )
          as _i3.AccountDataUseCase);

  @override
  set useCase(_i3.AccountDataUseCase? _useCase) => super.noSuchMethod(
    Invocation.setter(#useCase, _useCase),
    returnValueForMissingStub: null,
  );

  @override
  _i4.DialogService get dialogService =>
      (super.noSuchMethod(
            Invocation.getter(#dialogService),
            returnValue: _FakeDialogService_2(
              this,
              Invocation.getter(#dialogService),
            ),
            returnValueForMissingStub: _FakeDialogService_2(
              this,
              Invocation.getter(#dialogService),
            ),
          )
          as _i4.DialogService);

  @override
  set dialogService(_i4.DialogService? _dialogService) => super.noSuchMethod(
    Invocation.setter(#dialogService, _dialogService),
    returnValueForMissingStub: null,
  );

  @override
  _i5.NavigationService get navigationService =>
      (super.noSuchMethod(
            Invocation.getter(#navigationService),
            returnValue: _FakeNavigationService_3(
              this,
              Invocation.getter(#navigationService),
            ),
            returnValueForMissingStub: _FakeNavigationService_3(
              this,
              Invocation.getter(#navigationService),
            ),
          )
          as _i5.NavigationService);

  @override
  set navigationService(_i5.NavigationService? _navigationService) =>
      super.noSuchMethod(
        Invocation.setter(#navigationService, _navigationService),
        returnValueForMissingStub: null,
      );

  @override
  _i6.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_4(
              this,
              Invocation.getter(#storageUtils),
            ),
            returnValueForMissingStub: _FakeIStorageUtils_4(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i6.IStorageUtils);

  @override
  set storageUtils(_i6.IStorageUtils? _storageUtils) => super.noSuchMethod(
    Invocation.setter(#storageUtils, _storageUtils),
    returnValueForMissingStub: null,
  );

  @override
  _i2.RxBool get isLoading =>
      (super.noSuchMethod(
            Invocation.getter(#isLoading),
            returnValue: _FakeRxBool_5(this, Invocation.getter(#isLoading)),
            returnValueForMissingStub: _FakeRxBool_5(
              this,
              Invocation.getter(#isLoading),
            ),
          )
          as _i2.RxBool);

  @override
  set isLoading(_i2.RxBool? _isLoading) => super.noSuchMethod(
    Invocation.setter(#isLoading, _isLoading),
    returnValueForMissingStub: null,
  );

  @override
  _i2.RxBool get loadingError =>
      (super.noSuchMethod(
            Invocation.getter(#loadingError),
            returnValue: _FakeRxBool_5(this, Invocation.getter(#loadingError)),
            returnValueForMissingStub: _FakeRxBool_5(
              this,
              Invocation.getter(#loadingError),
            ),
          )
          as _i2.RxBool);

  @override
  set loadingError(_i2.RxBool? _loadingError) => super.noSuchMethod(
    Invocation.setter(#loadingError, _loadingError),
    returnValueForMissingStub: null,
  );

  @override
  _i2.RxBool get isLoadingError =>
      (super.noSuchMethod(
            Invocation.getter(#isLoadingError),
            returnValue: _FakeRxBool_5(
              this,
              Invocation.getter(#isLoadingError),
            ),
            returnValueForMissingStub: _FakeRxBool_5(
              this,
              Invocation.getter(#isLoadingError),
            ),
          )
          as _i2.RxBool);

  @override
  set isLoadingError(_i2.RxBool? _isLoadingError) => super.noSuchMethod(
    Invocation.setter(#isLoadingError, _isLoadingError),
    returnValueForMissingStub: null,
  );

  @override
  _i2.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_6<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(
            Invocation.getter(#hasListeners),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  int get listeners =>
      (super.noSuchMethod(
            Invocation.getter(#listeners),
            returnValue: 0,
            returnValueForMissingStub: 0,
          )
          as int);

  @override
  _i9.Future<void> fetchData(dynamic params) =>
      (super.noSuchMethod(
            Invocation.method(#fetchData, [params]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> toLocationSelectPage() =>
      (super.noSuchMethod(
            Invocation.method(#toLocationSelectPage, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> editLastName() =>
      (super.noSuchMethod(
            Invocation.method(#editLastName, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> editLastNameKana() =>
      (super.noSuchMethod(
            Invocation.method(#editLastNameKana, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> editFirstName() =>
      (super.noSuchMethod(
            Invocation.method(#editFirstName, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> editFirstNameKana() =>
      (super.noSuchMethod(
            Invocation.method(#editFirstNameKana, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> saveUserInfo() =>
      (super.noSuchMethod(
            Invocation.method(#saveUserInfo, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  bool validateNormalText(String? data) =>
      (super.noSuchMethod(
            Invocation.method(#validateNormalText, [data]),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool validateKatakanaText(String? name) =>
      (super.noSuchMethod(
            Invocation.method(#validateKatakanaText, [name]),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void loadDataOnInit() => super.noSuchMethod(
    Invocation.method(#loadDataOnInit, []),
    returnValueForMissingStub: null,
  );

  @override
  _i9.Future<void> loadDataWithLoadingStatus({dynamic params = null}) =>
      (super.noSuchMethod(
            Invocation.method(#loadDataWithLoadingStatus, [], {
              #params: params,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    _i10.ErrorHandlingMode? mode = _i10.ErrorHandlingMode.dialog,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#handleException, [exception, stackTrace, mode]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> showLoading() =>
      (super.noSuchMethod(
            Invocation.method(#showLoading, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  void hideLoading() => super.noSuchMethod(
    Invocation.method(#hideLoading, []),
    returnValueForMissingStub: null,
  );

  @override
  bool shouldShowNavigationBar() =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowNavigationBar, []),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  void configureNavigationBarVisibility() => super.noSuchMethod(
    Invocation.method(#configureNavigationBarVisibility, []),
    returnValueForMissingStub: null,
  );

  @override
  void resetOverlay() => super.noSuchMethod(
    Invocation.method(#resetOverlay, []),
    returnValueForMissingStub: null,
  );

  @override
  void update([List<Object>? ids, bool? condition = true]) =>
      super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Disposer addListener(_i11.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListener, [listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i11.Disposer);

  @override
  void removeListener(_i12.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void refreshGroup(Object? id) => super.noSuchMethod(
    Invocation.method(#refreshGroup, [id]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyChildrens() => super.noSuchMethod(
    Invocation.method(#notifyChildrens, []),
    returnValueForMissingStub: null,
  );

  @override
  void removeListenerId(Object? id, _i12.VoidCallback? listener) =>
      super.noSuchMethod(
        Invocation.method(#removeListenerId, [id, listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Disposer addListenerId(Object? key, _i11.GetStateUpdate? listener) =>
      (super.noSuchMethod(
            Invocation.method(#addListenerId, [key, listener]),
            returnValue: () {},
            returnValueForMissingStub: () {},
          )
          as _i11.Disposer);

  @override
  void disposeId(Object? id) => super.noSuchMethod(
    Invocation.method(#disposeId, [id]),
    returnValueForMissingStub: null,
  );
}

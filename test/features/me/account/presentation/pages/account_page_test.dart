import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/me/account/presentation/controllers/account_controller.dart';
import 'package:asset_force_mobile_v2/features/me/account/presentation/models/account_ui_state.dart';
import 'package:asset_force_mobile_v2/features/me/account/presentation/pages/account_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 生成AccountController的Mock
@GenerateNiceMocks([MockSpec<AccountController>()])
import 'account_page_test.mocks.dart';

class MockInternalFinalCallback<T> extends Mock implements InternalFinalCallback<T> {}

void main() {
  final mockInternalFinalCallback = MockInternalFinalCallback<void>();
  late MockAccountController mockController;
  late AccountUIState normalUserState;
  late AccountUIState fullFeatureUserState;
  late AccountUIState edgeCaseUserState;

  // ========================================
  // 测试数据设置方法
  // ========================================
  void setupTestStates() {
    // 普通用户状态（基础功能）
    normalUserState = AccountUIState(1);
    normalUserState.userName = '<EMAIL>';
    normalUserState.lastName = '田中';
    normalUserState.firstName = '太郎';
    normalUserState.lastNameKana = 'タナカ';
    normalUserState.firstNameKana = 'タロウ';
    normalUserState.roleName = '一般ユーザー';
    normalUserState.recoverableLimit = '2024/12/31';
    normalUserState.enableTwoStep = false;
    normalUserState.hasAssetLocation = false;

    // 全功能用户状态（所有功能开启）
    fullFeatureUserState = AccountUIState(2);
    fullFeatureUserState.userName = '<EMAIL>';
    fullFeatureUserState.lastName = '佐藤';
    fullFeatureUserState.firstName = '花子';
    fullFeatureUserState.lastNameKana = 'サトウ';
    fullFeatureUserState.firstNameKana = 'ハナコ';
    fullFeatureUserState.roleName = '管理者';
    fullFeatureUserState.recoverableLimit = '2025/03/31';
    fullFeatureUserState.enableTwoStep = true;
    fullFeatureUserState.hasAssetLocation = true;
    fullFeatureUserState.nationCode = '+81';
    fullFeatureUserState.tel = '090-1234-5678';
    fullFeatureUserState.location = 'Tokyo Office';
    fullFeatureUserState.tmpLocation = 'Osaka Branch'; // 临时场所

    // 边界条件状态（测试边界情况）
    edgeCaseUserState = AccountUIState(3);
    edgeCaseUserState.userName = '<EMAIL>';
    edgeCaseUserState.lastName = ''; // 空必填字段
    edgeCaseUserState.firstName = ''; // 空必填字段
    edgeCaseUserState.lastNameKana = '';
    edgeCaseUserState.firstNameKana = '';
    edgeCaseUserState.roleName = '';
    edgeCaseUserState.recoverableLimit = '';
    edgeCaseUserState.enableTwoStep = true;
    edgeCaseUserState.hasAssetLocation = true;
    edgeCaseUserState.nationCode = '';
    edgeCaseUserState.tel = '';
    edgeCaseUserState.location = '';
  }

  void setupMockController() {
    // 配置状态属性
    when(mockController.state).thenReturn(normalUserState.obs);

    // 配置GetX生命周期方法
    when(mockController.onInit()).thenAnswer((_) async {});
    when(mockController.onClose()).thenAnswer((_) async {});
    when(mockController.onStart).thenReturn(mockInternalFinalCallback);
    when(mockController.onDelete).thenReturn(mockInternalFinalCallback); // 添加onDelete stub

    // 配置LoadingController继承的属性
    when(mockController.isLoading).thenReturn(false.obs);
    when(mockController.loadingError).thenReturn(false.obs);

    // 配置编辑方法
    when(mockController.editLastName()).thenAnswer((_) async {});
    when(mockController.editFirstName()).thenAnswer((_) async {});
    when(mockController.editLastNameKana()).thenAnswer((_) async {});
    when(mockController.editFirstNameKana()).thenAnswer((_) async {});

    // 配置导航方法
    when(mockController.toLocationSelectPage()).thenAnswer((_) async {});
    when(mockController.saveUserInfo()).thenAnswer((_) async {});

    // 配置数据加载方法
    when(mockController.fetchData(any)).thenAnswer((_) async {});
    when(mockController.loadDataWithLoadingStatus()).thenAnswer((_) async {});

    // 配置验证方法（测试用的@visibleForTesting方法）
    when(mockController.validateNormalText(any)).thenReturn(false);
    when(mockController.validateKatakanaText(any)).thenReturn(true);
  }

  // 创建测试Widget的辅助方法
  Widget createWidgetUnderTest({AccountUIState? userState}) {
    // 设置Mock状态
    final state = userState ?? normalUserState;
    when(mockController.state).thenReturn(state.obs);

    // 注册Mock Controller
    Get.put<AccountController>(mockController);

    return GetMaterialApp(theme: AppTheme.lightTheme, home: const AccountPage());
  }

  // 更新Mock状态的辅助方法
  void updateMockState(AccountUIState newState) {
    when(mockController.state).thenReturn(newState.obs);
  }

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    Get.testMode = true;
    Get.reset(); // 确保干净的测试环境

    // 创建Mock实例
    mockController = MockAccountController();

    // 创建测试数据状态
    setupTestStates();

    // 配置Mock Controller的基本行为
    setupMockController();
  });

  tearDown(() {
    // 清理资源
    reset(mockController);
    clearInteractions(mockController);
    Get.reset();
  });

  // ========================================
  // 基础框架验证测试
  // ========================================
  group('测试基础设施验证', () {
    testWidgets('Mock设置验证 - 确保基础Mock配置正确', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 验证页面基本渲染
      expect(find.byType(AccountPage), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);

      // 验证Mock Controller正确注册
      final injectedController = Get.find<AccountController>();
      expect(injectedController, isA<MockAccountController>());
      expect(injectedController, mockController);
    });

    testWidgets('测试数据状态验证 - 确保测试状态对象正确', (WidgetTester tester) async {
      // Act & Assert - 普通用户状态
      await tester.pumpWidget(createWidgetUnderTest(userState: normalUserState));
      await tester.pumpAndSettle();
      expect(find.byType(AccountPage), findsOneWidget);

      // Act & Assert - 全功能用户状态
      await tester.pumpWidget(createWidgetUnderTest(userState: fullFeatureUserState));
      await tester.pumpAndSettle();
      expect(find.byType(AccountPage), findsOneWidget);

      // Act & Assert - 边界条件状态
      await tester.pumpWidget(createWidgetUnderTest(userState: edgeCaseUserState));
      await tester.pumpAndSettle();
      expect(find.byType(AccountPage), findsOneWidget);
    });

    testWidgets('状态切换验证 - 确保状态可以动态切换', (WidgetTester tester) async {
      // Arrange - 初始状态
      await tester.pumpWidget(createWidgetUnderTest(userState: normalUserState));
      await tester.pumpAndSettle();

      // Act - 切换到全功能状态
      when(mockController.state).thenReturn(fullFeatureUserState.obs);
      await tester.pump();

      // Assert - 确保切换成功
      expect(find.byType(AccountPage), findsOneWidget);

      // Act - 切换到边界状态
      when(mockController.state).thenReturn(edgeCaseUserState.obs);
      await tester.pump();

      // Assert - 确保切换成功
      expect(find.byType(AccountPage), findsOneWidget);
    });

    testWidgets('AppBar基础验证 - 确保基本UI结构存在', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 验证基本结构
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('アカウント情報'), findsOneWidget);
      expect(find.byKey(const Key('account_back_button')), findsOneWidget);
      expect(find.byKey(const Key('account_save_button')), findsOneWidget);
    });

    testWidgets('新增Key验证 - 确保新添加的Key存在', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.pumpAndSettle();

      // Assert - 验证新增的结构性Key
      expect(find.byKey(const Key('account_main_container')), findsOneWidget);
      expect(find.byKey(const Key('account_card_container')), findsOneWidget);
      expect(find.byKey(const Key('account_form_column')), findsOneWidget);

      // Assert - 验证字段Key（基础字段）
      expect(find.byKey(const Key('account_email_field')), findsOneWidget);
      expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
      expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);
    });
  });

  // ========================================
  // Phase 1: UI结构测试
  // ========================================
  group('Phase 1: UI结构测试', () {
    group('1.1 AppBar结构测试', () {
      testWidgets('AppBar标题和按钮显示 - 验证基本AppBar元素', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证AppBar基本结构
        expect(find.byType(AppBar), findsOneWidget);

        // 验证标题
        expect(find.text('アカウント情報'), findsOneWidget);

        // 验证返回按钮
        expect(find.byKey(const Key('account_back_button')), findsOneWidget);
        expect(find.byIcon(Icons.arrow_back), findsOneWidget);

        // 验证保存按钮
        expect(find.byKey(const Key('account_save_button')), findsOneWidget);
        expect(find.text('保存'), findsOneWidget);

        // 验证AppBar actions包含保存按钮
        final appBar = tester.widget<AppBar>(find.byType(AppBar));
        expect(appBar.actions, isNotNull);
        expect(appBar.actions!.length, equals(1));
      });

      testWidgets('返回按钮功能验证 - 验证返回按钮属性和图标', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证返回按钮详细属性
        final backButton = find.byKey(const Key('account_back_button'));
        expect(backButton, findsOneWidget);

        final iconButton = tester.widget<IconButton>(backButton);
        expect(iconButton.icon, isA<Icon>());
        expect((iconButton.icon as Icon).icon, equals(Icons.arrow_back));
        expect(iconButton.onPressed, isNotNull);
      });

      testWidgets('保存按钮状态验证 - 验证保存按钮样式和文本', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证保存按钮详细属性
        final saveButton = find.byKey(const Key('account_save_button'));
        expect(saveButton, findsOneWidget);

        final textButton = tester.widget<TextButton>(saveButton);
        expect(textButton.onPressed, isNotNull);

        // 验证按钮文本样式
        final text = tester.widget<Text>(find.descendant(of: saveButton, matching: find.text('保存')));
        expect(text.style?.color, equals(Colors.white));
      });
    });

    group('1.2 主体布局测试', () {
      testWidgets('Container和ClipRRect结构 - 验证主容器和卡片容器', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证主容器
        final mainContainer = find.byKey(const Key('account_main_container'));
        expect(mainContainer, findsOneWidget);

        final containerWidget = tester.widget<Container>(mainContainer);
        expect(containerWidget.margin, equals(const EdgeInsets.all(16.0)));

        // 验证卡片容器
        final cardContainer = find.byKey(const Key('account_card_container'));
        expect(cardContainer, findsOneWidget);

        final clipRRect = tester.widget<ClipRRect>(cardContainer);
        expect(clipRRect.borderRadius, equals(const BorderRadius.all(Radius.circular(8))));

        // 验证容器嵌套关系
        expect(find.descendant(of: mainContainer, matching: cardContainer), findsOneWidget);
      });

      testWidgets('Column布局验证 - 验证表单列布局结构', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证表单列
        final formColumn = find.byKey(const Key('account_form_column'));
        expect(formColumn, findsOneWidget);

        final column = tester.widget<Column>(formColumn);
        expect(column.children.length, greaterThan(5)); // 至少包含基本字段

        // 验证IntrinsicHeight和Column的嵌套关系
        expect(find.descendant(of: find.byType(IntrinsicHeight), matching: formColumn), findsOneWidget);
      });

      testWidgets('IntrinsicHeight使用验证 - 验证自适应高度布局', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证IntrinsicHeight存在
        expect(find.byType(IntrinsicHeight), findsOneWidget);

        // 验证IntrinsicHeight包含Column
        expect(find.descendant(of: find.byType(IntrinsicHeight), matching: find.byType(Column)), findsOneWidget);

        // 验证Obx包含主要内容
        expect(find.byType(Obx), findsOneWidget);
        expect(
          find.descendant(of: find.byType(Obx), matching: find.byKey(const Key('account_main_container'))),
          findsOneWidget,
        );
      });
    });

    group('1.3 字段渲染测试', () {
      testWidgets('基础字段显示 - 验证所有基础字段正确渲染', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证基础字段存在
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_kana_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_kana_field')), findsOneWidget);
        expect(find.byKey(const Key('account_expiry_field')), findsOneWidget);
        expect(find.byKey(const Key('account_group_field')), findsOneWidget);

        // 验证字段文本显示
        expect(find.text('<EMAIL>'), findsOneWidget); // 邮箱
        expect(find.text('田中'), findsOneWidget); // 姓
        expect(find.text('太郎'), findsOneWidget); // 名
        expect(find.text('タナカ'), findsOneWidget); // 姓カナ
        expect(find.text('タロウ'), findsOneWidget); // 名カナ
        expect(find.text('一般ユーザー'), findsOneWidget); // 组
      });

      testWidgets('条件字段显示 - enableTwoStep=false时隐藏电话字段', (WidgetTester tester) async {
        // Arrange - 使用normalUserState (enableTwoStep=false)
        await tester.pumpWidget(createWidgetUnderTest(userState: normalUserState));
        await tester.pumpAndSettle();

        // Assert - 电话相关字段应该隐藏
        expect(find.byKey(const Key('account_nation_code_field')), findsNothing);
        expect(find.byKey(const Key('account_phone_field')), findsNothing);
      });

      testWidgets('条件字段显示 - enableTwoStep=true时显示电话字段', (WidgetTester tester) async {
        // Arrange - 使用fullFeatureUserState (enableTwoStep=true)
        await tester.pumpWidget(createWidgetUnderTest(userState: fullFeatureUserState));
        await tester.pumpAndSettle();

        // Assert - 电话相关字段应该显示
        expect(find.byKey(const Key('account_nation_code_field')), findsOneWidget);
        expect(find.byKey(const Key('account_phone_field')), findsOneWidget);

        // 验证电话字段内容
        expect(find.text('+81'), findsOneWidget); // 国家代码
        expect(find.text('090-1234-5678'), findsOneWidget); // 电话号码
      });

      testWidgets('条件字段显示 - hasAssetLocation=false时隐藏场所字段', (WidgetTester tester) async {
        // Arrange - 使用normalUserState (hasAssetLocation=false)
        await tester.pumpWidget(createWidgetUnderTest(userState: normalUserState));
        await tester.pumpAndSettle();

        // Assert - 场所字段应该隐藏
        expect(find.byKey(const Key('account_location_field')), findsNothing);
      });

      testWidgets('条件字段显示 - hasAssetLocation=true时显示场所字段', (WidgetTester tester) async {
        // Arrange - 使用fullFeatureUserState (hasAssetLocation=true)
        await tester.pumpWidget(createWidgetUnderTest(userState: fullFeatureUserState));
        await tester.pumpAndSettle();

        // Assert - 场所字段应该显示
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);

        // 验证场所字段内容（显示tmpLocation而不是location）
        expect(find.text('Osaka Branch'), findsOneWidget); // tmpLocation
      });

      testWidgets('必填标记显示逻辑 - 验证必须字段的红色标签', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证必填标记存在
        expect(find.text('必須'), findsNWidgets(2)); // 姓和名字段都是必填

        // 验证必填标记样式
        final mustLabels = tester
            .widgetList<Container>(find.ancestor(of: find.text('必須'), matching: find.byType(Container)))
            .where(
              (container) =>
                  container.decoration is BoxDecoration &&
                  (container.decoration as BoxDecoration).color == const Color(0xFFf74a77),
            );
        expect(mustLabels.length, equals(2));

        // 验证必填文本样式
        final mustTexts = tester.widgetList<Text>(find.text('必須'));
        for (final text in mustTexts) {
          expect(text.style?.color, equals(Colors.white));
          expect(text.style?.fontSize, equals(12));
        }
      });

      testWidgets('锁定图标显示逻辑 - 验证只读字段的锁定图标', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证锁定图标存在
        // 邮箱、有效期限、组字段应该有锁定图标
        expect(find.byIcon(Icons.lock_outline), findsNWidgets(3));

        // 验证锁定图标样式
        final lockIcons = tester.widgetList<Icon>(find.byIcon(Icons.lock_outline));
        for (final icon in lockIcons) {
          expect(icon.size, equals(20));
          expect(icon.color, equals(const Color(0xFF202020)));
        }

        // 验证可编辑字段有箭头图标
        expect(find.byIcon(Icons.arrow_forward_ios), findsAtLeast(4)); // 至少姓、名、姓カナ、名カナ
      });
    });
  });

  // ========================================
  // Phase 2: 响应式状态测试
  // ========================================
  group('Phase 2: 响应式状态测试', () {
    group('2.1 不同用户状态渲染测试', () {
      testWidgets('普通用户状态渲染 - 验证基础字段内容正确显示', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: normalUserState));
        await tester.pumpAndSettle();

        // Assert - 验证普通用户状态内容
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('田中'), findsOneWidget);
        expect(find.text('太郎'), findsOneWidget);
        expect(find.text('タナカ'), findsOneWidget);
        expect(find.text('タロウ'), findsOneWidget);
        expect(find.text('一般ユーザー'), findsOneWidget);
        // 有效期限字段显示为空
        expect(find.byKey(const Key('account_expiry_field')), findsOneWidget);

        // Assert - 验证条件字段隐藏
        expect(find.byKey(const Key('account_nation_code_field')), findsNothing);
        expect(find.byKey(const Key('account_phone_field')), findsNothing);
        expect(find.byKey(const Key('account_location_field')), findsNothing);
      });

      testWidgets('全功能用户状态渲染 - 验证所有字段内容正确显示', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: fullFeatureUserState));
        await tester.pumpAndSettle();

        // Assert - 验证全功能用户状态内容
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('佐藤'), findsOneWidget);
        expect(find.text('花子'), findsOneWidget);
        expect(find.text('サトウ'), findsOneWidget);
        expect(find.text('ハナコ'), findsOneWidget);
        expect(find.text('管理者'), findsOneWidget);
        // 有效期限字段显示为空
        expect(find.byKey(const Key('account_expiry_field')), findsOneWidget);

        // Assert - 验证条件字段显示（enableTwoStep=true）
        expect(find.byKey(const Key('account_nation_code_field')), findsOneWidget);
        expect(find.byKey(const Key('account_phone_field')), findsOneWidget);
        expect(find.text('+81'), findsOneWidget);
        expect(find.text('090-1234-5678'), findsOneWidget);

        // Assert - 验证场所字段显示（hasAssetLocation=true）
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);
        expect(find.text('Osaka Branch'), findsOneWidget); // tmpLocation优先显示
      });

      testWidgets('边界条件用户状态渲染 - 验证空字段处理', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: edgeCaseUserState));
        await tester.pumpAndSettle();

        // Assert - 验证边界条件状态处理
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text(''), findsAtLeastNWidgets(6)); // 多个空字段

        // Assert - 验证条件字段显示（因为enableTwoStep=true, hasAssetLocation=true）
        expect(find.byKey(const Key('account_nation_code_field')), findsOneWidget);
        expect(find.byKey(const Key('account_phone_field')), findsOneWidget);
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);

        // Assert - 验证必填字段仍然存在（即使为空）
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);
      });
    });

    group('2.2 条件显示逻辑测试', () {
      testWidgets('enableTwoStep=false状态 - 验证双因子认证字段隐藏', (WidgetTester tester) async {
        // Arrange - 明确禁用双因子认证的状态
        final disabledTwoStepState = AccountUIState(1);
        disabledTwoStepState.userName = '<EMAIL>';
        disabledTwoStepState.lastName = '測試';
        disabledTwoStepState.firstName = 'ユーザー';
        disabledTwoStepState.enableTwoStep = false; // 明确设置为false
        disabledTwoStepState.hasAssetLocation = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: disabledTwoStepState));
        await tester.pumpAndSettle();

        // Assert - 验证电话字段隐藏
        expect(find.byKey(const Key('account_nation_code_field')), findsNothing);
        expect(find.byKey(const Key('account_phone_field')), findsNothing);

        // Assert - 验证基础字段仍然显示
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);
      });

      testWidgets('enableTwoStep=true状态 - 验证双因子认证字段显示', (WidgetTester tester) async {
        // Arrange - 明确启用双因子认证的状态
        final enabledTwoStepState = AccountUIState(1);
        enabledTwoStepState.userName = '<EMAIL>';
        enabledTwoStepState.lastName = '測試';
        enabledTwoStepState.firstName = 'ユーザー';
        enabledTwoStepState.enableTwoStep = true; // 明确设置为true
        enabledTwoStepState.nationCode = '+86';
        enabledTwoStepState.tel = '138-0000-0000';
        enabledTwoStepState.hasAssetLocation = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: enabledTwoStepState));
        await tester.pumpAndSettle();

        // Assert - 验证电话字段显示
        expect(find.byKey(const Key('account_nation_code_field')), findsOneWidget);
        expect(find.byKey(const Key('account_phone_field')), findsOneWidget);
        expect(find.text('+86'), findsOneWidget);
        expect(find.text('138-0000-0000'), findsOneWidget);

        // Assert - 验证基础字段仍然显示
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);
      });

      testWidgets('hasAssetLocation=false状态 - 验证场所字段隐藏', (WidgetTester tester) async {
        // Arrange - 明确禁用场所功能的状态
        final disabledLocationState = AccountUIState(1);
        disabledLocationState.userName = '<EMAIL>';
        disabledLocationState.lastName = '測試';
        disabledLocationState.firstName = 'ユーザー';
        disabledLocationState.enableTwoStep = false;
        disabledLocationState.hasAssetLocation = false; // 明确设置为false

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: disabledLocationState));
        await tester.pumpAndSettle();

        // Assert - 验证场所字段隐藏
        expect(find.byKey(const Key('account_location_field')), findsNothing);

        // Assert - 验证基础字段仍然显示
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);
      });

      testWidgets('hasAssetLocation=true状态 - 验证场所字段显示', (WidgetTester tester) async {
        // Arrange - 明确启用场所功能的状态
        final enabledLocationState = AccountUIState(1);
        enabledLocationState.userName = '<EMAIL>';
        enabledLocationState.lastName = '測試';
        enabledLocationState.firstName = 'ユーザー';
        enabledLocationState.enableTwoStep = false;
        enabledLocationState.hasAssetLocation = true; // 明确设置为true
        enabledLocationState.location = 'Kyoto Office';
        enabledLocationState.tmpLocation = null;

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: enabledLocationState));
        await tester.pumpAndSettle();

        // Assert - 验证场所字段显示
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);
        expect(find.text('Kyoto Office'), findsOneWidget);

        // Assert - 验证基础字段仍然显示
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);
      });

      testWidgets('tmpLocation优先级逻辑 - 验证临时场所优先显示', (WidgetTester tester) async {
        // Arrange - 同时设置常规场所和临时场所
        final tmpLocationState = AccountUIState(1);
        tmpLocationState.userName = '<EMAIL>';
        tmpLocationState.lastName = '測試';
        tmpLocationState.firstName = 'ユーザー';
        tmpLocationState.enableTwoStep = false;
        tmpLocationState.hasAssetLocation = true;
        tmpLocationState.location = 'Tokyo Office'; // 常规场所
        tmpLocationState.tmpLocation = 'Nagoya Branch'; // 临时场所

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: tmpLocationState));
        await tester.pumpAndSettle();

        // Assert - 验证临时场所优先显示
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);
        expect(find.text('Nagoya Branch'), findsOneWidget); // 临时场所
        expect(find.text('Tokyo Office'), findsNothing); // 常规场所被隐藏
      });

      testWidgets('复合条件组合测试 - 验证所有功能同时启用', (WidgetTester tester) async {
        // Act - 使用已经配置好的全功能状态
        await tester.pumpWidget(createWidgetUnderTest(userState: fullFeatureUserState));
        await tester.pumpAndSettle();

        // Assert - 验证所有条件字段都显示
        expect(find.byKey(const Key('account_nation_code_field')), findsOneWidget);
        expect(find.byKey(const Key('account_phone_field')), findsOneWidget);
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);

        // Assert - 验证内容正确
        expect(find.text('+81'), findsOneWidget);
        expect(find.text('090-1234-5678'), findsOneWidget);
        expect(find.text('Osaka Branch'), findsOneWidget); // tmpLocation

        // Assert - 验证基础字段仍然存在
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);
      });
    });

    group('2.3 Obx响应式结构验证', () {
      testWidgets('Obx包装验证 - 验证响应式组件正确使用Obx', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证Obx正确存在
        expect(find.byType(Obx), findsOneWidget);

        // Assert - 验证Obx包装了主要的响应式内容
        expect(
          find.descendant(of: find.byType(Obx), matching: find.byKey(const Key('account_main_container'))),
          findsOneWidget,
        );

        expect(
          find.descendant(of: find.byType(Obx), matching: find.byKey(const Key('account_card_container'))),
          findsOneWidget,
        );

        expect(
          find.descendant(of: find.byType(Obx), matching: find.byKey(const Key('account_form_column'))),
          findsOneWidget,
        );

        // Assert - 验证基础字段在Obx内
        expect(
          find.descendant(of: find.byType(Obx), matching: find.byKey(const Key('account_email_field'))),
          findsOneWidget,
        );

        expect(
          find.descendant(of: find.byType(Obx), matching: find.byKey(const Key('account_last_name_field'))),
          findsOneWidget,
        );
      });

      testWidgets('条件字段Obx嵌套验证 - 验证条件字段也在Obx响应式范围内', (WidgetTester tester) async {
        // Act - 使用启用所有功能的状态
        await tester.pumpWidget(createWidgetUnderTest(userState: fullFeatureUserState));
        await tester.pumpAndSettle();

        // Assert - 验证条件字段正确嵌套在Obx内
        expect(
          find.descendant(of: find.byType(Obx), matching: find.byKey(const Key('account_nation_code_field'))),
          findsOneWidget,
        );

        expect(
          find.descendant(of: find.byType(Obx), matching: find.byKey(const Key('account_phone_field'))),
          findsOneWidget,
        );

        expect(
          find.descendant(of: find.byType(Obx), matching: find.byKey(const Key('account_location_field'))),
          findsOneWidget,
        );

        // Assert - 验证条件字段的内容也在Obx内正确显示
        expect(find.descendant(of: find.byType(Obx), matching: find.text('+81')), findsOneWidget);

        expect(find.descendant(of: find.byType(Obx), matching: find.text('090-1234-5678')), findsOneWidget);

        expect(find.descendant(of: find.byType(Obx), matching: find.text('Osaka Branch')), findsOneWidget);
      });

      testWidgets('Obx范围完整性验证 - 验证所有动态内容都在Obx覆盖范围内', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: normalUserState));
        await tester.pumpAndSettle();

        // Assert - 验证Obx是唯一的响应式包装器
        expect(find.byType(Obx), findsOneWidget);

        // Assert - 验证所有用户数据字段都在Obx内
        final userDataFields = ['<EMAIL>', '田中', '太郎', 'タナカ', 'タロウ', '一般ユーザー'];

        for (final fieldText in userDataFields) {
          expect(find.descendant(of: find.byType(Obx), matching: find.text(fieldText)), findsOneWidget);
        }

        // Assert - 验证所有Key标记的字段都在Obx内
        final keyedFields = [
          'account_email_field',
          'account_last_name_field',
          'account_first_name_field',
          'account_last_name_kana_field',
          'account_first_name_kana_field',
          'account_group_field',
          'account_expiry_field',
        ];

        for (final fieldKey in keyedFields) {
          expect(find.descendant(of: find.byType(Obx), matching: find.byKey(Key(fieldKey))), findsOneWidget);
        }
      });
    });

    group('2.4 状态配置一致性验证', () {
      testWidgets('普通用户状态配置 - 验证基础功能状态正确反映在UI中', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: normalUserState));
        await tester.pumpAndSettle();

        // Assert - 验证基础信息一致性
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('田中'), findsOneWidget);

        // Assert - 验证条件字段一致性（普通用户：无双因子认证，无场所）
        expect(find.byKey(const Key('account_nation_code_field')), findsNothing);
        expect(find.byKey(const Key('account_phone_field')), findsNothing);
        expect(find.byKey(const Key('account_location_field')), findsNothing);
      });

      testWidgets('全功能用户状态配置 - 验证所有功能状态正确反映在UI中', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: fullFeatureUserState));
        await tester.pumpAndSettle();

        // Assert - 验证基础信息一致性
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('佐藤'), findsOneWidget);

        // Assert - 验证条件字段一致性（全功能用户：有双因子认证，有场所）
        expect(find.byKey(const Key('account_nation_code_field')), findsOneWidget);
        expect(find.byKey(const Key('account_phone_field')), findsOneWidget);
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);
      });

      testWidgets('边界条件状态配置 - 验证边界情况状态正确反映在UI中', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: edgeCaseUserState));
        await tester.pumpAndSettle();

        // Assert - 验证基础信息一致性
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text(''), findsAtLeastNWidgets(1)); // 空字段

        // Assert - 验证条件字段一致性（边界条件：有双因子认证，有场所）
        expect(find.byKey(const Key('account_nation_code_field')), findsOneWidget);
        expect(find.byKey(const Key('account_phone_field')), findsOneWidget);
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);
      });

      testWidgets('必填字段一致性 - 验证必填标记在所有状态下都正确显示', (WidgetTester tester) async {
        // Act - 使用普通状态测试
        await tester.pumpWidget(createWidgetUnderTest(userState: normalUserState));
        await tester.pumpAndSettle();

        // Assert - 验证必填字段的红色标记存在
        // 姓（必填）
        expect(
          find.descendant(of: find.byKey(const Key('account_last_name_field')), matching: find.text('必須')),
          findsOneWidget,
        );

        // 名（必填）
        expect(
          find.descendant(of: find.byKey(const Key('account_first_name_field')), matching: find.text('必須')),
          findsOneWidget,
        );

        // Assert - 验证カナ字段没有必填标记（根据实际代码）
        // 姓カナ（非必填）
        expect(
          find.descendant(of: find.byKey(const Key('account_last_name_kana_field')), matching: find.text('必須')),
          findsNothing,
        );

        // 名カナ（非必填）
        expect(
          find.descendant(of: find.byKey(const Key('account_first_name_kana_field')), matching: find.text('必須')),
          findsNothing,
        );

        // Assert - 验证非必填字段没有红色标记
        // 邮箱地址（非必填，锁定）
        expect(
          find.descendant(of: find.byKey(const Key('account_email_field')), matching: find.text('必須')),
          findsNothing,
        );

        // 组（非必填，锁定）
        expect(
          find.descendant(of: find.byKey(const Key('account_group_field')), matching: find.text('必須')),
          findsNothing,
        );
      });

      testWidgets('锁定字段一致性 - 验证锁定图标在正确字段显示', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: normalUserState));
        await tester.pumpAndSettle();

        // Assert - 验证锁定字段有锁定图标
        // 邮箱地址（锁定）
        expect(
          find.descendant(of: find.byKey(const Key('account_email_field')), matching: find.byIcon(Icons.lock_outline)),
          findsOneWidget,
        );

        // 组（锁定）
        expect(
          find.descendant(of: find.byKey(const Key('account_group_field')), matching: find.byIcon(Icons.lock_outline)),
          findsOneWidget,
        );

        // 期限（锁定）
        expect(
          find.descendant(of: find.byKey(const Key('account_expiry_field')), matching: find.byIcon(Icons.lock_outline)),
          findsOneWidget,
        );

        // Assert - 验证可编辑字段没有锁定图标
        // 姓（可编辑）
        expect(
          find.descendant(
            of: find.byKey(const Key('account_last_name_field')),
            matching: find.byIcon(Icons.lock_outline),
          ),
          findsNothing,
        );

        // 名（可编辑）
        expect(
          find.descendant(
            of: find.byKey(const Key('account_first_name_field')),
            matching: find.byIcon(Icons.lock_outline),
          ),
          findsNothing,
        );
      });

      testWidgets('边界状态处理一致性 - 验证空值和异常状态的正确处理', (WidgetTester tester) async {
        // Act - 使用边界条件状态
        await tester.pumpWidget(createWidgetUnderTest(userState: edgeCaseUserState));
        await tester.pumpAndSettle();

        // Assert - 验证空字段不导致异常
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.byType(Obx), findsOneWidget);

        // Assert - 验证必填字段即使为空也有对应的UI元素
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_kana_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_kana_field')), findsOneWidget);

        // Assert - 验证条件字段在边界状态下仍正确显示
        expect(find.byKey(const Key('account_nation_code_field')), findsOneWidget);
        expect(find.byKey(const Key('account_phone_field')), findsOneWidget);
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);

        // Assert - 验证UI结构完整性
        expect(find.byKey(const Key('account_main_container')), findsOneWidget);
        expect(find.byKey(const Key('account_card_container')), findsOneWidget);
        expect(find.byKey(const Key('account_form_column')), findsOneWidget);
      });
    });
  });

  // ========================================
  // Phase 3: 用户交互测试
  // ========================================
  group('Phase 3: 用户交互测试', () {
    group('3.1 编辑字段交互测试', () {
      testWidgets('姓字段点击交互 - 验证点击触发editLastName调用', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 清理之前的交互记录，准备验证用户交互
        clearInteractions(mockController);

        // Act - 点击姓字段
        await tester.tap(find.byKey(const Key('account_last_name_field')));
        await tester.pumpAndSettle();

        // Assert - 验证editLastName被调用
        verify(mockController.editLastName()).called(1);

        // Assert - 验证其他编辑方法没有被调用
        verifyNever(mockController.editFirstName());
        verifyNever(mockController.editLastNameKana());
        verifyNever(mockController.editFirstNameKana());
      });

      testWidgets('名字段点击交互 - 验证点击触发editFirstName调用', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 清理之前的交互记录，准备验证用户交互
        clearInteractions(mockController);

        // Act - 点击名字段
        await tester.tap(find.byKey(const Key('account_first_name_field')));
        await tester.pumpAndSettle();

        // Assert - 验证editFirstName被调用
        verify(mockController.editFirstName()).called(1);

        // Assert - 验证其他编辑方法没有被调用
        verifyNever(mockController.editLastName());
        verifyNever(mockController.editLastNameKana());
        verifyNever(mockController.editFirstNameKana());
      });

      testWidgets('姓カナ字段点击交互 - 验证点击触发editLastNameKana调用', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 清理之前的交互记录，准备验证用户交互
        clearInteractions(mockController);

        // Act - 点击姓カナ字段
        await tester.tap(find.byKey(const Key('account_last_name_kana_field')));
        await tester.pumpAndSettle();

        // Assert - 验证editLastNameKana被调用
        verify(mockController.editLastNameKana()).called(1);

        // Assert - 验证其他编辑方法没有被调用
        verifyNever(mockController.editLastName());
        verifyNever(mockController.editFirstName());
        verifyNever(mockController.editFirstNameKana());
      });

      testWidgets('名カナ字段点击交互 - 验证点击触发editFirstNameKana调用', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 清理之前的交互记录，准备验证用户交互
        clearInteractions(mockController);

        // Act - 点击名カナ字段
        await tester.tap(find.byKey(const Key('account_first_name_kana_field')));
        await tester.pumpAndSettle();

        // Assert - 验证editFirstNameKana被调用
        verify(mockController.editFirstNameKana()).called(1);

        // Assert - 验证其他编辑方法没有被调用
        verifyNever(mockController.editLastName());
        verifyNever(mockController.editFirstName());
        verifyNever(mockController.editLastNameKana());
      });
    });

    group('3.2 导航交互测试', () {
      testWidgets('场所字段点击交互 - 验证点击触发场所选择页面导航', (WidgetTester tester) async {
        // Act - 使用有场所功能的状态
        await tester.pumpWidget(createWidgetUnderTest(userState: fullFeatureUserState));
        await tester.pumpAndSettle();

        // 清理之前的交互记录，准备验证用户交互
        clearInteractions(mockController);

        // Act - 点击场所字段
        await tester.tap(find.byKey(const Key('account_location_field')));
        await tester.pumpAndSettle();

        // Assert - 验证toLocationSelectPage被调用
        verify(mockController.toLocationSelectPage()).called(1);

        // Assert - 验证其他导航方法没有被调用
        verifyNever(mockController.saveUserInfo());
      });

      testWidgets('保存按钮点击交互 - 验证点击触发保存操作', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 清理之前的交互记录，准备验证用户交互
        clearInteractions(mockController);

        // Act - 点击保存按钮
        await tester.tap(find.byKey(const Key('account_save_button')));
        await tester.pumpAndSettle();

        // Assert - 验证saveUserInfo被调用
        verify(mockController.saveUserInfo()).called(1);

        // Assert - 验证其他导航方法没有被调用
        verifyNever(mockController.toLocationSelectPage());
      });

      testWidgets('返回按钮点击交互 - 验证AppBar返回按钮功能', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 清理之前的交互记录，准备验证用户交互
        clearInteractions(mockController);

        // Act - 点击返回按钮
        await tester.tap(find.byKey(const Key('account_back_button')));
        await tester.pumpAndSettle();

        // Assert - 验证返回按钮被点击（这里验证UI响应，不验证具体导航逻辑）
        // 实际导航由系统处理，我们主要验证按钮的可点击性
        expect(find.byKey(const Key('account_back_button')), findsOneWidget);

        // Assert - 验证没有其他方法被意外调用
        verifyNever(mockController.saveUserInfo());
        verifyNever(mockController.toLocationSelectPage());
      });
    });

    group('3.3 条件交互测试', () {
      testWidgets('锁定字段交互限制 - 验证锁定字段不可点击编辑', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 清理之前的交互记录，准备验证用户交互
        clearInteractions(mockController);

        // Act - 尝试点击锁定字段（邮箱地址）
        await tester.tap(find.byKey(const Key('account_email_field')));
        await tester.pumpAndSettle();

        // Assert - 验证没有编辑相关的方法被调用
        verifyNever(mockController.editLastName());
        verifyNever(mockController.editFirstName());
        verifyNever(mockController.editLastNameKana());
        verifyNever(mockController.editFirstNameKana());
        verifyNever(mockController.saveUserInfo());
        verifyNever(mockController.toLocationSelectPage());
      });

      testWidgets('条件字段交互可用性 - 验证条件字段在相应条件下可交互', (WidgetTester tester) async {
        // Act - 使用启用场所功能的状态
        await tester.pumpWidget(createWidgetUnderTest(userState: fullFeatureUserState));
        await tester.pumpAndSettle();

        // Assert - 验证场所字段存在且可交互
        final locationField = find.byKey(const Key('account_location_field'));
        expect(locationField, findsOneWidget);

        // 清理之前的交互记录，准备验证用户交互
        clearInteractions(mockController);

        // Act - 点击场所字段
        await tester.tap(locationField);
        await tester.pumpAndSettle();

        // Assert - 验证场所选择方法被调用
        verify(mockController.toLocationSelectPage()).called(1);

        // Assert - 验证其他方法没有被调用
        verifyNever(mockController.saveUserInfo());
        verifyNever(mockController.editLastName());
        verifyNever(mockController.editFirstName());
      });

      testWidgets('条件字段交互不可用性 - 验证条件字段在相应条件下不可交互', (WidgetTester tester) async {
        // Act - 使用普通用户状态（无场所功能）
        await tester.pumpWidget(createWidgetUnderTest(userState: normalUserState));
        await tester.pumpAndSettle();

        // 清理之前的交互记录，准备验证用户交互
        clearInteractions(mockController);

        // Assert - 验证场所字段不存在
        expect(find.byKey(const Key('account_location_field')), findsNothing);

        // Assert - 验证双因子认证字段不存在
        expect(find.byKey(const Key('account_nation_code_field')), findsNothing);
        expect(find.byKey(const Key('account_phone_field')), findsNothing);

        // Assert - 验证没有相关方法被调用（即使在无条件字段的情况下也不应该被调用）
        verifyNever(mockController.toLocationSelectPage());
        verifyNever(mockController.saveUserInfo());
      });
    });
  });

  // ========================================
  // Phase 4: 边界条件测试
  // ========================================
  group('Phase 4: 边界条件测试', () {
    group('4.1 空值和异常数据处理', () {
      testWidgets('最小化状态处理 - 验证最小化状态不导致异常', (WidgetTester tester) async {
        // Arrange - 创建最小化状态（仅有ID，其他字段为默认值）
        final minimalState = AccountUIState(0);
        // 不设置任何其他字段，使用默认值

        // Act - 验证页面正常渲染
        await tester.pumpWidget(createWidgetUnderTest(userState: minimalState));
        await tester.pumpAndSettle();

        // Assert - 验证基本UI结构仍然存在
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
      });

      testWidgets('空字符串字段处理 - 验证空字符串字段正确显示', (WidgetTester tester) async {
        // Arrange - 创建空字符串状态
        final emptyState = AccountUIState(1);
        emptyState.userName = '';
        emptyState.lastName = '';
        emptyState.firstName = '';
        emptyState.lastNameKana = '';
        emptyState.firstNameKana = '';
        emptyState.roleName = '';
        emptyState.recoverableLimit = '';
        emptyState.enableTwoStep = false;
        emptyState.hasAssetLocation = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: emptyState));
        await tester.pumpAndSettle();

        // Assert - 验证页面正常渲染
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);

        // Assert - 验证条件字段在空状态下隐藏
        expect(find.byKey(const Key('account_nation_code_field')), findsNothing);
        expect(find.byKey(const Key('account_phone_field')), findsNothing);
        expect(find.byKey(const Key('account_location_field')), findsNothing);
      });

      testWidgets('长文本处理 - 验证长文本正确显示和处理', (WidgetTester tester) async {
        // Arrange - 创建较长但合理的文本状态
        final longTextState = AccountUIState(1);
        final reasonableLongText = 'あいうえおかきくけこさしすせそたちつてと'; // 25个字符的长文本
        longTextState.userName = '$<EMAIL>';
        longTextState.lastName = reasonableLongText;
        longTextState.firstName = reasonableLongText;
        longTextState.lastNameKana = 'アイウエオカキクケコ'; // 10个字符
        longTextState.firstNameKana = 'タチツテトナニヌネノ'; // 10个字符
        longTextState.roleName = reasonableLongText;
        longTextState.enableTwoStep = false;
        longTextState.hasAssetLocation = false;

        // Act - 验证页面正常渲染
        await tester.pumpWidget(createWidgetUnderTest(userState: longTextState));
        await tester.pumpAndSettle();

        // Assert - 验证基本UI结构仍然存在
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);

        // Assert - 验证长文本被正确处理（可能被截断或省略）
        expect(find.text('$<EMAIL>'), findsOneWidget);
        expect(find.text(reasonableLongText), findsWidgets); // 可能找到多个相同的长文本
      });

      testWidgets('特殊字符处理 - 验证特殊字符和符号正确显示', (WidgetTester tester) async {
        // Arrange - 创建包含特殊字符的状态
        final specialCharState = AccountUIState(1);
        specialCharState.userName = '<EMAIL>';
        specialCharState.lastName = 'テスト!@#\$%^&*()';
        specialCharState.firstName = '名前<>?:"{}|';
        specialCharState.lastNameKana = 'カナ～♪♫♬';
        specialCharState.firstNameKana = 'テスト★☆◆◇';
        specialCharState.roleName = 'ロール※￥';
        specialCharState.enableTwoStep = false;
        specialCharState.hasAssetLocation = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: specialCharState));
        await tester.pumpAndSettle();

        // Assert - 验证页面正常渲染并显示特殊字符
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('テスト!@#\$%^&*()'), findsOneWidget);
        expect(find.text('名前<>?:"{}|'), findsOneWidget);
      });
    });

    group('4.2 状态边界值测试', () {
      testWidgets('boolean边界状态组合1 - enableTwoStep=false, hasAssetLocation=false', (WidgetTester tester) async {
        // Arrange
        final boundaryState = AccountUIState(1);
        boundaryState.userName = '<EMAIL>';
        boundaryState.lastName = 'テスト';
        boundaryState.firstName = 'ユーザー';
        boundaryState.enableTwoStep = false;
        boundaryState.hasAssetLocation = false;
        boundaryState.nationCode = '';
        boundaryState.tel = '';
        boundaryState.location = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: boundaryState));
        await tester.pumpAndSettle();

        // Assert - 验证页面正常渲染
        expect(find.byType(AccountPage), findsOneWidget);

        // Assert - 验证条件字段不显示
        expect(find.byKey(const Key('account_nation_code_field')), findsNothing);
        expect(find.byKey(const Key('account_phone_field')), findsNothing);
        expect(find.byKey(const Key('account_location_field')), findsNothing);
      });

      testWidgets('boolean边界状态组合2 - enableTwoStep=true, hasAssetLocation=false', (WidgetTester tester) async {
        // Arrange
        final boundaryState = AccountUIState(2);
        boundaryState.userName = '<EMAIL>';
        boundaryState.lastName = 'テスト';
        boundaryState.firstName = 'ユーザー';
        boundaryState.enableTwoStep = true;
        boundaryState.hasAssetLocation = false;
        boundaryState.nationCode = '+81';
        boundaryState.tel = '090-0000-0000';
        boundaryState.location = '';

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: boundaryState));
        await tester.pumpAndSettle();

        // Assert - 验证页面正常渲染
        expect(find.byType(AccountPage), findsOneWidget);

        // Assert - 验证双因子认证字段显示，场所字段不显示
        expect(find.byKey(const Key('account_nation_code_field')), findsOneWidget);
        expect(find.byKey(const Key('account_phone_field')), findsOneWidget);
        expect(find.byKey(const Key('account_location_field')), findsNothing);
      });

      testWidgets('boolean边界状态组合3 - enableTwoStep=false, hasAssetLocation=true', (WidgetTester tester) async {
        // Arrange
        final boundaryState = AccountUIState(3);
        boundaryState.userName = '<EMAIL>';
        boundaryState.lastName = 'テスト';
        boundaryState.firstName = 'ユーザー';
        boundaryState.enableTwoStep = false;
        boundaryState.hasAssetLocation = true;
        boundaryState.nationCode = '';
        boundaryState.tel = '';
        boundaryState.location = 'Test Location';

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: boundaryState));
        await tester.pumpAndSettle();

        // Assert - 验证页面正常渲染
        expect(find.byType(AccountPage), findsOneWidget);

        // Assert - 验证场所字段显示，双因子认证字段不显示
        expect(find.byKey(const Key('account_nation_code_field')), findsNothing);
        expect(find.byKey(const Key('account_phone_field')), findsNothing);
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);
      });

      testWidgets('boolean边界状态组合4 - enableTwoStep=true, hasAssetLocation=true', (WidgetTester tester) async {
        // Arrange
        final boundaryState = AccountUIState(4);
        boundaryState.userName = '<EMAIL>';
        boundaryState.lastName = 'テスト';
        boundaryState.firstName = 'ユーザー';
        boundaryState.enableTwoStep = true;
        boundaryState.hasAssetLocation = true;
        boundaryState.nationCode = '+81';
        boundaryState.tel = '090-0000-0000';
        boundaryState.location = 'Test Location';

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: boundaryState));
        await tester.pumpAndSettle();

        // Assert - 验证页面正常渲染
        expect(find.byType(AccountPage), findsOneWidget);

        // Assert - 验证所有条件字段都显示
        expect(find.byKey(const Key('account_nation_code_field')), findsOneWidget);
        expect(find.byKey(const Key('account_phone_field')), findsOneWidget);
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);
      });

      testWidgets('数字ID边界值0 - 验证零值ID处理', (WidgetTester tester) async {
        // Arrange
        final extremeIdState = AccountUIState(0);
        extremeIdState.userName = '<EMAIL>';
        extremeIdState.lastName = 'Test';
        extremeIdState.firstName = 'User';
        extremeIdState.enableTwoStep = false;
        extremeIdState.hasAssetLocation = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: extremeIdState));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('Test'), findsAtLeastNWidgets(1));
        expect(find.text('User'), findsAtLeastNWidgets(1));
      });

      testWidgets('数字ID边界值1 - 验证最小正值ID处理', (WidgetTester tester) async {
        // Arrange
        final extremeIdState = AccountUIState(1);
        extremeIdState.userName = '<EMAIL>';
        extremeIdState.lastName = 'Test';
        extremeIdState.firstName = 'User';
        extremeIdState.enableTwoStep = false;
        extremeIdState.hasAssetLocation = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: extremeIdState));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('Test'), findsAtLeastNWidgets(1));
      });

      testWidgets('数字ID边界值********* - 验证大数值ID处理', (WidgetTester tester) async {
        // Arrange
        final extremeIdState = AccountUIState(*********);
        extremeIdState.userName = '<EMAIL>';
        extremeIdState.lastName = 'Test';
        extremeIdState.firstName = 'User';
        extremeIdState.enableTwoStep = false;
        extremeIdState.hasAssetLocation = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: extremeIdState));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('Test'), findsAtLeastNWidgets(1));
      });

      testWidgets('数字ID边界值-1 - 验证负值ID处理', (WidgetTester tester) async {
        // Arrange
        final extremeIdState = AccountUIState(-1);
        extremeIdState.userName = '<EMAIL>';
        extremeIdState.lastName = 'Test';
        extremeIdState.firstName = 'User';
        extremeIdState.enableTwoStep = false;
        extremeIdState.hasAssetLocation = false;

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: extremeIdState));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('Test'), findsAtLeastNWidgets(1));
      });

      testWidgets('临时场所优先级边界 - 验证tmpLocation null处理', (WidgetTester tester) async {
        // Arrange - location有值但tmpLocation为null
        final boundaryLocationState = AccountUIState(1);
        boundaryLocationState.userName = '<EMAIL>';
        boundaryLocationState.lastName = 'Location';
        boundaryLocationState.firstName = 'Test';
        boundaryLocationState.enableTwoStep = false;
        boundaryLocationState.hasAssetLocation = true;
        boundaryLocationState.location = 'Default Location';
        boundaryLocationState.tmpLocation = null; // 明确设置为null

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: boundaryLocationState));
        await tester.pumpAndSettle();

        // Assert - 验证显示常规场所
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);
        expect(find.text('Default Location'), findsOneWidget);

        // Arrange - 两个都为空字符串
        boundaryLocationState.location = '';
        boundaryLocationState.tmpLocation = null;

        // Act - 验证页面正常渲染
        await tester.pumpWidget(createWidgetUnderTest(userState: boundaryLocationState));
        await tester.pumpAndSettle();
      });
    });

    group('4.3 性能和渲染极限测试', () {
      testWidgets('频繁状态更新性能 - 验证大量状态更新不影响性能', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 测量性能
        final stopwatch = Stopwatch()..start();

        // Act - 频繁更新状态（模拟快速状态变化）
        for (int i = 0; i < 50; i++) {
          final dynamicState = AccountUIState(i);
          dynamicState.userName = 'perf$<EMAIL>';
          dynamicState.lastName = 'Performance$i';
          dynamicState.firstName = 'Test$i';
          dynamicState.enableTwoStep = i % 2 == 0;
          dynamicState.hasAssetLocation = i % 3 == 0;

          when(mockController.state).thenReturn(dynamicState.obs);

          if (i % 10 == 0) {
            await tester.pump();
          }
        }

        await tester.pumpAndSettle();
        stopwatch.stop();

        // Assert - 验证性能在合理范围内（10秒内完成）
        expect(stopwatch.elapsedMilliseconds, lessThan(10000));

        // Assert - 验证UI仍然正常
        expect(find.byType(AccountPage), findsOneWidget);
      });

      testWidgets('大量同时渲染字段 - 验证所有字段同时显示不影响性能', (WidgetTester tester) async {
        // Arrange - 启用所有字段的状态
        final allFieldsState = AccountUIState(1);
        allFieldsState.userName = '<EMAIL>';
        allFieldsState.lastName = 'All';
        allFieldsState.firstName = 'Fields';
        allFieldsState.lastNameKana = 'オール';
        allFieldsState.firstNameKana = 'フィールド';
        allFieldsState.roleName = 'Administrator';
        allFieldsState.recoverableLimit = '2024/12/31';
        allFieldsState.enableTwoStep = true;
        allFieldsState.hasAssetLocation = true;
        allFieldsState.nationCode = '+81';
        allFieldsState.tel = '090-1234-5678';
        allFieldsState.location = 'Main Office';
        allFieldsState.tmpLocation = 'Temporary Branch';

        // 测量渲染性能
        final stopwatch = Stopwatch()..start();

        // Act
        await tester.pumpWidget(createWidgetUnderTest(userState: allFieldsState));
        await tester.pumpAndSettle();

        stopwatch.stop();

        // Assert - 验证渲染性能在合理范围内（5秒内完成）
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));

        // Assert - 验证所有字段都正确渲染
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_kana_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_kana_field')), findsOneWidget);
        expect(find.byKey(const Key('account_group_field')), findsOneWidget);
        expect(find.byKey(const Key('account_expiry_field')), findsOneWidget);
        expect(find.byKey(const Key('account_nation_code_field')), findsOneWidget);
        expect(find.byKey(const Key('account_phone_field')), findsOneWidget);
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);

        // Assert - 验证tmpLocation优先显示
        expect(find.text('Temporary Branch'), findsOneWidget);
        expect(find.text('Main Office'), findsNothing);
      });

      testWidgets('内存使用边界测试 - 验证多次创建销毁Widget不导致内存泄露', (WidgetTester tester) async {
        // Act - 多次创建和销毁Widget
        for (int i = 0; i < 20; i++) {
          final memoryTestState = AccountUIState(i);
          memoryTestState.userName = 'memory$<EMAIL>';
          memoryTestState.lastName = 'Memory$i';
          memoryTestState.firstName = 'Test$i';
          memoryTestState.enableTwoStep = i % 2 == 0;
          memoryTestState.hasAssetLocation = i % 3 == 0;

          // Act - 创建Widget
          await tester.pumpWidget(createWidgetUnderTest(userState: memoryTestState));
          await tester.pumpAndSettle();

          // Assert - 验证基本功能正常
          expect(find.byType(AccountPage), findsOneWidget);
          expect(find.text('memory$<EMAIL>'), findsOneWidget);

          // Act - 销毁Widget
          await tester.pumpWidget(Container());
          await tester.pumpAndSettle();
        }

        // 最终验证 - 确保可以正常重新创建
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();
        expect(find.byType(AccountPage), findsOneWidget);
      });
    });
  });

  // ========================================
  // Phase 5: 集成测试和完整业务流程验证
  // ========================================
  group('Phase 5: 集成测试和完整业务流程验证', () {
    group('5.1 完整编辑流程测试', () {
      testWidgets('完整编辑姓名流程 - 验证从点击到完成的完整流程', (WidgetTester tester) async {
        // Arrange - 设置初始状态
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // 清理交互记录
        clearInteractions(mockController);

        // Act - 执行完整的编辑流程
        // 1. 点击姓字段
        await tester.tap(find.byKey(const Key('account_last_name_field')));
        await tester.pumpAndSettle();

        // 2. 点击名字段
        await tester.tap(find.byKey(const Key('account_first_name_field')));
        await tester.pumpAndSettle();

        // 3. 点击保存按钮
        await tester.tap(find.byKey(const Key('account_save_button')));
        await tester.pumpAndSettle();

        // Assert - 验证完整流程的调用序列
        verify(mockController.editLastName()).called(1);
        verify(mockController.editFirstName()).called(1);
        verify(mockController.saveUserInfo()).called(1);
      });

      testWidgets('编辑カナ字段组合流程 - 验证相关字段的组合编辑', (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();
        clearInteractions(mockController);

        // Act - 编辑所有カナ字段
        await tester.tap(find.byKey(const Key('account_last_name_kana_field')));
        await tester.pumpAndSettle();

        await tester.tap(find.byKey(const Key('account_first_name_kana_field')));
        await tester.pumpAndSettle();

        // Assert - 验证相关方法被调用
        verify(mockController.editLastNameKana()).called(1);
        verify(mockController.editFirstNameKana()).called(1);

        // Assert - 验证其他编辑方法没有被意外调用
        verifyNever(mockController.editLastName());
        verifyNever(mockController.editFirstName());
      });

      testWidgets('条件字段编辑流程 - 验证场所编辑的完整流程', (WidgetTester tester) async {
        // Arrange - 使用有场所功能的状态
        await tester.pumpWidget(createWidgetUnderTest(userState: fullFeatureUserState));
        await tester.pumpAndSettle();
        clearInteractions(mockController);

        // Act - 编辑场所并保存
        await tester.tap(find.byKey(const Key('account_location_field')));
        await tester.pumpAndSettle();

        await tester.tap(find.byKey(const Key('account_save_button')));
        await tester.pumpAndSettle();

        // Assert - 验证场所编辑和保存流程
        verify(mockController.toLocationSelectPage()).called(1);
        verify(mockController.saveUserInfo()).called(1);
      });
    });

    group('5.2 状态变化和响应流程测试', () {
      testWidgets('状态对比验证 - 验证不同状态下UI的正确渲染', (WidgetTester tester) async {
        // Arrange & Act - 测试普通用户状态
        await tester.pumpWidget(createWidgetUnderTest(userState: normalUserState));
        await tester.pumpAndSettle();

        // Assert - 验证普通用户状态
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.byKey(const Key('account_nation_code_field')), findsNothing);
        expect(find.byKey(const Key('account_location_field')), findsNothing);

        // Act - 重新创建Widget用全功能用户状态
        await tester.pumpWidget(Container()); // 清空当前Widget
        await tester.pumpAndSettle();

        await tester.pumpWidget(createWidgetUnderTest(userState: fullFeatureUserState));
        await tester.pumpAndSettle();

        // Assert - 验证全功能用户状态
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.byKey(const Key('account_nation_code_field')), findsOneWidget);
        expect(find.byKey(const Key('account_phone_field')), findsOneWidget);
        expect(find.byKey(const Key('account_location_field')), findsOneWidget);
      });

      testWidgets('多状态渲染稳定性 - 验证多种状态的渲染稳定性', (WidgetTester tester) async {
        // 定义测试状态数组
        final testStates = [normalUserState, fullFeatureUserState, edgeCaseUserState];

        // Act - 多次切换不同状态的Widget
        for (int i = 0; i < 3; i++) {
          for (final state in testStates) {
            // 创建Widget
            await tester.pumpWidget(createWidgetUnderTest(userState: state));
            await tester.pumpAndSettle();

            // 验证基本结构稳定
            expect(find.byType(AccountPage), findsOneWidget);
            expect(find.byType(Scaffold), findsOneWidget);
            expect(find.byKey(const Key('account_email_field')), findsOneWidget);

            // 清空Widget
            await tester.pumpWidget(Container());
            await tester.pumpAndSettle();
          }
        }

        // Assert - 最终验证可以正常重新创建
        await tester.pumpWidget(createWidgetUnderTest(userState: normalUserState));
        await tester.pumpAndSettle();
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
      });

      testWidgets('tmpLocation优先级验证 - 验证临时场所的显示优先级', (WidgetTester tester) async {
        // Arrange - 创建有默认场所的状态
        final defaultLocationState = AccountUIState(1);
        defaultLocationState.userName = '<EMAIL>';
        defaultLocationState.lastName = 'Location';
        defaultLocationState.firstName = 'Test';
        defaultLocationState.enableTwoStep = false;
        defaultLocationState.hasAssetLocation = true;
        defaultLocationState.location = 'Default Location';
        defaultLocationState.tmpLocation = null;

        // Act & Assert - 测试默认场所显示
        await tester.pumpWidget(createWidgetUnderTest(userState: defaultLocationState));
        await tester.pumpAndSettle();
        expect(find.text('Default Location'), findsOneWidget);

        // Arrange - 创建有临时场所的状态
        final tmpLocationState = AccountUIState(2);
        tmpLocationState.userName = '<EMAIL>';
        tmpLocationState.lastName = 'Location';
        tmpLocationState.firstName = 'Test';
        tmpLocationState.enableTwoStep = false;
        tmpLocationState.hasAssetLocation = true;
        tmpLocationState.location = 'Default Location';
        tmpLocationState.tmpLocation = 'Temporary Location';

        // Act & Assert - 测试临时场所优先显示
        await tester.pumpWidget(Container()); // 清空
        await tester.pumpAndSettle();

        await tester.pumpWidget(createWidgetUnderTest(userState: tmpLocationState));
        await tester.pumpAndSettle();
        expect(find.text('Temporary Location'), findsOneWidget);
        expect(find.text('Default Location'), findsNothing);
      });
    });

    group('5.3 异常处理和恢复测试', () {
      testWidgets('Controller方法调用验证 - 验证Controller方法被正确调用', (WidgetTester tester) async {
        // Arrange - 使用正常的Mock配置
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();
        clearInteractions(mockController);

        // Act - 触发Controller方法调用
        await tester.tap(find.byKey(const Key('account_last_name_field')));
        await tester.pumpAndSettle();

        // Assert - 验证方法被调用，UI保持稳定
        verify(mockController.editLastName()).called(1);
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);

        // Act - 再次触发其他方法
        await tester.tap(find.byKey(const Key('account_save_button')));
        await tester.pumpAndSettle();

        // Assert - 验证保存方法被调用
        verify(mockController.saveUserInfo()).called(1);
        expect(find.byType(AccountPage), findsOneWidget);
      });

      testWidgets('状态恢复测试 - 验证异常后状态能正确恢复', (WidgetTester tester) async {
        // Arrange - 正常初始化
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Act - 模拟状态异常和恢复
        // 1. 设置异常状态（空状态）
        when(mockController.state).thenReturn(AccountUIState(0).obs);
        await tester.pump();
        await tester.pumpAndSettle();

        // 2. 恢复正常状态
        when(mockController.state).thenReturn(normalUserState.obs);
        await tester.pump();
        await tester.pumpAndSettle();

        // Assert - 验证状态正确恢复
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('田中'), findsOneWidget);
      });

      testWidgets('连续快速操作稳定性 - 验证连续快速点击的稳定性', (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();
        clearInteractions(mockController);

        // Act - 连续快速点击多个字段
        for (int i = 0; i < 10; i++) {
          await tester.tap(find.byKey(const Key('account_last_name_field')));
          await tester.tap(find.byKey(const Key('account_first_name_field')));
          await tester.tap(find.byKey(const Key('account_save_button')));
          // 注意：不等待pumpAndSettle，模拟快速连续点击
          await tester.pump(const Duration(milliseconds: 10));
        }

        await tester.pumpAndSettle(); // 最终等待所有操作完成

        // Assert - 验证UI仍然稳定
        expect(find.byType(AccountPage), findsOneWidget);

        // Assert - 验证方法被调用了预期次数
        verify(mockController.editLastName()).called(10);
        verify(mockController.editFirstName()).called(10);
        verify(mockController.saveUserInfo()).called(10);
      });
    });

    group('5.4 可访问性和用户体验测试', () {
      testWidgets('语义化标签测试 - 验证UI元素的语义化', (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证关键UI元素的可访问性
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(SafeArea), findsOneWidget);

        // 验证所有字段都有对应的Key，便于自动化测试和可访问性工具
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_save_button')), findsOneWidget);
        expect(find.byKey(const Key('account_back_button')), findsOneWidget);
      });

      testWidgets('必填字段标识测试 - 验证必填字段的视觉提示', (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证必填标记的存在和数量
        expect(find.text('必須'), findsNWidgets(2)); // 姓和名是必填字段

        // 验证必填字段与非必填字段的区别
        // 姓字段应该有必填标记
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        // 邮箱字段不应该有必填标记（是锁定字段）
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
      });

      testWidgets('锁定字段视觉反馈测试 - 验证锁定字段的视觉提示', (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证锁定图标的存在
        expect(find.byIcon(Icons.lock_outline), findsNWidgets(3)); // 邮箱、有效期限、组别字段

        // 验证锁定字段不可编辑的视觉反馈
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_expiry_field')), findsOneWidget);
        expect(find.byKey(const Key('account_group_field')), findsOneWidget);
      });

      testWidgets('基本布局结构测试 - 验证页面基本布局组件', (WidgetTester tester) async {
        // Arrange - 使用默认屏幕尺寸
        await tester.pumpWidget(createWidgetUnderTest());
        await tester.pumpAndSettle();

        // Assert - 验证基本布局组件存在
        expect(find.byType(AccountPage), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(SafeArea), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);

        // Assert - 验证主要容器和结构
        expect(find.byKey(const Key('account_main_container')), findsOneWidget);
        expect(find.byKey(const Key('account_card_container')), findsOneWidget);
        expect(find.byKey(const Key('account_form_column')), findsOneWidget);

        // Assert - 验证所有关键字段都正确渲染
        expect(find.byKey(const Key('account_email_field')), findsOneWidget);
        expect(find.byKey(const Key('account_last_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_first_name_field')), findsOneWidget);
        expect(find.byKey(const Key('account_save_button')), findsOneWidget);
        expect(find.byKey(const Key('account_back_button')), findsOneWidget);
      });
    });
  });
}

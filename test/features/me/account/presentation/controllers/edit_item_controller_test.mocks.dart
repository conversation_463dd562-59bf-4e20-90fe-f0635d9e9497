// Mocks generated by <PERSON>ckito 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/account/presentation/controllers/edit_item_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i4;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i2.NavigationService {
  @override
  _i3.Future<dynamic> navigateTo(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<bool> navigateUntil(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i3.Future<bool>.value(false),
            returnValueForMissingStub: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i3.Future<dynamic> toAssetDetail(_i4.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i3.Future<dynamic>.value(),
            returnValueForMissingStub: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);
}

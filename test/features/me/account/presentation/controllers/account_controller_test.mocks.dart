// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/account/presentation/controllers/account_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;
import 'dart:ui' as _i10;

import 'package:asset_force_mobile_v2/core/services/dialog_service.dart' as _i9;
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart'
    as _i13;
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i4;
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart' as _i8;
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart'
    as _i14;
import 'package:asset_force_mobile_v2/features/me/account/domain/usecase/account_data_usercase.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/me/account/presentation/models/account_ui_state.dart'
    as _i5;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/layout_setting_repository.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart'
    as _i11;
import 'package:flutter/cupertino.dart' as _i12;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i15;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUserRepository_0 extends _i1.SmartFake
    implements _i2.UserRepository {
  _FakeUserRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLayoutSettingRepository_1 extends _i1.SmartFake
    implements _i3.LayoutSettingRepository {
  _FakeLayoutSettingRepository_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIStorageUtils_2 extends _i1.SmartFake implements _i4.IStorageUtils {
  _FakeIStorageUtils_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAccountUIState_3 extends _i1.SmartFake
    implements _i5.AccountUIState {
  _FakeAccountUIState_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AccountDataUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockAccountDataUseCase extends _i1.Mock
    implements _i6.AccountDataUseCase {
  MockAccountDataUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.UserRepository get userRepository =>
      (super.noSuchMethod(
            Invocation.getter(#userRepository),
            returnValue: _FakeUserRepository_0(
              this,
              Invocation.getter(#userRepository),
            ),
          )
          as _i2.UserRepository);

  @override
  _i3.LayoutSettingRepository get layoutSettingRepository =>
      (super.noSuchMethod(
            Invocation.getter(#layoutSettingRepository),
            returnValue: _FakeLayoutSettingRepository_1(
              this,
              Invocation.getter(#layoutSettingRepository),
            ),
          )
          as _i3.LayoutSettingRepository);

  @override
  _i4.IStorageUtils get storageUtils =>
      (super.noSuchMethod(
            Invocation.getter(#storageUtils),
            returnValue: _FakeIStorageUtils_2(
              this,
              Invocation.getter(#storageUtils),
            ),
          )
          as _i4.IStorageUtils);

  @override
  _i7.Future<_i5.AccountUIState> call(_i8.NoParams? params) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i7.Future<_i5.AccountUIState>.value(
              _FakeAccountUIState_3(this, Invocation.method(#call, [params])),
            ),
          )
          as _i7.Future<_i5.AccountUIState>);

  @override
  _i7.Future<bool> updateUserInfo(_i5.AccountUIState? state) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserInfo, [state]),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);
}

/// A class which mocks [DialogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDialogService extends _i1.Mock implements _i9.DialogService {
  MockDialogService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<void> show({
    String? title,
    required String? content,
    String? confirmText,
    String? cancelText,
    _i10.VoidCallback? onConfirm,
    _i10.VoidCallback? onCancel,
    bool? barrierDismissible,
    _i11.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#show, [], {
              #title: title,
              #content: content,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> showInput({
    String? title,
    required String? contentText,
    String? hintText,
    String? initialValue,
    String? confirmText,
    String? cancelText,
    required dynamic Function(String)? onConfirm,
    _i10.VoidCallback? onCancel,
    String? Function(String?)? inputValidator,
    int? maxLength,
    _i12.TextInputType? keyboardType,
    bool? obscureText,
    bool? barrierDismissible,
    _i11.DialogType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showInput, [], {
              #title: title,
              #contentText: contentText,
              #hintText: hintText,
              #initialValue: initialValue,
              #confirmText: confirmText,
              #cancelText: cancelText,
              #onConfirm: onConfirm,
              #onCancel: onCancel,
              #inputValidator: inputValidator,
              #maxLength: maxLength,
              #keyboardType: keyboardType,
              #obscureText: obscureText,
              #barrierDismissible: barrierDismissible,
              #type: type,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void showToast(String? message) => super.noSuchMethod(
    Invocation.method(#showToast, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void showCustomToast(String? message) => super.noSuchMethod(
    Invocation.method(#showCustomToast, [message]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [NavigationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigationService extends _i1.Mock implements _i13.NavigationService {
  MockNavigationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<dynamic> navigateTo(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateTo,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i7.Future<dynamic>.value(),
          )
          as _i7.Future<dynamic>);

  @override
  _i7.Future<dynamic> navigateOffAll(
    String? route, {
    dynamic arguments,
    int? id,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateOffAll,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i7.Future<dynamic>.value(),
          )
          as _i7.Future<dynamic>);

  @override
  _i7.Future<bool> navigateUntil(String? route, {dynamic arguments, int? id}) =>
      (super.noSuchMethod(
            Invocation.method(
              #navigateUntil,
              [route],
              {#arguments: arguments, #id: id},
            ),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  void goBack<T>({int? id, T? result}) => super.noSuchMethod(
    Invocation.method(#goBack, [], {#id: id, #result: result}),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<dynamic> toAssetDetail(_i14.AssetDetailArguments? arguments) =>
      (super.noSuchMethod(
            Invocation.method(#toAssetDetail, [arguments]),
            returnValue: _i7.Future<dynamic>.value(),
          )
          as _i7.Future<dynamic>);
}

/// A class which mocks [IStorageUtils].
///
/// See the documentation for Mockito's code generation for more information.
class MockIStorageUtils extends _i1.Mock implements _i4.IStorageUtils {
  MockIStorageUtils() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<void> init() =>
      (super.noSuchMethod(
            Invocation.method(#init, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> initPersonalStorage({required String? cryptedName}) =>
      (super.noSuchMethod(
            Invocation.method(#initPersonalStorage, [], {
              #cryptedName: cryptedName,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  T? getValue<T>(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#getValue, [key], {#personal: personal}),
          )
          as T?);

  @override
  _i7.Future<void> setValue<T>(
    String? key,
    T? value, {
    bool? personal = false,
    int? durationInSeconds,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #setValue,
              [key, value],
              {#personal: personal, #durationInSeconds: durationInSeconds},
            ),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> removeValue(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#removeValue, [key], {#personal: personal}),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  bool containsKey(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#containsKey, [key], {#personal: personal}),
            returnValue: false,
          )
          as bool);

  @override
  _i7.Future<void> clearAll({bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#clearAll, [], {#personal: personal}),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  List<String> getAllKeys({bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#getAllKeys, [], {#personal: personal}),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  String getAssetScanLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getAssetScanLocation, []),
            returnValue: _i15.dummyValue<String>(
              this,
              Invocation.method(#getAssetScanLocation, []),
            ),
          )
          as String);

  @override
  _i7.Future<void> setAssetScanLocation({required String? assetLocation}) =>
      (super.noSuchMethod(
            Invocation.method(#setAssetScanLocation, [], {
              #assetLocation: assetLocation,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);
}

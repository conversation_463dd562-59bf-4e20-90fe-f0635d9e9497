import 'package:asset_force_mobile_v2/core/presentation/loading_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/location_select/data/models/location_page_param.dart';
import 'package:asset_force_mobile_v2/features/me/account/domain/usecase/account_data_usercase.dart';
import 'package:asset_force_mobile_v2/features/me/account/presentation/controllers/account_controller.dart';
import 'package:asset_force_mobile_v2/features/me/account/presentation/models/account_ui_state.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'account_controller_test.mocks.dart';

@GenerateMocks([AccountDataUseCase, DialogService, NavigationService, IStorageUtils])
void main() {
  late MockAccountDataUseCase mockAccountDataUseCase;
  late MockDialogService mockDialogService;
  late MockNavigationService mockNavigationService;
  late MockIStorageUtils mockStorageUtils;
  late AccountController controller;

  setUpAll(() {
    // 初始化Flutter测试绑定 - 这对于使用GetX响应式状态管理是必需的
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    mockAccountDataUseCase = MockAccountDataUseCase();
    mockDialogService = MockDialogService();
    mockNavigationService = MockNavigationService();
    mockStorageUtils = MockIStorageUtils();

    controller = AccountController(mockAccountDataUseCase, mockDialogService, mockNavigationService, mockStorageUtils);
  });

  tearDown(() {
    controller.dispose();
  });

  group('AccountController - Phase 1: 基础功能测试', () {
    test('应该正确初始化所有依赖注入', () {
      // Assert
      expect(controller.useCase, equals(mockAccountDataUseCase));
      expect(controller.dialogService, equals(mockDialogService));
      expect(controller.navigationService, equals(mockNavigationService));
      expect(controller.storageUtils, equals(mockStorageUtils));
    });

    test('应该继承LoadingController功能', () {
      // Assert
      expect(controller, isA<LoadingController>());
      expect(controller.isLoading, isA<dynamic>());
      expect(controller.loadingError, isA<dynamic>());
      expect(controller.isLoadingError, isA<dynamic>());
    });

    test('应该初始化正确的状态值', () {
      // Assert
      expect(controller.state, isA<dynamic>());
      expect(controller.state.value, isA<AccountUIState>());
      expect(controller.isLoading.value, isFalse);
      expect(controller.loadingError.value, isFalse);
      expect(controller.isLoadingError.value, isFalse);
    });

    test('应该正确设置state的初始值', () {
      // Assert
      expect(controller.state.value.userId, equals(0));
      expect(controller.state.value.userName, isEmpty);
      expect(controller.state.value.lastName, isEmpty);
      expect(controller.state.value.firstName, isEmpty);
      expect(controller.state.value.lastNameKana, isEmpty);
      expect(controller.state.value.firstNameKana, isEmpty);
    });
  });

  group('AccountController - Phase 2: 数据加载和状态管理', () {
    late AccountUIState testAccountState;

    setUp(() {
      // 创建测试用的AccountUIState
      testAccountState = AccountUIState(123);
      testAccountState.userName = '<EMAIL>';
      testAccountState.lastName = '田中';
      testAccountState.firstName = '太郎';
      testAccountState.lastNameKana = 'タナカ';
      testAccountState.firstNameKana = 'タロウ';
      testAccountState.tel = '090-1234-5678';
      testAccountState.nationCode = '+81';
    });

    test('fetchData应该调用useCase并更新state', () async {
      // Arrange
      when(mockAccountDataUseCase.call(const NoParams())).thenAnswer((_) async => testAccountState);

      // Act
      await controller.fetchData(null);

      // Assert
      verify(mockAccountDataUseCase.call(const NoParams())).called(1);
      expect(controller.state.value.userId, equals(123));
      expect(controller.state.value.userName, equals('<EMAIL>'));
      expect(controller.state.value.lastName, equals('田中'));
      expect(controller.state.value.firstName, equals('太郎'));
      expect(controller.state.value.lastNameKana, equals('タナカ'));
      expect(controller.state.value.firstNameKana, equals('タロウ'));
      expect(controller.state.value.tel, equals('090-1234-5678'));
      expect(controller.state.value.nationCode, equals('+81'));
    });

    test('fetchData应该处理UseCase异常', () async {
      // Arrange
      when(mockAccountDataUseCase.call(const NoParams())).thenThrow(Exception('Network error'));

      // Act & Assert
      expect(() => controller.fetchData(null), throwsException);
      verify(mockAccountDataUseCase.call(const NoParams())).called(1);
    });

    test('应该正确处理LoadingController的状态管理', () async {
      // Arrange
      when(mockAccountDataUseCase.call(const NoParams())).thenAnswer((_) async => testAccountState);

      // 验证初始状态
      expect(controller.isLoading.value, isFalse);
      expect(controller.isLoadingError.value, isFalse);

      // Act
      final future = controller.loadDataWithLoadingStatus();

      // 验证加载中状态
      expect(controller.isLoading.value, isTrue);
      expect(controller.isLoadingError.value, isFalse);

      await future;

      // 验证加载完成状态
      expect(controller.isLoading.value, isFalse);
      expect(controller.isLoadingError.value, isFalse);
      verify(mockAccountDataUseCase.call(const NoParams())).called(1);
    });

    test('应该正确处理状态变化和异常', () async {
      // Arrange
      when(mockAccountDataUseCase.call(const NoParams())).thenThrow(Exception('Network error'));

      // 验证初始状态
      expect(controller.isLoading.value, isFalse);
      expect(controller.isLoadingError.value, isFalse);

      // Act & Assert - 直接测试fetchData的异常处理
      expect(() => controller.fetchData(null), throwsException);
      verify(mockAccountDataUseCase.call(const NoParams())).called(1);

      // 验证异常后状态保持初始值
      expect(controller.isLoading.value, isFalse);
      expect(controller.isLoadingError.value, isFalse);
    });

    test('应该防止重复加载数据', () async {
      // Arrange
      when(mockAccountDataUseCase.call(const NoParams())).thenAnswer((_) async {
        await Future.delayed(Duration(milliseconds: 100));
        return testAccountState;
      });

      // Act
      final future1 = controller.loadDataWithLoadingStatus();
      final future2 = controller.loadDataWithLoadingStatus(); // 第二次调用应该被忽略

      await Future.wait([future1, future2]);

      // Assert
      verify(mockAccountDataUseCase.call(const NoParams())).called(1); // 只调用一次
    });
  });

  group('AccountController - Phase 3: 纯函数验证测试', () {
    group('validateNormalText() 空格验证测试', () {
      test('应该识别空字符串为有效空格', () {
        // Act & Assert
        expect(controller.validateNormalText(''), isTrue);
      });

      test('应该识别单个半角空格', () {
        // Act & Assert
        expect(controller.validateNormalText(' '), isTrue);
      });

      test('应该识别多个半角空格', () {
        // Act & Assert
        expect(controller.validateNormalText('   '), isTrue);
        expect(controller.validateNormalText('     '), isTrue);
      });

      test('应该识别单个全角空格', () {
        // Act & Assert
        expect(controller.validateNormalText('　'), isTrue);
      });

      test('应该识别多个全角空格', () {
        // Act & Assert
        expect(controller.validateNormalText('　　'), isTrue);
        expect(controller.validateNormalText('　　　'), isTrue);
      });

      test('应该识别混合半角和全角空格', () {
        // Act & Assert
        expect(controller.validateNormalText(' 　 '), isTrue);
        expect(controller.validateNormalText('　 　 '), isTrue);
      });

      test('应该拒绝包含文字的字符串', () {
        // Act & Assert
        expect(controller.validateNormalText('田中'), isFalse);
        expect(controller.validateNormalText(' 田中 '), isFalse);
        expect(controller.validateNormalText('　田中　'), isFalse);
      });

      test('应该拒绝包含英文字符的字符串', () {
        // Act & Assert
        expect(controller.validateNormalText('abc'), isFalse);
        expect(controller.validateNormalText(' abc '), isFalse);
        expect(controller.validateNormalText('Test'), isFalse);
      });

      test('应该拒绝包含数字的字符串', () {
        // Act & Assert
        expect(controller.validateNormalText('123'), isFalse);
        expect(controller.validateNormalText(' 123 '), isFalse);
        expect(controller.validateNormalText('0'), isFalse);
      });

      test('应该拒绝包含特殊字符的字符串', () {
        // Act & Assert
        expect(controller.validateNormalText('!@#'), isFalse);
        expect(controller.validateNormalText(' . '), isFalse);
        expect(controller.validateNormalText('-'), isFalse);
      });
    });

    group('validateKatakanaText() 片假名验证测试', () {
      test('应该验证有效的片假名字符', () {
        // Act & Assert
        expect(controller.validateKatakanaText('タナカ'), isTrue);
        expect(controller.validateKatakanaText('タロウ'), isTrue);
        expect(controller.validateKatakanaText('ハナコ'), isTrue);
        expect(controller.validateKatakanaText('カタカナ'), isTrue);
      });

      test('应该验证包含长音符的片假名', () {
        // Act & Assert
        expect(controller.validateKatakanaText('トーキョー'), isTrue);
        expect(controller.validateKatakanaText('コーヒー'), isTrue);
        expect(controller.validateKatakanaText('スーパー'), isTrue);
      });

      test('应该验证扩展片假名字符', () {
        // Act & Assert
        expect(controller.validateKatakanaText('ヴィクター'), isTrue);
        expect(controller.validateKatakanaText('ウィリアム'), isTrue);
      });

      test('应该拒绝平假名字符', () {
        // Act & Assert
        expect(controller.validateKatakanaText('たなか'), isFalse);
        expect(controller.validateKatakanaText('たろう'), isFalse);
        expect(controller.validateKatakanaText('ひらがな'), isFalse);
      });

      test('应该拒绝汉字字符', () {
        // Act & Assert
        expect(controller.validateKatakanaText('田中'), isFalse);
        expect(controller.validateKatakanaText('太郎'), isFalse);
        expect(controller.validateKatakanaText('山田'), isFalse);
      });

      test('应该拒绝英文字符', () {
        // Act & Assert
        expect(controller.validateKatakanaText('Tanaka'), isFalse);
        expect(controller.validateKatakanaText('ABC'), isFalse);
        expect(controller.validateKatakanaText('Test'), isFalse);
      });

      test('应该拒绝数字字符', () {
        // Act & Assert
        expect(controller.validateKatakanaText('123'), isFalse);
        expect(controller.validateKatakanaText('０１２'), isFalse);
        expect(controller.validateKatakanaText('1カタカナ'), isFalse);
      });

      test('应该拒绝空字符串', () {
        // Act & Assert
        expect(controller.validateKatakanaText(''), isFalse);
      });

      test('应该拒绝混合字符', () {
        // Act & Assert
        expect(controller.validateKatakanaText('タナカabc'), isFalse);
        expect(controller.validateKatakanaText('カタカナ123'), isFalse);
        expect(controller.validateKatakanaText('タナカ田中'), isFalse);
        expect(controller.validateKatakanaText('カタかな'), isFalse);
      });

      test('应该拒绝包含空格的字符串', () {
        // Act & Assert
        expect(controller.validateKatakanaText('タナカ タロウ'), isFalse);
        expect(controller.validateKatakanaText(' タナカ'), isFalse);
        expect(controller.validateKatakanaText('タナカ '), isFalse);
        expect(controller.validateKatakanaText('タナカ　タロウ'), isFalse); // 全角空格
      });

      test('应该拒绝特殊符号', () {
        // Act & Assert
        expect(controller.validateKatakanaText('タナカ!'), isFalse);
        expect(controller.validateKatakanaText('タナカ@'), isFalse);
        expect(controller.validateKatakanaText('タナカ・'), isFalse);
        expect(controller.validateKatakanaText('タナカ。'), isFalse);
      });
    });
  });

  group('AccountController - Phase 4: 导航功能测试', () {
    setUp(() {
      // 设置初始状态，包含位置信息
      controller.state.value.userId = 123;
      controller.state.value.userName = '<EMAIL>';
      controller.state.value.location = '東京都渋谷区';
      controller.state.value.tmpLocation = null;
    });

    test('toLocationSelectPage应该调用正确的导航方法', () async {
      // Arrange
      const mockLocation = '神奈川県横浜市';
      when(
        mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
      ).thenAnswer((_) async => mockLocation);

      // Act
      await controller.toLocationSelectPage();

      // Assert
      verify(
        mockNavigationService.navigateTo(AutoRoutes.locationSelectPage, arguments: anyNamed('arguments')),
      ).called(1);
    });

    test('应该传递正确的LocationPageParam参数', () async {
      // Arrange
      const mockLocation = '神奈川県横浜市';
      when(
        mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
      ).thenAnswer((_) async => mockLocation);

      // Act
      await controller.toLocationSelectPage();

      // Assert
      final capturedCall = verify(
        mockNavigationService.navigateTo(captureAny, arguments: captureAnyNamed('arguments')),
      ).captured;

      expect(capturedCall[0], equals(AutoRoutes.locationSelectPage));
      final locationParam = capturedCall[1] as LocationPageParam;
      expect(locationParam.isFromUserSettings, isTrue);
      expect(locationParam.location, equals('東京都渋谷区'));
    });

    test('应该处理导航返回结果并更新tmpLocation', () async {
      // Arrange
      const mockReturnLocation = '神奈川県横浜市';
      when(
        mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
      ).thenAnswer((_) async => mockReturnLocation);

      // 验证初始状态
      expect(controller.state.value.tmpLocation, isNull);

      // Act
      await controller.toLocationSelectPage();

      // Assert
      expect(controller.state.value.tmpLocation, equals(mockReturnLocation));
    });

    test('应该处理导航返回null的情况', () async {
      // Arrange
      when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async => null);

      // 设置初始的tmpLocation
      controller.state.value.tmpLocation = '初期値';

      // Act
      await controller.toLocationSelectPage();

      // Assert
      expect(controller.state.value.tmpLocation, equals('初期値')); // 应该保持不变
    });

    test('应该在返回结果不为null时调用state.refresh', () async {
      // Arrange
      const mockReturnLocation = '神奈川県横浜市';
      when(
        mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
      ).thenAnswer((_) async => mockReturnLocation);

      // Act
      await controller.toLocationSelectPage();

      // Assert
      // 验证tmpLocation被更新（间接验证了refresh被调用）
      expect(controller.state.value.tmpLocation, equals(mockReturnLocation));
    });

    test('应该处理NavigationService异常', () async {
      // Arrange
      when(
        mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
      ).thenThrow(Exception('Navigation error'));

      // Act & Assert
      expect(() => controller.toLocationSelectPage(), throwsException);
      verify(
        mockNavigationService.navigateTo(AutoRoutes.locationSelectPage, arguments: anyNamed('arguments')),
      ).called(1);
    });

    test('应该正确处理当前位置为空的情况', () async {
      // Arrange
      controller.state.value.location = ''; // 设置空位置
      const mockReturnLocation = '神奈川県横浜市';
      when(
        mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
      ).thenAnswer((_) async => mockReturnLocation);

      // Act
      await controller.toLocationSelectPage();

      // Assert
      final capturedCall = verify(
        mockNavigationService.navigateTo(captureAny, arguments: captureAnyNamed('arguments')),
      ).captured;

      final locationParam = capturedCall[1] as LocationPageParam;
      expect(locationParam.location, equals('')); // 应该传递空字符串
      expect(controller.state.value.tmpLocation, equals(mockReturnLocation));
    });
  });

  group('AccountController - Phase 5: 编辑功能测试', () {
    setUp(() {
      // 设置初始状态
      controller.state.value.lastName = '田中';
      controller.state.value.firstName = '太郎';
      controller.state.value.lastNameKana = 'タナカ';
      controller.state.value.firstNameKana = 'タロウ';
    });

    group('editLastName() 姓编辑测试', () {
      test('应该调用导航并传递正确参数', () async {
        // Arrange
        when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async => '山田');

        // Act
        await controller.editLastName();

        // Assert
        final capturedCall = verify(
          mockNavigationService.navigateTo(captureAny, arguments: captureAnyNamed('arguments')),
        ).captured;

        expect(capturedCall[0], equals('/edit_item_page'));
        final arguments = capturedCall[1] as Map<String, dynamic>;
        expect(arguments['title'], equals('姓'));
        expect(arguments['value'], equals('田中'));
      });

      test('应该处理有效输入并更新lastName', () async {
        // Arrange
        const newLastName = '山田';
        when(
          mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
        ).thenAnswer((_) async => newLastName);

        // Act
        await controller.editLastName();

        // Assert
        expect(controller.state.value.lastName, equals(newLastName));
        verifyNever(mockDialogService.show(content: anyNamed('content')));
      });

      test('应该验证空格输入并显示错误对话框', () async {
        // Arrange
        const invalidInput = '   '; // 纯空格
        when(
          mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
        ).thenAnswer((_) async => invalidInput);
        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});

        // Act
        await controller.editLastName();

        // Assert
        expect(controller.state.value.lastName, equals('田中')); // 应该保持原值
        verify(mockDialogService.show(content: '姓 スペースを入力しないでください。')).called(1);
      });

      test('应该处理取消编辑情况', () async {
        // Arrange
        when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async => null);

        // Act
        await controller.editLastName();

        // Assert
        expect(controller.state.value.lastName, equals('田中')); // 应该保持原值
        verifyNever(mockDialogService.show(content: anyNamed('content')));
      });
    });

    group('editLastNameKana() 姓カナ编辑测试', () {
      test('应该调用导航并传递正确参数', () async {
        // Arrange
        when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async => 'ヤマダ');

        // Act
        await controller.editLastNameKana();

        // Assert
        final capturedCall = verify(
          mockNavigationService.navigateTo(captureAny, arguments: captureAnyNamed('arguments')),
        ).captured;

        expect(capturedCall[0], equals('/edit_item_page'));
        final arguments = capturedCall[1] as Map<String, dynamic>;
        expect(arguments['title'], equals('姓（カナ）'));
        expect(arguments['value'], equals('タナカ'));
      });

      test('应该处理有效片假名输入并更新lastNameKana', () async {
        // Arrange
        const newLastNameKana = 'ヤマダ';
        when(
          mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
        ).thenAnswer((_) async => newLastNameKana);

        // Act
        await controller.editLastNameKana();

        // Assert
        expect(controller.state.value.lastNameKana, equals(newLastNameKana));
        verifyNever(mockDialogService.show(content: anyNamed('content')));
      });

      test('应该验证无效片假名输入并显示错误对话框', () async {
        // Arrange
        const invalidInput = 'やまだ'; // 平假名，应该被拒绝
        when(
          mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
        ).thenAnswer((_) async => invalidInput);
        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});

        // Act
        await controller.editLastNameKana();

        // Assert
        expect(controller.state.value.lastNameKana, equals('タナカ')); // 应该保持原值
        verify(mockDialogService.show(content: '姓（カナ）カナを入力してください。')).called(1);
      });

      test('应该处理取消编辑情况', () async {
        // Arrange
        when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async => null);

        // Act
        await controller.editLastNameKana();

        // Assert
        expect(controller.state.value.lastNameKana, equals('タナカ')); // 应该保持原值
        verifyNever(mockDialogService.show(content: anyNamed('content')));
      });
    });

    group('editFirstName() 名编辑测试', () {
      test('应该调用导航并传递正确参数', () async {
        // Arrange
        when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async => '次郎');

        // Act
        await controller.editFirstName();

        // Assert
        final capturedCall = verify(
          mockNavigationService.navigateTo(captureAny, arguments: captureAnyNamed('arguments')),
        ).captured;

        expect(capturedCall[0], equals('/edit_item_page'));
        final arguments = capturedCall[1] as Map<String, dynamic>;
        expect(arguments['title'], equals('名'));
        expect(arguments['value'], equals('太郎'));
      });

      test('应该处理有效输入并更新firstName', () async {
        // Arrange
        const newFirstName = '次郎';
        when(
          mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
        ).thenAnswer((_) async => newFirstName);

        // Act
        await controller.editFirstName();

        // Assert
        expect(controller.state.value.firstName, equals(newFirstName));
        verifyNever(mockDialogService.show(content: anyNamed('content')));
      });

      test('应该验证空格输入并显示错误对话框', () async {
        // Arrange
        const invalidInput = '　　'; // 全角空格
        when(
          mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
        ).thenAnswer((_) async => invalidInput);
        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});

        // Act
        await controller.editFirstName();

        // Assert
        expect(controller.state.value.firstName, equals('太郎')); // 应该保持原值
        verify(mockDialogService.show(content: '名 スペースを入力しないでください。')).called(1);
      });

      test('应该处理取消编辑情况', () async {
        // Arrange
        when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async => null);

        // Act
        await controller.editFirstName();

        // Assert
        expect(controller.state.value.firstName, equals('太郎')); // 应该保持原值
        verifyNever(mockDialogService.show(content: anyNamed('content')));
      });
    });

    group('editFirstNameKana() 名カナ编辑测试', () {
      test('应该调用导航并传递正确参数', () async {
        // Arrange
        when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async => 'ジロウ');

        // Act
        await controller.editFirstNameKana();

        // Assert
        final capturedCall = verify(
          mockNavigationService.navigateTo(captureAny, arguments: captureAnyNamed('arguments')),
        ).captured;

        expect(capturedCall[0], equals('/edit_item_page'));
        final arguments = capturedCall[1] as Map<String, dynamic>;
        expect(arguments['title'], equals('名（カナ）'));
        expect(arguments['value'], equals('タロウ'));
      });

      test('应该处理有效片假名输入并更新firstNameKana', () async {
        // Arrange
        const newFirstNameKana = 'ジロウ';
        when(
          mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
        ).thenAnswer((_) async => newFirstNameKana);

        // Act
        await controller.editFirstNameKana();

        // Assert
        expect(controller.state.value.firstNameKana, equals(newFirstNameKana));
        verifyNever(mockDialogService.show(content: anyNamed('content')));
      });

      test('应该验证无效片假名输入并显示错误对话框', () async {
        // Arrange
        const invalidInput = 'じろう'; // 平假名，应该被拒绝
        when(
          mockNavigationService.navigateTo(any, arguments: anyNamed('arguments')),
        ).thenAnswer((_) async => invalidInput);
        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});

        // Act
        await controller.editFirstNameKana();

        // Assert
        expect(controller.state.value.firstNameKana, equals('タロウ')); // 应该保持原值
        verify(mockDialogService.show(content: '名（カナ）カナを入力してください。')).called(1);
      });

      test('应该处理取消编辑情况', () async {
        // Arrange
        when(mockNavigationService.navigateTo(any, arguments: anyNamed('arguments'))).thenAnswer((_) async => null);

        // Act
        await controller.editFirstNameKana();

        // Assert
        expect(controller.state.value.firstNameKana, equals('タロウ')); // 应该保持原值
        verifyNever(mockDialogService.show(content: anyNamed('content')));
      });
    });
  });

  group('AccountController - Phase 6: 保存功能测试', () {
    setUp(() {
      // 设置有效的初始状态
      controller.state.value.lastName = '田中';
      controller.state.value.firstName = '太郎';
      controller.state.value.userId = 123;
    });

    group('saveUserInfo() 必填字段验证', () {
      test('应该验证lastName必填并显示错误对话框', () async {
        // Arrange
        controller.state.value.lastName = ''; // 设置为空
        controller.state.value.firstName = '太郎';
        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});

        // Act
        await controller.saveUserInfo();

        // Assert
        verify(mockDialogService.show(content: '必須項目は全部入力してください。')).called(1);
        verifyNever(mockAccountDataUseCase.updateUserInfo(any));
        verifyNever(mockStorageUtils.setValue<String>(any, any));
      });

      test('应该验证firstName必填并显示错误对话框', () async {
        // Arrange
        controller.state.value.lastName = '田中';
        controller.state.value.firstName = ''; // 设置为空
        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});

        // Act
        await controller.saveUserInfo();

        // Assert
        verify(mockDialogService.show(content: '必須項目は全部入力してください。')).called(1);
        verifyNever(mockAccountDataUseCase.updateUserInfo(any));
        verifyNever(mockStorageUtils.setValue<String>(any, any));
      });

      test('应该验证两个必填字段都为空的情况', () async {
        // Arrange
        controller.state.value.lastName = '';
        controller.state.value.firstName = '';
        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});

        // Act
        await controller.saveUserInfo();

        // Assert
        verify(mockDialogService.show(content: '必須項目は全部入力してください。')).called(1);
        verifyNever(mockAccountDataUseCase.updateUserInfo(any));
        verifyNever(mockStorageUtils.setValue<String>(any, any));
      });
    });

    group('saveUserInfo() 更新成功流程', () {
      test('应该调用useCase.updateUserInfo并传递正确参数', () async {
        // Arrange
        when(mockAccountDataUseCase.updateUserInfo(any)).thenAnswer((_) async => true);
        when(mockAccountDataUseCase.call(const NoParams())).thenAnswer((_) async => AccountUIState(123));
        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});
        when(mockStorageUtils.setValue<String>(any, any)).thenAnswer((_) async {});

        // Act
        await controller.saveUserInfo();

        // Assert
        final captured = verify(mockAccountDataUseCase.updateUserInfo(captureAny)).captured;
        final passedState = captured.first as AccountUIState;
        expect(passedState.lastName, equals('田中'));
        expect(passedState.firstName, equals('太郎'));
        expect(passedState.userId, equals(123));
      });

      test('应该在更新成功后保存到存储', () async {
        // Arrange
        final testState = AccountUIState(123);
        testState.lastName = '田中';
        testState.firstName = '太郎';
        controller.state.value = testState;
        controller.state.refresh();

        // 验证状态设置是否正确
        expect(controller.state.value.lastName, equals('田中'));
        expect(controller.state.value.firstName, equals('太郎'));

        when(mockAccountDataUseCase.updateUserInfo(any)).thenAnswer((_) async => true);

        // Mock loadDataWithLoadingStatus调用，返回包含正确数据的状态
        final mockReturnState = AccountUIState(123);
        mockReturnState.lastName = '田中';
        mockReturnState.firstName = '太郎';
        when(mockAccountDataUseCase.call(const NoParams())).thenAnswer((_) async => mockReturnState);

        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});
        when(mockStorageUtils.setValue<String>(any, any)).thenAnswer((_) async {});

        // Act
        await controller.saveUserInfo();

        // Assert - 使用更宽松的验证，先检查是否调用了正确的方法
        final lastNameCalls = verify(mockStorageUtils.setValue<String>(StorageKeys.lastName, captureAny)).captured;
        final firstNameCalls = verify(mockStorageUtils.setValue<String>(StorageKeys.firstName, captureAny)).captured;

        expect(lastNameCalls, hasLength(1));
        expect(firstNameCalls, hasLength(1));
        expect(lastNameCalls.first, equals('田中'));
        expect(firstNameCalls.first, equals('太郎'));
      });

      test('应该在更新成功后显示成功对话框', () async {
        // Arrange
        final testState = AccountUIState(123);
        testState.lastName = '田中';
        testState.firstName = '太郎';
        controller.state.value = testState;
        controller.state.refresh();

        when(mockAccountDataUseCase.updateUserInfo(any)).thenAnswer((_) async => true);

        // Mock loadDataWithLoadingStatus调用，返回包含正确数据的状态
        final mockReturnState = AccountUIState(123);
        mockReturnState.lastName = '田中';
        mockReturnState.firstName = '太郎';
        when(mockAccountDataUseCase.call(const NoParams())).thenAnswer((_) async => mockReturnState);

        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});
        when(mockStorageUtils.setValue<String>(any, any)).thenAnswer((_) async {});

        // Act
        await controller.saveUserInfo();

        // Assert
        verify(mockDialogService.show(content: 'アカウント情報を更新しました。')).called(1);
      });

      test('应该在更新成功后重新加载数据', () async {
        // Arrange
        final testState = AccountUIState(123);
        testState.lastName = '田中';
        testState.firstName = '太郎';
        controller.state.value = testState;
        controller.state.refresh();

        when(mockAccountDataUseCase.updateUserInfo(any)).thenAnswer((_) async => true);

        // Mock loadDataWithLoadingStatus调用，返回包含正确数据的状态
        final mockReturnState = AccountUIState(123);
        mockReturnState.lastName = '田中';
        mockReturnState.firstName = '太郎';
        when(mockAccountDataUseCase.call(const NoParams())).thenAnswer((_) async => mockReturnState);

        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});
        when(mockStorageUtils.setValue<String>(any, any)).thenAnswer((_) async {});

        // Act
        await controller.saveUserInfo();

        // Assert
        // 验证loadDataWithLoadingStatus被调用（通过验证fetchData被调用）
        verify(mockAccountDataUseCase.call(const NoParams())).called(1);
      });
    });

    group('saveUserInfo() 更新失败流程', () {
      test('应该在更新失败时显示失败对话框', () async {
        // Arrange
        when(mockAccountDataUseCase.updateUserInfo(any)).thenAnswer((_) async => false);
        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});

        // Act
        await controller.saveUserInfo();

        // Assert
        verify(mockDialogService.show(content: '更新失敗。')).called(1);
        verifyNever(mockStorageUtils.setValue<String>(any, any));
        verifyNever(mockAccountDataUseCase.call(const NoParams()));
      });

      test('应该在更新失败时不执行存储操作', () async {
        // Arrange
        when(mockAccountDataUseCase.updateUserInfo(any)).thenAnswer((_) async => false);
        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});

        // Act
        await controller.saveUserInfo();

        // Assert
        verifyNever(mockStorageUtils.setValue<String>(any, any));
      });

      test('应该在更新失败时不重新加载数据', () async {
        // Arrange
        when(mockAccountDataUseCase.updateUserInfo(any)).thenAnswer((_) async => false);
        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});

        // Act
        await controller.saveUserInfo();

        // Assert
        verifyNever(mockAccountDataUseCase.call(const NoParams()));
      });
    });

    group('saveUserInfo() 异常处理', () {
      test('应该处理UseCase异常并回退到错误状态', () async {
        // Arrange
        when(mockAccountDataUseCase.updateUserInfo(any)).thenThrow(Exception('Update failed'));

        // Act
        try {
          await controller.saveUserInfo();
        } catch (e) {
          // 异常被抛出是预期的
        }

        // Assert
        verify(mockAccountDataUseCase.updateUserInfo(any)).called(1);
        verifyNever(mockStorageUtils.setValue<String>(any, any));
      });

      test('应该处理存储异常', () async {
        // Arrange
        when(mockAccountDataUseCase.updateUserInfo(any)).thenAnswer((_) async => true);
        when(mockAccountDataUseCase.call(const NoParams())).thenAnswer((_) async => AccountUIState(123));
        when(mockStorageUtils.setValue<String>(any, any)).thenThrow(Exception('Storage error'));
        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});

        // Act
        try {
          await controller.saveUserInfo();
        } catch (e) {
          // 异常被抛出是预期的
        }

        // Assert
        verify(mockAccountDataUseCase.updateUserInfo(any)).called(1);
        verify(mockStorageUtils.setValue<String>(any, any)).called(1);
      });

      test('应该处理对话框服务异常', () async {
        // Arrange
        when(mockAccountDataUseCase.updateUserInfo(any)).thenAnswer((_) async => true);
        when(mockAccountDataUseCase.call(const NoParams())).thenAnswer((_) async => AccountUIState(123));
        when(mockStorageUtils.setValue<String>(any, any)).thenAnswer((_) async {});
        when(mockDialogService.show(content: anyNamed('content'))).thenThrow(Exception('Dialog error'));

        // Act
        try {
          await controller.saveUserInfo();
        } catch (e) {
          // 异常被抛出是预期的
        }

        // Assert
        verify(mockAccountDataUseCase.updateUserInfo(any)).called(1);
        verify(mockDialogService.show(content: anyNamed('content'))).called(1);
      });
    });

    group('saveUserInfo() 边界条件', () {
      test('应该正确处理包含特殊字符的姓名', () async {
        // Arrange
        final testState = AccountUIState(123);
        testState.lastName = '田中-山田';
        testState.firstName = '太郎(次郎)';
        controller.state.value = testState;
        controller.state.refresh();

        when(mockAccountDataUseCase.updateUserInfo(any)).thenAnswer((_) async => true);

        // Mock loadDataWithLoadingStatus调用，返回包含正确数据的状态
        final mockReturnState = AccountUIState(123);
        mockReturnState.lastName = '田中-山田';
        mockReturnState.firstName = '太郎(次郎)';
        when(mockAccountDataUseCase.call(const NoParams())).thenAnswer((_) async => mockReturnState);

        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});
        when(mockStorageUtils.setValue<String>(any, any)).thenAnswer((_) async {});

        // Act
        await controller.saveUserInfo();

        // Assert
        verify(mockStorageUtils.setValue<String>(StorageKeys.lastName, '田中-山田')).called(1);
        verify(mockStorageUtils.setValue<String>(StorageKeys.firstName, '太郎(次郎)')).called(1);
      });

      test('应该正确处理长姓名', () async {
        // Arrange
        final testState = AccountUIState(123);
        testState.lastName = 'とても長い姓名前の例';
        testState.firstName = 'とても長い名前の例です';
        controller.state.value = testState;
        controller.state.refresh();

        when(mockAccountDataUseCase.updateUserInfo(any)).thenAnswer((_) async => true);

        // Mock loadDataWithLoadingStatus调用，返回包含正确数据的状态
        final mockReturnState = AccountUIState(123);
        mockReturnState.lastName = 'とても長い姓名前の例';
        mockReturnState.firstName = 'とても長い名前の例です';
        when(mockAccountDataUseCase.call(const NoParams())).thenAnswer((_) async => mockReturnState);

        when(mockDialogService.show(content: anyNamed('content'))).thenAnswer((_) async {});
        when(mockStorageUtils.setValue<String>(any, any)).thenAnswer((_) async {});

        // Act
        await controller.saveUserInfo();

        // Assert
        verify(mockStorageUtils.setValue<String>(StorageKeys.lastName, 'とても長い姓名前の例')).called(1);
        verify(mockStorageUtils.setValue<String>(StorageKeys.firstName, 'とても長い名前の例です')).called(1);
      });
    });
  });
}

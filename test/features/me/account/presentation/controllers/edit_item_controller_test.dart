import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/features/me/account/presentation/controllers/edit_item_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 生成NavigationService的Mock
@GenerateNiceMocks([MockSpec<NavigationService>()])
import 'edit_item_controller_test.mocks.dart';

void main() {
  late MockNavigationService mockNavigationService;

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    Get.testMode = true;
    Get.reset(); // 确保干净的测试环境

    // 创建Mock实例
    mockNavigationService = MockNavigationService();
  });

  tearDown(() {
    // 清理资源
    reset(mockNavigationService);
    clearInteractions(mockNavigationService);
    Get.reset();
  });

  group('Phase 1: 构造函数与依赖注入测试', () {
    group('1.1 构造函数测试', () {
      test('带NavigationService参数创建 - 验证依赖注入', () {
        // Arrange & Act
        final controller = EditItemController(navigationService: mockNavigationService);

        // Assert
        expect(controller, isNotNull);
        expect(controller.itemTitle, isNotNull);
        expect(controller.itemValue, isNotNull);
        expect(controller.originalValue, equals(''));
        expect(controller.itemTitle.value, equals(''));
        expect(controller.itemValue.value, equals(''));

        // 验证NavigationService被正确注入
        // 注意：_navigationService是私有的，通过行为验证
      });

      test('不带参数创建（使用Get.find）- 验证fallback机制', () {
        // Arrange
        Get.put<NavigationService>(mockNavigationService);

        // Act
        final controller = EditItemController();

        // Assert
        expect(controller, isNotNull);
        expect(controller.itemTitle.value, equals(''));
        expect(controller.itemValue.value, equals(''));
        expect(controller.originalValue, equals(''));

        // 验证Get.find fallback机制正常工作
        // 通过后续的导航行为验证
      });

      test('初始状态验证 - 验证所有属性的初始值', () {
        // Arrange & Act
        final controller = EditItemController(navigationService: mockNavigationService);

        // Assert - 验证所有初始状态
        expect(controller.itemTitle, isA<RxString>());
        expect(controller.itemValue, isA<RxString>());
        expect(controller.originalValue, isA<String>());

        expect(controller.itemTitle.value, isEmpty);
        expect(controller.itemValue.value, isEmpty);
        expect(controller.originalValue, isEmpty);

        // 验证textController还未初始化（late变量）
        expect(() => controller.textController, throwsA(isA<Error>()));
      });
    });

    group('1.2 依赖注入测试', () {
      test('NavigationService注入成功 - 验证私有字段_navigationService', () {
        // Arrange & Act
        final controller = EditItemController(navigationService: mockNavigationService);

        // 设置必要的状态以测试导航
        controller.setItemValues('test', 'value');

        // Act - 通过save方法间接验证NavigationService注入
        controller.save();

        // Assert - 验证mockNavigationService被调用
        verify(mockNavigationService.goBack(result: 'value')).called(1);
      });

      test('Get.find fallback机制 - 验证当参数为null时的行为', () {
        // Arrange
        Get.put<NavigationService>(mockNavigationService);

        // Act
        final controller = EditItemController(navigationService: null);

        // 设置状态并测试导航
        controller.setItemValues('test', 'fallback_value');
        controller.save();

        // Assert - 验证Get.find的NavigationService被使用
        verify(mockNavigationService.goBack(result: 'fallback_value')).called(1);
      });

      test('Observable初始化 - 验证itemTitle.obs和itemValue.obs', () {
        // Arrange & Act
        final controller = EditItemController(navigationService: mockNavigationService);

        // Assert - 验证Observable类型和初始值
        expect(controller.itemTitle, isA<RxString>());
        expect(controller.itemValue, isA<RxString>());

        // 验证Observable的响应性
        bool titleChanged = false;
        bool valueChanged = false;

        controller.itemTitle.listen((value) {
          titleChanged = true;
        });

        controller.itemValue.listen((value) {
          valueChanged = true;
        });

        // 触发变化
        controller.itemTitle.value = 'new title';
        controller.itemValue.value = 'new value';

        // 验证Observer被触发
        expect(titleChanged, isTrue);
        expect(valueChanged, isTrue);
        expect(controller.itemTitle.value, equals('new title'));
        expect(controller.itemValue.value, equals('new value'));
      });
    });
  });

  group('Phase 2: 状态管理测试', () {
    group('2.1 setItemValues方法测试', () {
      test('正常设置值 - 验证title、value、originalValue更新', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // Act
        controller.setItemValues('测试标题', '测试值');

        // Assert - 验证所有值正确设置
        expect(controller.itemTitle.value, equals('测试标题'));
        expect(controller.itemValue.value, equals('测试值'));
        expect(controller.originalValue, equals('测试值'));

        // 验证TextController正确创建和设置
        expect(controller.textController, isNotNull);
        expect(controller.textController.text, equals('测试值'));
      });

      test('TextController创建和设置 - 验证textController正确初始化', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // Act
        controller.setItemValues('标题', '初始值');

        // Assert - 验证TextController状态
        expect(controller.textController, isA<TextEditingController>());
        expect(controller.textController.text, equals('初始值'));
        expect(controller.textController.selection, isA<TextSelection>());

        // 验证TextController可以正常使用
        controller.textController.text = '修改后的值';
        expect(controller.textController.text, equals('修改后的值'));
      });

      test('Observable响应性 - 验证.obs值变化通知', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        List<String> titleChanges = [];
        List<String> valueChanges = [];

        // 设置Observer监听变化
        controller.itemTitle.listen((value) => titleChanges.add(value));
        controller.itemValue.listen((value) => valueChanges.add(value));

        // Act
        controller.setItemValues('新标题', '新值');

        // Assert - 验证Observable通知
        expect(titleChanges, contains('新标题'));
        expect(valueChanges, contains('新值'));

        // 再次修改验证持续响应
        controller.setItemValues('更新标题', '更新值');
        expect(titleChanges, contains('更新标题'));
        expect(valueChanges, contains('更新值'));
      });

      test('重复调用处理 - 验证多次调用时TextController处理', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // Act - 第一次调用
        controller.setItemValues('标题1', '值1');
        final firstController = controller.textController;

        // Act - 第二次调用
        controller.setItemValues('标题2', '值2');
        final secondController = controller.textController;

        // Assert - 验证TextController处理
        expect(controller.itemTitle.value, equals('标题2'));
        expect(controller.itemValue.value, equals('值2'));
        expect(controller.originalValue, equals('值2'));
        expect(controller.textController.text, equals('值2'));

        // 验证TextController实例管理（是否重新创建）
        expect(secondController, isNotNull);
      });

      test('空值和特殊字符处理 - 边界条件测试', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // Test Case 1: 空值处理
        controller.setItemValues('', '');
        expect(controller.itemTitle.value, equals(''));
        expect(controller.itemValue.value, equals(''));
        expect(controller.originalValue, equals(''));
        expect(controller.textController.text, equals(''));

        // Test Case 2: 特殊字符处理
        const specialTitle = '特殊字符\n\t测试 🎉';
        const specialValue = 'emoji test 🚀\nline break\ttab';

        controller.setItemValues(specialTitle, specialValue);
        expect(controller.itemTitle.value, equals(specialTitle));
        expect(controller.itemValue.value, equals(specialValue));
        expect(controller.originalValue, equals(specialValue));
        expect(controller.textController.text, equals(specialValue));

        // Test Case 3: 极长字符串
        final longString = 'A' * 1000;
        controller.setItemValues('长标题', longString);
        expect(controller.itemValue.value, equals(longString));
        expect(controller.textController.text, equals(longString));
      });
    });

    group('2.2 Observable状态测试', () {
      test('itemTitle响应性 - 验证Observer能监听到变化', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        String? lastTitleChange;
        int changeCount = 0;

        // 设置详细的Observer
        controller.itemTitle.listen((value) {
          lastTitleChange = value;
          changeCount++;
        });

        // Act & Assert - 初始状态（Observable.listen不会立即触发初始值）
        expect(controller.itemTitle.value, equals(''));
        expect(changeCount, equals(0)); // 未触发

        // Act & Assert - 通过setItemValues改变
        controller.setItemValues('标题1', '值1');
        expect(lastTitleChange, equals('标题1'));
        expect(changeCount, equals(1));

        // Act & Assert - 直接改变Observable
        controller.itemTitle.value = '直接修改标题';
        expect(lastTitleChange, equals('直接修改标题'));
        expect(changeCount, equals(2));
      });

      test('itemValue响应性 - 验证UI绑定更新', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        List<String> valueHistory = [];

        controller.itemValue.listen((value) => valueHistory.add(value));

        // Act - 多种修改方式
        controller.setItemValues('标题', '方式1');
        controller.itemValue.value = '方式2';
        controller.setItemValues('新标题', '方式3');

        // Assert - 验证所有变化都被捕获（不包含初始空值）
        expect(valueHistory, containsAll(['方式1', '方式2', '方式3']));
        expect(valueHistory.length, equals(3));
        expect(controller.itemValue.value, equals('方式3'));

        // 验证最终状态一致性
        expect(controller.originalValue, equals('方式3'));
      });

      test('状态一致性 - 验证originalValue与itemValue关系', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // Test Case 1: setItemValues后的一致性
        controller.setItemValues('标题', '原始值');
        expect(controller.originalValue, equals('原始值'));
        expect(controller.itemValue.value, equals('原始值'));

        // Test Case 2: 修改itemValue后originalValue不变
        controller.itemValue.value = '修改后的值';
        expect(controller.originalValue, equals('原始值')); // 保持不变
        expect(controller.itemValue.value, equals('修改后的值'));

        // Test Case 3: 重新调用setItemValues重置一致性
        controller.setItemValues('新标题', '新原始值');
        expect(controller.originalValue, equals('新原始值'));
        expect(controller.itemValue.value, equals('新原始值'));

        // Test Case 4: TextController与itemValue同步验证
        controller.setItemValues('测试', 'TextController同步');
        expect(controller.textController.text, equals('TextController同步'));
        expect(controller.itemValue.value, equals('TextController同步'));
      });
    });
  });

  group('Phase 3: 业务逻辑测试', () {
    group('3.1 save方法测试', () {
      test('保存当前值 - 验证调用_navigationService.goBack(result: itemValue.value)', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        controller.setItemValues('测试标题', '原始值');

        // 修改当前值
        controller.itemValue.value = '修改后的值';

        // Act
        controller.save();

        // Assert - 验证NavigationService被正确调用
        verify(mockNavigationService.goBack(result: '修改后的值')).called(1);
        verifyNoMoreInteractions(mockNavigationService);
      });

      test('保存后状态不变 - 验证save不影响当前状态', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        controller.setItemValues('标题', '原始值');
        controller.itemValue.value = '当前值';

        // 保存前状态
        final beforeSaveTitle = controller.itemTitle.value;
        final beforeSaveValue = controller.itemValue.value;
        final beforeOriginalValue = controller.originalValue;
        final beforeTextController = controller.textController.text;

        // Act
        controller.save();

        // Assert - 验证状态完全不变
        expect(controller.itemTitle.value, equals(beforeSaveTitle));
        expect(controller.itemValue.value, equals(beforeSaveValue));
        expect(controller.originalValue, equals(beforeOriginalValue));
        expect(controller.textController.text, equals(beforeTextController));

        // 验证导航被调用
        verify(mockNavigationService.goBack(result: '当前值')).called(1);
      });

      test('保存空值 - 验证空字符串保存', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        controller.setItemValues('标题', '初始值');

        // 清空值
        controller.itemValue.value = '';

        // Act
        controller.save();

        // Assert - 验证空值正确传递
        verify(mockNavigationService.goBack(result: '')).called(1);

        // 验证状态保持
        expect(controller.itemValue.value, equals(''));
        expect(controller.originalValue, equals('初始值')); // 原始值不变
      });
    });

    group('3.2 back方法测试', () {
      test('返回原始值 - 验证调用_navigationService.goBack(result: originalValue)', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        controller.setItemValues('标题', '原始值');

        // 修改当前值
        controller.itemValue.value = '修改后的值';

        // Act
        controller.back();

        // Assert - 验证返回原始值
        verify(mockNavigationService.goBack(result: '原始值')).called(1);
        verifyNoMoreInteractions(mockNavigationService);
      });

      test('取消后状态不变 - 验证back不影响当前状态', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        controller.setItemValues('标题', '原始值');
        controller.itemValue.value = '修改值';

        // 取消前状态
        final beforeBackTitle = controller.itemTitle.value;
        final beforeBackValue = controller.itemValue.value;
        final beforeOriginalValue = controller.originalValue;
        final beforeTextController = controller.textController.text;

        // Act
        controller.back();

        // Assert - 验证状态完全不变
        expect(controller.itemTitle.value, equals(beforeBackTitle));
        expect(controller.itemValue.value, equals(beforeBackValue));
        expect(controller.originalValue, equals(beforeOriginalValue));
        expect(controller.textController.text, equals(beforeTextController));

        // 验证导航被调用（返回原始值）
        verify(mockNavigationService.goBack(result: '原始值')).called(1);
      });

      test('修改后取消 - 验证编辑后取消返回原始值', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        controller.setItemValues('原始标题', '原始值');

        // 进行多次修改
        controller.itemTitle.value = '修改标题1';
        controller.itemValue.value = '修改值1';
        controller.itemTitle.value = '修改标题2';
        controller.itemValue.value = '修改值2';

        // Act
        controller.back();

        // Assert - 验证无论如何修改，都返回最初的原始值
        verify(mockNavigationService.goBack(result: '原始值')).called(1);

        // 验证当前状态保持修改后的值
        expect(controller.itemTitle.value, equals('修改标题2'));
        expect(controller.itemValue.value, equals('修改值2'));
        expect(controller.originalValue, equals('原始值')); // 原始值不变
      });
    });

    group('3.3 clearItemValue方法测试', () {
      test('清除itemValue - 验证itemValue.value = \'\'', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        controller.setItemValues('标题', '有内容的值');

        // 验证清除前有值
        expect(controller.itemValue.value, equals('有内容的值'));
        expect(controller.textController.text, equals('有内容的值'));

        // Act
        controller.clearItemValue();

        // Assert - 验证itemValue被清空
        expect(controller.itemValue.value, equals(''));

        // 验证其他状态不受影响
        expect(controller.itemTitle.value, equals('标题'));
        expect(controller.originalValue, equals('有内容的值')); // 原始值保持不变
      });

      test('清除TextController - 验证textController.clear()调用', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        controller.setItemValues('标题', '文本内容');

        // 验证清除前TextController有内容
        expect(controller.textController.text, equals('文本内容'));

        // Act
        controller.clearItemValue();

        // Assert - 验证TextController被清空
        expect(controller.textController.text, equals(''));

        // 验证itemValue也被清空
        expect(controller.itemValue.value, equals(''));

        // 验证TextController可以继续正常使用
        controller.textController.text = '新内容';
        expect(controller.textController.text, equals('新内容'));
      });

      test('Observable通知 - 验证清除触发UI更新', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        controller.setItemValues('标题', '初始内容');

        // 设置Observer监听
        List<String> valueChanges = [];
        controller.itemValue.listen((value) => valueChanges.add(value));

        // Act
        controller.clearItemValue();

        // Assert - 验证Observable通知被触发
        expect(valueChanges, contains(''));
        expect(valueChanges.last, equals(''));

        // 验证多次清除的行为
        controller.itemValue.value = '重新设置';
        controller.clearItemValue();

        expect(valueChanges, contains('重新设置'));
        expect(valueChanges.last, equals(''));
        expect(valueChanges.length, equals(3)); // '重新设置' + 两次 ''
      });
    });
  });

  group('Phase 4: 集成与生命周期测试', () {
    group('4.1 方法交互测试', () {
      test('设置→清除→保存流程 - 验证完整编辑流程', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // Act - 完整的编辑流程
        // 1. 设置初始值
        controller.setItemValues('测试标题', '初始值');
        expect(controller.itemValue.value, equals('初始值'));
        expect(controller.textController.text, equals('初始值'));

        // 2. 清除值
        controller.clearItemValue();
        expect(controller.itemValue.value, equals(''));
        expect(controller.textController.text, equals(''));

        // 3. 重新设置新值
        controller.itemValue.value = '新输入值';

        // 4. 保存
        controller.save();

        // Assert - 验证整个流程的状态和调用
        verify(mockNavigationService.goBack(result: '新输入值')).called(1);
        expect(controller.originalValue, equals('初始值')); // 原始值保持不变
        expect(controller.itemValue.value, equals('新输入值'));
      });

      test('设置→修改→取消流程 - 验证取消逻辑', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // Act - 完整的取消流程
        // 1. 设置初始值
        controller.setItemValues('标题', '原始内容');

        // 2. 模拟用户编辑
        controller.itemValue.value = '用户修改1';
        controller.textController.text = '用户修改1';

        // 3. 继续修改
        controller.itemValue.value = '用户修改2';
        controller.textController.text = '用户修改2';

        // 4. 取消操作
        controller.back();

        // Assert - 验证取消流程
        verify(mockNavigationService.goBack(result: '原始内容')).called(1);

        // 验证当前状态仍保持最后修改的值
        expect(controller.itemValue.value, equals('用户修改2'));
        expect(controller.textController.text, equals('用户修改2'));
        expect(controller.originalValue, equals('原始内容')); // 原始值不变
      });

      test('多次编辑会话 - 验证重复使用场景', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // Act & Assert - 第一次编辑会话
        controller.setItemValues('标题1', '值1');
        controller.itemValue.value = '修改值1';
        controller.save();
        verify(mockNavigationService.goBack(result: '修改值1')).called(1);

        // 清理mock调用历史
        clearInteractions(mockNavigationService);

        // Act & Assert - 第二次编辑会话（重用Controller）
        controller.setItemValues('标题2', '值2');
        expect(controller.itemValue.value, equals('值2'));
        expect(controller.originalValue, equals('值2'));
        expect(controller.textController.text, equals('值2'));

        controller.itemValue.value = '修改值2';
        controller.back(); // 这次选择取消
        verify(mockNavigationService.goBack(result: '值2')).called(1);

        // 清理并进行第三次会话
        clearInteractions(mockNavigationService);

        // Act & Assert - 第三次编辑会话
        controller.setItemValues('标题3', '值3');
        controller.clearItemValue();
        controller.itemValue.value = '全新值3';
        controller.save();
        verify(mockNavigationService.goBack(result: '全新值3')).called(1);
      });
    });

    group('4.2 生命周期测试', () {
      test('TextController内存管理 - 验证是否有内存泄露风险', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // Act - 多次设置TextController
        controller.setItemValues('标题1', '值1');
        final firstController = controller.textController;
        expect(firstController.text, equals('值1'));

        // 再次设置，验证TextController处理
        controller.setItemValues('标题2', '值2');
        final secondController = controller.textController;
        expect(secondController.text, equals('值2'));

        // Assert - 验证TextController被正确替换
        // 注意：这里每次setItemValues都创建新的TextController
        // 这是当前实现的行为，虽然可能不是最优的
        expect(secondController, isNotNull);
        expect(secondController.text, equals('值2'));

        // 验证Controller仍然可以正常工作
        controller.clearItemValue();
        expect(controller.textController.text, equals(''));
      });

      test('Controller销毁时清理 - 验证onClose行为', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // Act - 初始化TextController
        controller.setItemValues('标题', '值');
        expect(controller.textController.text, equals('值'));

        // 验证TextController可以正常使用
        controller.textController.text = '修改后的值';
        expect(controller.textController.text, equals('修改后的值'));

        // Act - 模拟Controller销毁
        // 注意：onClose方法应该安全地处理TextController的清理
        expect(() => controller.onClose(), returnsNormally);

        // Assert - 验证onClose执行成功
        // 由于onClose中有try-catch，所以不会抛出异常
      });

      test('状态重置 - 验证Controller重用场景', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // Act - 设置初始状态
        controller.setItemValues('初始标题', '初始值');
        controller.itemValue.value = '修改值';

        // 验证状态已设置
        expect(controller.itemTitle.value, equals('初始标题'));
        expect(controller.itemValue.value, equals('修改值'));
        expect(controller.originalValue, equals('初始值'));

        // Act - 重置状态（模拟新的编辑会话）
        controller.setItemValues('新标题', '新值');

        // Assert - 验证状态完全重置
        expect(controller.itemTitle.value, equals('新标题'));
        expect(controller.itemValue.value, equals('新值'));
        expect(controller.originalValue, equals('新值'));
        expect(controller.textController.text, equals('新值'));

        // 验证重置后的Controller功能正常
        controller.clearItemValue();
        expect(controller.itemValue.value, equals(''));
        expect(controller.textController.text, equals(''));

        // 验证重置后的导航功能
        controller.itemValue.value = '重置后的值';
        controller.save();
        verify(mockNavigationService.goBack(result: '重置后的值')).called(1);
      });
    });
  });

  group('Phase 5: 错误处理与边界条件测试', () {
    group('5.1 异常情况测试', () {
      test('late textController访问 - 验证setItemValues调用前访问textController', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // Act & Assert - 验证未初始化时访问textController抛出异常
        expect(() => controller.textController, throwsA(isA<Error>()));
        expect(() => controller.textController.text, throwsA(isA<Error>()));
        expect(() => controller.textController.clear(), throwsA(isA<Error>()));

        // 验证其他方法在textController未初始化时仍能正常工作
        expect(() => controller.save(), returnsNormally);
        expect(() => controller.back(), returnsNormally);

        // 验证clearItemValue在textController未初始化时的处理
        expect(() => controller.clearItemValue(), throwsA(isA<Error>()));

        // 验证设置值后textController正常工作
        controller.setItemValues('标题', '值');
        expect(() => controller.textController.text, returnsNormally);
        expect(controller.textController.text, equals('值'));
      });

      test('NavigationService异常 - 验证导航失败处理', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);
        controller.setItemValues('标题', '值');

        // 模拟NavigationService抛出异常
        when(mockNavigationService.goBack(result: anyNamed('result'))).thenThrow(Exception('Navigation failed'));

        // Act & Assert - 验证save方法在NavigationService异常时的处理
        expect(() => controller.save(), throwsA(isA<Exception>()));

        // 验证back方法在NavigationService异常时的处理
        expect(() => controller.back(), throwsA(isA<Exception>()));

        // 验证异常后Controller状态仍然正常
        expect(controller.itemTitle.value, equals('标题'));
        expect(controller.itemValue.value, equals('值'));
        expect(controller.originalValue, equals('值'));
        expect(controller.textController.text, equals('值'));

        // 验证其他方法仍能正常工作
        expect(() => controller.clearItemValue(), returnsNormally);
        expect(controller.itemValue.value, equals(''));
      });

      test('极长字符串处理 - 验证性能边界', () {
        // Arrange
        final controller = EditItemController(navigationService: mockNavigationService);

        // 创建极长字符串（10K字符）
        final longTitle = 'A' * 10000;
        final longValue = 'B' * 10000;

        // Act - 测试极长字符串的处理
        final stopwatch = Stopwatch()..start();

        controller.setItemValues(longTitle, longValue);

        stopwatch.stop();

        // Assert - 验证性能在合理范围内（应该在100ms内完成）
        expect(stopwatch.elapsedMilliseconds, lessThan(100));

        // 验证状态正确设置
        expect(controller.itemTitle.value, equals(longTitle));
        expect(controller.itemValue.value, equals(longValue));
        expect(controller.originalValue, equals(longValue));
        expect(controller.textController.text, equals(longValue));

        // 验证清除操作性能
        stopwatch.reset();
        stopwatch.start();

        controller.clearItemValue();

        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(50));
        expect(controller.itemValue.value, equals(''));
        expect(controller.textController.text, equals(''));

        // 验证导航操作性能
        controller.itemValue.value = 'C' * 5000;

        stopwatch.reset();
        stopwatch.start();

        controller.save();

        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(50));
        verify(mockNavigationService.goBack(result: 'C' * 5000)).called(1);
      });

      test('Null值处理 - 验证空值保护', () {
        // Arrange & Act - 测试null参数的处理
        final controller = EditItemController(navigationService: mockNavigationService);

        // 虽然setItemValues参数是non-null，但测试边界情况
        // 测试空字符串（最接近null的合法值）
        controller.setItemValues('', '');

        // Assert - 验证空字符串正确处理
        expect(controller.itemTitle.value, equals(''));
        expect(controller.itemValue.value, equals(''));
        expect(controller.originalValue, equals(''));
        expect(controller.textController.text, equals(''));

        // 验证空值导航
        controller.save();
        verify(mockNavigationService.goBack(result: '')).called(1);

        clearInteractions(mockNavigationService);

        controller.back();
        verify(mockNavigationService.goBack(result: '')).called(1);

        // 测试只有标题为空的情况
        clearInteractions(mockNavigationService);

        controller.setItemValues('', '有值');
        expect(controller.itemTitle.value, equals(''));
        expect(controller.itemValue.value, equals('有值'));
        expect(controller.originalValue, equals('有值'));

        // 测试只有值为空的情况
        controller.setItemValues('有标题', '');
        expect(controller.itemTitle.value, equals('有标题'));
        expect(controller.itemValue.value, equals(''));
        expect(controller.originalValue, equals(''));
        expect(controller.textController.text, equals(''));

        // 验证空值清除操作
        controller.clearItemValue();
        expect(controller.itemValue.value, equals(''));
        expect(controller.textController.text, equals(''));

        // 验证设置空值后重新设置正常值
        controller.setItemValues('恢复标题', '恢复值');
        expect(controller.itemTitle.value, equals('恢复标题'));
        expect(controller.itemValue.value, equals('恢复值'));
        expect(controller.textController.text, equals('恢复值'));
      });
    });
  });
}

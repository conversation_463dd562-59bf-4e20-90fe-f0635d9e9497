// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in asset_force_mobile_v2/test/features/me/account/domain/usecase/account_data_usercase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart'
    as _i13;
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/data/model/un_permission_response_entity.dart'
    as _i8;
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting_0_5.dart'
    as _i12;
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_user_tenant_model.dart'
    as _i3;
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/tenant_info_response.dart'
    as _i9;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_my_account_model.dart'
    as _i2;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart'
    as _i10;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart'
    as _i7;
import 'package:asset_force_mobile_v2/features/shared/data/models/user/user_role_response.dart'
    as _i6;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/layout_setting_repository.dart'
    as _i11;
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart'
    as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i14;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeSharedMyAccountModel_0 extends _i1.SmartFake
    implements _i2.SharedMyAccountModel {
  _FakeSharedMyAccountModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSharedUserTenantModel_1 extends _i1.SmartFake
    implements _i3.SharedUserTenantModel {
  _FakeSharedUserTenantModel_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [UserRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserRepository extends _i1.Mock implements _i4.UserRepository {
  MockUserRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.SharedMyAccountModel> getUserInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getUserInfo, []),
            returnValue: _i5.Future<_i2.SharedMyAccountModel>.value(
              _FakeSharedMyAccountModel_0(
                this,
                Invocation.method(#getUserInfo, []),
              ),
            ),
          )
          as _i5.Future<_i2.SharedMyAccountModel>);

  @override
  _i5.Future<_i3.SharedUserTenantModel> getUserTenant() =>
      (super.noSuchMethod(
            Invocation.method(#getUserTenant, []),
            returnValue: _i5.Future<_i3.SharedUserTenantModel>.value(
              _FakeSharedUserTenantModel_1(
                this,
                Invocation.method(#getUserTenant, []),
              ),
            ),
          )
          as _i5.Future<_i3.SharedUserTenantModel>);

  @override
  _i5.Future<bool> getAuthorityInfo(int? functionId) =>
      (super.noSuchMethod(
            Invocation.method(#getAuthorityInfo, [functionId]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<List<_i6.UserRoleResponseData>> getUserRole() =>
      (super.noSuchMethod(
            Invocation.method(#getUserRole, []),
            returnValue: _i5.Future<List<_i6.UserRoleResponseData>>.value(
              <_i6.UserRoleResponseData>[],
            ),
          )
          as _i5.Future<List<_i6.UserRoleResponseData>>);

  @override
  _i5.Future<bool> updateUserInfo(dynamic userInfo) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserInfo, [userInfo]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<List<_i7.SharedUserModel>> getUserList() =>
      (super.noSuchMethod(
            Invocation.method(#getUserList, []),
            returnValue: _i5.Future<List<_i7.SharedUserModel>>.value(
              <_i7.SharedUserModel>[],
            ),
          )
          as _i5.Future<List<_i7.SharedUserModel>>);

  @override
  _i5.Future<_i8.UnPermissionResponseEntity?> getUnPermission() =>
      (super.noSuchMethod(
            Invocation.method(#getUnPermission, []),
            returnValue: _i5.Future<_i8.UnPermissionResponseEntity?>.value(),
          )
          as _i5.Future<_i8.UnPermissionResponseEntity?>);

  @override
  _i5.Future<_i9.TenantInfoResponse?> getTenantInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getTenantInfo, []),
            returnValue: _i5.Future<_i9.TenantInfoResponse?>.value(),
          )
          as _i5.Future<_i9.TenantInfoResponse?>);

  @override
  _i5.Future<List<_i10.SharedRoleModel>> getGroupList(
    String? processDefinitionId,
    String? taskDefKey,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getGroupList, [processDefinitionId, taskDefKey]),
            returnValue: _i5.Future<List<_i10.SharedRoleModel>>.value(
              <_i10.SharedRoleModel>[],
            ),
          )
          as _i5.Future<List<_i10.SharedRoleModel>>);
}

/// A class which mocks [LayoutSettingRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockLayoutSettingRepository extends _i1.Mock
    implements _i11.LayoutSettingRepository {
  MockLayoutSettingRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<List<_i12.SharedLayoutSetting05>> getLayoutSetting() =>
      (super.noSuchMethod(
            Invocation.method(#getLayoutSetting, []),
            returnValue: _i5.Future<List<_i12.SharedLayoutSetting05>>.value(
              <_i12.SharedLayoutSetting05>[],
            ),
          )
          as _i5.Future<List<_i12.SharedLayoutSetting05>>);
}

/// A class which mocks [IStorageUtils].
///
/// See the documentation for Mockito's code generation for more information.
class MockIStorageUtils extends _i1.Mock implements _i13.IStorageUtils {
  MockIStorageUtils() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<void> init() =>
      (super.noSuchMethod(
            Invocation.method(#init, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> initPersonalStorage({required String? cryptedName}) =>
      (super.noSuchMethod(
            Invocation.method(#initPersonalStorage, [], {
              #cryptedName: cryptedName,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  T? getValue<T>(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#getValue, [key], {#personal: personal}),
          )
          as T?);

  @override
  _i5.Future<void> setValue<T>(
    String? key,
    T? value, {
    bool? personal = false,
    int? durationInSeconds,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #setValue,
              [key, value],
              {#personal: personal, #durationInSeconds: durationInSeconds},
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> removeValue(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#removeValue, [key], {#personal: personal}),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  bool containsKey(String? key, {bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#containsKey, [key], {#personal: personal}),
            returnValue: false,
          )
          as bool);

  @override
  _i5.Future<void> clearAll({bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#clearAll, [], {#personal: personal}),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  List<String> getAllKeys({bool? personal = false}) =>
      (super.noSuchMethod(
            Invocation.method(#getAllKeys, [], {#personal: personal}),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  String getAssetScanLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getAssetScanLocation, []),
            returnValue: _i14.dummyValue<String>(
              this,
              Invocation.method(#getAssetScanLocation, []),
            ),
          )
          as String);

  @override
  _i5.Future<void> setAssetScanLocation({required String? assetLocation}) =>
      (super.noSuchMethod(
            Invocation.method(#setAssetScanLocation, [], {
              #assetLocation: assetLocation,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

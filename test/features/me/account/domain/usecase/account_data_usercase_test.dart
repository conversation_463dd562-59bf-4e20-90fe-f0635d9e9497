import 'dart:async';
import 'dart:io';

import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/me/account/domain/usecase/account_data_usercase.dart';
import 'package:asset_force_mobile_v2/features/me/account/presentation/models/account_ui_state.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting_0_5.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_my_account_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/user_role_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/layout_setting_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// 自动生成 mock 类
@GenerateMocks([UserRepository, LayoutSettingRepository, IStorageUtils])
import 'account_data_usercase_test.mocks.dart';

void main() {
  group('AccountDataUseCase', () {
    late AccountDataUseCase accountDataUseCase;
    late MockUserRepository mockUserRepository;
    late MockLayoutSettingRepository mockLayoutSettingRepository;
    late MockIStorageUtils mockStorageUtils;

    setUp(() {
      // 初始化LogUtil
      LogUtil.initialize();

      mockUserRepository = MockUserRepository();
      mockLayoutSettingRepository = MockLayoutSettingRepository();
      mockStorageUtils = MockIStorageUtils();

      accountDataUseCase = AccountDataUseCase(mockUserRepository, mockLayoutSettingRepository, mockStorageUtils);
    });

    tearDown(() {
      // 测试后清理
    });

    group('基础功能测试', () {
      test('应该能够创建 AccountDataUseCase 实例', () {
        expect(accountDataUseCase, isNotNull);
        expect(accountDataUseCase, isA<AccountDataUseCase>());
      });

      test('应该实现 UseCase 接口', () {
        expect(accountDataUseCase, isA<UseCase<AccountUIState, NoParams>>());
      });

      test('应该正确注入所有依赖', () {
        expect(accountDataUseCase.userRepository, equals(mockUserRepository));
        expect(accountDataUseCase.layoutSettingRepository, equals(mockLayoutSettingRepository));
        expect(accountDataUseCase.storageUtils, equals(mockStorageUtils));
      });
    });

    group('call() 方法测试', () {
      late SharedMyAccountModel testUserAccountModel;
      late List<UserRoleResponseData> testUserRoles;
      late List<SharedLayoutSetting05> testLayoutSettings;

      setUp(() {
        // 创建测试数据
        testUserAccountModel = _createTestUserAccountModel();
        testUserRoles = _createTestUserRoles();
        testLayoutSettings = _createTestLayoutSettings();
      });

      test('应该成功获取用户账户数据', () async {
        // 设置 mock 返回值
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => testUserAccountModel);
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => testUserRoles);
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => testLayoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(true);

        // 执行测试
        final result = await accountDataUseCase.call(NoParams());

        // 验证
        expect(result, isNotNull);
        expect(result, isA<AccountUIState>());
        verify(mockUserRepository.getUserInfo()).called(1);
        verify(mockUserRepository.getUserRole()).called(1);
        verify(mockLayoutSettingRepository.getLayoutSetting()).called(1);
      });

      test('应该正确映射用户基本信息', () async {
        // 设置 mock 返回值
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => testUserAccountModel);
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => testUserRoles);
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => testLayoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 执行测试
        final result = await accountDataUseCase.call(NoParams());

        // 验证用户基本信息映射
        expect(result.userId, equals(123));
        expect(result.userName, equals('<EMAIL>'));
        expect(result.firstName, equals('太郎'));
        expect(result.lastName, equals('田中'));
        expect(result.firstNameKana, equals('タロウ'));
        expect(result.lastNameKana, equals('タナカ'));
        expect(result.recoverableLimit, equals('2024-12-31'));
        expect(result.location, equals('東京'));
        expect(result.nationCode, equals('+81'));
      });

      test('应该正确处理用户角色列表', () async {
        // 设置 mock 返回值
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => testUserAccountModel);
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => testUserRoles);
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => testLayoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 执行测试
        final result = await accountDataUseCase.call(NoParams());

        // 验证角色列表处理
        expect(result.roleName, equals('管理者,一般ユーザー'));
      });

      test('应该正确设置两步验证状态', () async {
        // 测试启用两步验证
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => testUserAccountModel);
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => testUserRoles);
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => testLayoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(true);

        final result = await accountDataUseCase.call(NoParams());
        expect(result.enableTwoStep, true);

        // 测试禁用两步验证
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);
        final result2 = await accountDataUseCase.call(NoParams());
        expect(result2.enableTwoStep, false);

        // 测试空值情况
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(null);
        final result3 = await accountDataUseCase.call(NoParams());
        expect(result3.enableTwoStep, false);
      });

      test('应该正确隐藏电话号码', () async {
        // 设置 mock 返回值
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => testUserAccountModel);
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => testUserRoles);
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => testLayoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 执行测试
        final result = await accountDataUseCase.call(NoParams());

        // 验证电话号码被隐藏（假设hidePhoneNumber()方法会隐藏部分数字）
        expect(result.tel, isNotEmpty);
        expect(result.tel, isNot(equals('***********'))); // 原始号码不应该直接显示
      });

      test('应该正确处理布局设置列表', () async {
        // 设置 mock 返回值
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => testUserAccountModel);
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => testUserRoles);
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => testLayoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 执行测试
        final result = await accountDataUseCase.call(NoParams());

        // 验证布局设置列表处理
        expect(result.layoutSettingList, isNotEmpty);
        expect(result.layoutSettingList.length, equals(testLayoutSettings.length));

        // 验证カスタム項目被正确设置
        expect(result.appurtenancesInformationActionItemListDict['カスタム項目'], isNotNull);
      });
    });

    group('call() 方法边界条件', () {
      test('应该处理空的用户信息', () async {
        // 创建空用户信息
        final emptyUserAccount = SharedMyAccountModel(
          code: 200,
          msg: 'Success',
          data: null, // 用户数据为null
          hasAssetLocation: false,
        );

        // 设置 mock 返回值
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => emptyUserAccount);
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => []);
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => []);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(null);

        // 执行测试
        final result = await accountDataUseCase.call(NoParams());

        // 验证默认值设置
        expect(result.userId, equals(0)); // 应该设置为默认值0
        expect(result.userName, equals(''));
        expect(result.firstName, equals(''));
        expect(result.lastName, equals(''));
        expect(result.tel, equals(''));
        expect(result.enableTwoStep, false);
        expect(result.hasAssetLocation, false);
      });

      test('应该处理空的角色列表', () async {
        // 设置 mock 返回值 - 空角色列表
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => _createTestUserAccountModel());
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => []); // 空角色列表
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => _createTestLayoutSettings());
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 执行测试
        final result = await accountDataUseCase.call(NoParams());

        // 验证空角色列表处理
        expect(result.roleName, equals('')); // 空列表应该返回空字符串
      });

      test('应该处理空的布局设置', () async {
        // 设置 mock 返回值 - 空布局设置
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => _createTestUserAccountModel());
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => []); // 空布局设置
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 执行测试
        final result = await accountDataUseCase.call(NoParams());

        // 验证空布局设置处理
        expect(result.layoutSettingList, isEmpty);
        expect(result.appurtenancesInformationActionItemListDict['カスタム項目'], isEmpty);
      });

      test('应该为null值设置默认值', () async {
        // 创建包含null值的用户数据
        final userDataWithNulls = SharedUserModel(
          userId: null,
          userName: null,
          firstName: null,
          lastName: null,
          tel: null,
          location: null,
          nationCode: null,
          recoverableLimit: null,
          userText: null,
        );

        final userAccountWithNulls = SharedMyAccountModel(
          code: 200,
          msg: 'Success',
          data: userDataWithNulls,
          hasAssetLocation: null,
        );

        // 设置 mock 返回值
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => userAccountWithNulls);
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => _createTestLayoutSettings());
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(null);

        // 执行测试
        final result = await accountDataUseCase.call(NoParams());

        // 验证所有null值都有合适的默认值
        expect(result.userId, equals(0)); // null userId 应该默认为 0
        expect(result.userName, equals(''));
        expect(result.firstName, equals(''));
        expect(result.lastName, equals(''));
        expect(result.tel, equals(''));
        expect(result.location, equals(''));
        expect(result.nationCode, equals(''));
        expect(result.recoverableLimit, equals(''));
        expect(result.hasAssetLocation, equals(false)); // null 应该默认为 false
        expect(result.enableTwoStep, equals(false)); // null 应该默认为 false
      });

      test('应该处理存储中缺失的配置', () async {
        // 设置 mock 返回值 - 存储中没有配置
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => _createTestUserAccountModel());
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => _createTestLayoutSettings());
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(null); // 存储中没有这个配置

        // 执行测试
        final result = await accountDataUseCase.call(NoParams());

        // 验证缺失配置的默认值处理
        expect(result.enableTwoStep, false); // 缺失时应该默认为false

        // 验证存储被正确调用
        verify(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).called(1);
      });

      test('应该处理部分null的布局设置项', () async {
        // 创建包含null值的布局设置
        final layoutSettingsWithNulls = [
          SharedLayoutSetting05(
            itemName: null, // itemName为null
            itemType: 'input',
            mobileFlg: '1',
            defaultData: 'test',
          ),
          SharedLayoutSetting05(
            itemName: 'ユーザー名',
            itemType: null, // itemType为null
            mobileFlg: '1',
            defaultData: null,
          ),
          SharedLayoutSetting05(
            itemName: 'テストフィールド',
            itemType: 'input',
            mobileFlg: null, // mobileFlg为null
            defaultData: 'test value',
          ),
        ];

        // 设置 mock 返回值
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => _createTestUserAccountModel());
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => layoutSettingsWithNulls);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 执行测试
        final result = await accountDataUseCase.call(NoParams());

        // 验证带null值的布局设置被正确处理
        expect(result.layoutSettingList, isNotEmpty);
        expect(result.layoutSettingList.length, equals(3));

        // null的mobileFlg不应该被添加到カスタム項目中
        final customItems = result.appurtenancesInformationActionItemListDict['カスタム項目'] as List?;
        expect(customItems, isNotNull);
        // 只有mobileFlg为'1'的项目才会被添加
        expect(customItems!.length, lessThanOrEqualTo(2));
      });
    });

    group('_prepareLayoutSettingList() 方法测试', () {
      late AccountUIState testModel;

      setUp(() {
        // 创建测试用的AccountUIState
        testModel = AccountUIState(123);
        testModel.lastName = '田中';
        testModel.firstName = '太郎';
        testModel.setUserText('{"customField1": "value1", "customField2": "value2"}');
      });

      test('应该正确处理用户名项目', () async {
        // 创建包含用户名项目的布局设置
        final layoutSettings = [
          SharedLayoutSetting05(itemName: 'ユーザー名', itemType: 'label', mobileFlg: '1', defaultData: null),
        ];

        testModel.layoutSettingList = layoutSettings;

        // 调用私有方法进行测试（通过call方法间接测试）
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => _createTestUserAccountModel());
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => layoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        final result = await accountDataUseCase.call(NoParams());

        // 验证用户名被正确拼接
        final userNameItem = result.layoutSettingList.firstWhere((item) => item.itemName == 'ユーザー名');
        expect(userNameItem.defaultData, equals('田中 太郎'));
      });

      test('应该正确处理时间项目', () async {
        // 创建包含时间项目的布局设置
        final layoutSettings = [
          SharedLayoutSetting05(itemName: '処理時間', itemType: 'date', mobileFlg: '1', defaultData: null),
          SharedLayoutSetting05(itemName: '更新時間', itemType: 'date', mobileFlg: '1', defaultData: null),
        ];

        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => _createTestUserAccountModel());
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => layoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        final result = await accountDataUseCase.call(NoParams());

        // 验证时间项目被设置为当前格式化时间
        final processTimeItem = result.layoutSettingList.firstWhere((item) => item.itemName == '処理時間');
        final updateTimeItem = result.layoutSettingList.firstWhere((item) => item.itemName == '更新時間');

        expect(processTimeItem.defaultData, isNotNull);
        expect(processTimeItem.defaultData, isNotEmpty);
        expect(updateTimeItem.defaultData, isNotNull);
        expect(updateTimeItem.defaultData, isNotEmpty);

        // 验证时间格式（应该包含日期和时间）
        expect(processTimeItem.defaultData, matches(r'\d{4}/\d{2}/\d{2} \d{2}:\d{2}'));
        expect(updateTimeItem.defaultData, matches(r'\d{4}/\d{2}/\d{2} \d{2}:\d{2}'));
      });

      test('应该正确处理位置项目', () async {
        // 创建包含位置项目的布局设置
        final layoutSettings = [
          SharedLayoutSetting05(itemName: 'ロケーション', itemType: 'label', mobileFlg: '1', defaultData: null),
        ];

        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => _createTestUserAccountModel());
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => layoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        final result = await accountDataUseCase.call(NoParams());

        // 验证位置项目被设置为默认值
        final locationItem = result.layoutSettingList.firstWhere((item) => item.itemName == 'ロケーション');
        expect(locationItem.defaultData, equals('ロケーションが特定できません'));
      });

      test('应该正确筛选移动端项目', () async {
        // 创建混合的布局设置（移动端和桌面端）
        final layoutSettings = [
          SharedLayoutSetting05(
            itemName: 'モバイル項目1',
            itemType: 'input',
            mobileFlg: '1', // 移动端项目
            defaultData: 'mobile1',
          ),
          SharedLayoutSetting05(
            itemName: 'デスクトップ項目',
            itemType: 'input',
            mobileFlg: '0', // 桌面端项目
            defaultData: 'desktop',
          ),
          SharedLayoutSetting05(
            itemName: 'モバイル項目2',
            itemType: 'textarea',
            mobileFlg: '1', // 移动端项目
            defaultData: 'mobile2',
          ),
          SharedLayoutSetting05(
            itemName: 'フラグなし項目',
            itemType: 'input',
            mobileFlg: null, // 没有标志的项目
            defaultData: 'no_flag',
          ),
        ];

        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => _createTestUserAccountModel());
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => layoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        final result = await accountDataUseCase.call(NoParams());

        // 验证只有移动端项目被添加到カスタム項目中
        final customItems = result.appurtenancesInformationActionItemListDict['カスタム項目'] as List<SharedLayoutSetting05>;
        expect(customItems.length, equals(2)); // 只有2个移动端项目

        final itemNames = customItems.map((item) => item.itemName).toList();
        expect(itemNames, contains('モバイル項目1'));
        expect(itemNames, contains('モバイル項目2'));
        expect(itemNames, isNot(contains('デスクトップ項目')));
        expect(itemNames, isNot(contains('フラグなし項目')));
      });

      test('应该正确设置用户文本数据', () async {
        // 创建包含完整userText的用户账户模型
        final userDataWithCustomText = SharedUserModel(
          userId: 123,
          tenantId: 'test-tenant',
          userName: '<EMAIL>',
          lastName: '田中',
          firstName: '太郎',
          lastNameKana: 'タナカ',
          firstNameKana: 'タロウ',
          nationCode: '+81',
          tel: '***********',
          recoverableLimit: '2024-12-31',
          location: '東京',
          userText: '{"customField1": "value1", "customField2": "value2"}', // 包含测试需要的字段
        );

        final userAccountWithCustomText = SharedMyAccountModel(
          code: 200,
          msg: 'Success',
          data: userDataWithCustomText,
          hasAssetLocation: true,
          enableTwoStep: false,
          membership: 1,
          tenantName: 'Test Tenant',
          accessKeyKbn: '1',
          mfaType: 'SMS',
        );

        // 创建包含自定义项目的布局设置
        final layoutSettings = [
          SharedLayoutSetting05(itemName: 'customField1', itemType: 'input', mobileFlg: '1', defaultData: null),
          SharedLayoutSetting05(itemName: 'customField2', itemType: 'textarea', mobileFlg: '1', defaultData: null),
          SharedLayoutSetting05(itemName: 'nonExistentField', itemType: 'input', mobileFlg: '1', defaultData: null),
        ];

        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => userAccountWithCustomText);
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => layoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        final result = await accountDataUseCase.call(NoParams());

        // 验证用户文本数据被正确设置
        final customField1 = result.layoutSettingList.firstWhere((item) => item.itemName == 'customField1');
        final customField2 = result.layoutSettingList.firstWhere((item) => item.itemName == 'customField2');
        final nonExistentField = result.layoutSettingList.firstWhere((item) => item.itemName == 'nonExistentField');

        expect(customField1.defaultData, equals('value1')); // 从userText中获取
        expect(customField2.defaultData, equals('value2')); // 从userText中获取
        expect(nonExistentField.defaultData, isNull); // userText中不存在该字段
      });

      test('应该处理空的项目名称', () async {
        // 创建包含空项目名称的布局设置
        final layoutSettings = [
          SharedLayoutSetting05(
            itemName: null, // 空项目名称
            itemType: 'input',
            mobileFlg: '1',
            defaultData: 'test value',
          ),
          SharedLayoutSetting05(
            itemName: '', // 空字符串项目名称
            itemType: 'input',
            mobileFlg: '1',
            defaultData: 'empty name',
          ),
        ];

        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => _createTestUserAccountModel());
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => layoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        final result = await accountDataUseCase.call(NoParams());

        // 验证空项目名称不会导致错误
        expect(result.layoutSettingList.length, equals(2));

        // 这些项目仍然应该被添加到カスタム項目中（如果mobileFlg为'1'）
        final customItems = result.appurtenancesInformationActionItemListDict['カスタム項目'] as List<SharedLayoutSetting05>;
        expect(customItems.length, equals(2));
      });

      test('应该处理混合的项目类型', () async {
        // 创建包含完整userText的用户账户模型
        final userDataWithCustomText = SharedUserModel(
          userId: 123,
          tenantId: 'test-tenant',
          userName: '<EMAIL>',
          lastName: '田中',
          firstName: '太郎',
          lastNameKana: 'タナカ',
          firstNameKana: 'タロウ',
          nationCode: '+81',
          tel: '***********',
          recoverableLimit: '2024-12-31',
          location: '東京',
          userText: '{"customField1": "value1"}', // 包含customField1
        );

        final userAccountWithCustomText = SharedMyAccountModel(
          code: 200,
          msg: 'Success',
          data: userDataWithCustomText,
          hasAssetLocation: true,
          enableTwoStep: false,
          membership: 1,
          tenantName: 'Test Tenant',
          accessKeyKbn: '1',
          mfaType: 'SMS',
        );

        // 创建包含所有特殊项目类型的布局设置
        final layoutSettings = [
          SharedLayoutSetting05(itemName: 'ユーザー名', itemType: 'label', mobileFlg: '1', defaultData: null),
          SharedLayoutSetting05(itemName: '処理時間', itemType: 'date', mobileFlg: '1', defaultData: null),
          SharedLayoutSetting05(itemName: 'ロケーション', itemType: 'label', mobileFlg: '1', defaultData: null),
          SharedLayoutSetting05(itemName: 'customField1', itemType: 'input', mobileFlg: '1', defaultData: null),
          SharedLayoutSetting05(
            itemName: '通常項目',
            itemType: 'textarea',
            mobileFlg: '0', // 桌面端项目
            defaultData: 'normal value',
          ),
        ];

        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => userAccountWithCustomText);
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => layoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        final result = await accountDataUseCase.call(NoParams());

        // 验证所有特殊项目都被正确处理
        final userNameItem = result.layoutSettingList.firstWhere((item) => item.itemName == 'ユーザー名');
        final processTimeItem = result.layoutSettingList.firstWhere((item) => item.itemName == '処理時間');
        final locationItem = result.layoutSettingList.firstWhere((item) => item.itemName == 'ロケーション');
        final customField = result.layoutSettingList.firstWhere((item) => item.itemName == 'customField1');

        expect(userNameItem.defaultData, equals('田中 太郎'));
        expect(processTimeItem.defaultData, matches(r'\d{4}/\d{2}/\d{2} \d{2}:\d{2}'));
        expect(locationItem.defaultData, equals('ロケーションが特定できません'));
        expect(customField.defaultData, equals('value1'));

        // 验证只有移动端项目被添加到カスタム項目中
        final customItems = result.appurtenancesInformationActionItemListDict['カスタム項目'] as List<SharedLayoutSetting05>;
        expect(customItems.length, equals(4)); // 4个移动端项目

        final mobileItemNames = customItems.map((item) => item.itemName).toList();
        expect(mobileItemNames, contains('ユーザー名'));
        expect(mobileItemNames, contains('処理時間'));
        expect(mobileItemNames, contains('ロケーション'));
        expect(mobileItemNames, contains('customField1'));
        expect(mobileItemNames, isNot(contains('通常項目')));
      });
    });

    group('updateUserInfo() 方法测试', () {
      late AccountUIState testState;

      setUp(() {
        // 创建测试用的AccountUIState
        testState = AccountUIState(123);
        testState.userName = '<EMAIL>';
        testState.firstName = '太郎';
        testState.lastName = '田中';
        testState.firstNameKana = 'タロウ';
        testState.lastNameKana = 'タナカ';
        testState.tel = '***********';
        testState.nationCode = '+81';
        testState.recoverableLimit = '2024-12-31';
        testState.location = '東京';
        testState.setUserText('{"customField": "value"}');
      });

      test('应该成功更新用户信息', () async {
        // 设置 mock 返回值
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);
        when(mockUserRepository.updateUserInfo(any)).thenAnswer((_) async => true);

        // 执行测试
        final result = await accountDataUseCase.updateUserInfo(testState);

        // 验证结果
        expect(result, true);

        // 验证存储被正确调用
        verify(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).called(1);

        // 验证repository被正确调用
        verify(mockUserRepository.updateUserInfo(any)).called(1);
      });

      test('应该正确处理两步验证启用状态', () async {
        // 测试启用两步验证的情况
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(true);
        when(mockStorageUtils.getValue<String>(StorageKeys.tel + testState.userName)).thenReturn('***********');
        when(mockStorageUtils.getValue<String>(StorageKeys.nationCode + testState.userName)).thenReturn('+86');
        when(mockUserRepository.updateUserInfo(any)).thenAnswer((_) async => true);

        final result = await accountDataUseCase.updateUserInfo(testState);

        expect(result, true);

        // 验证从存储中获取了两步验证相关的电话号码和国家代码
        verify(mockStorageUtils.getValue<String>(StorageKeys.tel + testState.userName)).called(1);
        verify(mockStorageUtils.getValue<String>(StorageKeys.nationCode + testState.userName)).called(1);

        // 验证更新对象中包含正确的数据
        final captured = verify(mockUserRepository.updateUserInfo(captureAny)).captured.single as Map<String, dynamic>;
        expect(captured['enableTwoStep'], true);
        expect(captured['tel'], '***********'); // 从存储获取的值
        expect(captured['nationCode'], '+86'); // 从存储获取的值
      });

      test('应该正确处理两步验证禁用状态', () async {
        // 测试禁用两步验证的情况
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);
        when(mockUserRepository.updateUserInfo(any)).thenAnswer((_) async => true);

        final result = await accountDataUseCase.updateUserInfo(testState);

        expect(result, true);

        // 验证不会从存储中获取电话号码和国家代码
        verifyNever(mockStorageUtils.getValue<String>(StorageKeys.tel + testState.userName));
        verifyNever(mockStorageUtils.getValue<String>(StorageKeys.nationCode + testState.userName));

        // 验证更新对象中包含正确的数据
        final captured = verify(mockUserRepository.updateUserInfo(captureAny)).captured.single as Map<String, dynamic>;
        expect(captured['enableTwoStep'], false);
        expect(captured['tel'], testState.tel); // 使用state中的值
        expect(captured['nationCode'], testState.nationCode); // 使用state中的值
      });

      test('应该构建正确的更新对象', () async {
        // 设置测试数据
        testState.tmpLocation = '大阪'; // 设置临时位置

        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);
        when(mockUserRepository.updateUserInfo(any)).thenAnswer((_) async => true);

        await accountDataUseCase.updateUserInfo(testState);

        // 验证更新对象的完整性
        final captured = verify(mockUserRepository.updateUserInfo(captureAny)).captured.single as Map<String, dynamic>;

        expect(captured['userName'], equals('<EMAIL>'));
        expect(captured['firstName'], equals('太郎'));
        expect(captured['lastName'], equals('田中'));
        expect(captured['lastNameKana'], equals('タナカ'));
        expect(captured['firstNameKana'], equals('タロウ'));
        expect(captured['tel'], equals('***********'));
        expect(captured['recoverableLimit'], equals('2024-12-31'));
        expect(captured['enableTwoStep'], equals(false));
        expect(captured['nationCode'], equals('+81'));
        expect(captured['userText'], equals('{"customField": "value"}'));
        expect(captured['location'], equals('大阪')); // 应该使用tmpLocation
      });

      test('应该处理tmpLocation为null的情况', () async {
        // tmpLocation为null，应该使用location
        testState.tmpLocation = null;

        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);
        when(mockUserRepository.updateUserInfo(any)).thenAnswer((_) async => true);

        await accountDataUseCase.updateUserInfo(testState);

        final captured = verify(mockUserRepository.updateUserInfo(captureAny)).captured.single as Map<String, dynamic>;
        expect(captured['location'], equals('東京')); // 应该使用原始location
      });

      test('应该验证必填字段', () async {
        // 测试lastName为空的情况
        testState.lastName = '';

        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        final result = await accountDataUseCase.updateUserInfo(testState);

        expect(result, false); // 应该返回false
        verifyNever(mockUserRepository.updateUserInfo(any)); // 不应该调用repository
      });

      test('应该验证firstName必填', () async {
        // 测试firstName为空的情况
        testState.firstName = '';

        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        final result = await accountDataUseCase.updateUserInfo(testState);

        expect(result, false); // 应该返回false
        verifyNever(mockUserRepository.updateUserInfo(any)); // 不应该调用repository
      });

      test('应该验证两个必填字段都为空', () async {
        // 测试两个必填字段都为空的情况
        testState.lastName = '';
        testState.firstName = '';

        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        final result = await accountDataUseCase.updateUserInfo(testState);

        expect(result, false); // 应该返回false
        verifyNever(mockUserRepository.updateUserInfo(any)); // 不应该调用repository
      });

      test('应该处理存储中缺失的两步验证配置', () async {
        // 存储中没有两步验证配置
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(null);
        when(mockUserRepository.updateUserInfo(any)).thenAnswer((_) async => true);

        final result = await accountDataUseCase.updateUserInfo(testState);

        expect(result, true);

        // 验证更新对象中两步验证为false（默认值）
        final captured = verify(mockUserRepository.updateUserInfo(captureAny)).captured.single as Map<String, dynamic>;
        expect(captured['enableTwoStep'], false);
      });

      test('应该处理存储中缺失的电话号码配置', () async {
        // 启用两步验证但存储中没有对应的电话号码
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(true);
        when(mockStorageUtils.getValue<String>(StorageKeys.tel + testState.userName)).thenReturn(null);
        when(mockStorageUtils.getValue<String>(StorageKeys.nationCode + testState.userName)).thenReturn(null);
        when(mockUserRepository.updateUserInfo(any)).thenAnswer((_) async => true);

        final result = await accountDataUseCase.updateUserInfo(testState);

        expect(result, true);

        // 验证更新对象中使用空字符串作为默认值
        final captured = verify(mockUserRepository.updateUserInfo(captureAny)).captured.single as Map<String, dynamic>;
        expect(captured['tel'], ''); // 缺失时应该为空字符串
        expect(captured['nationCode'], ''); // 缺失时应该为空字符串
      });

      test('应该处理repository更新失败', () async {
        // repository返回失败
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);
        when(mockUserRepository.updateUserInfo(any)).thenAnswer((_) async => false);

        final result = await accountDataUseCase.updateUserInfo(testState);

        expect(result, false); // 应该返回false
        verify(mockUserRepository.updateUserInfo(any)).called(1);
      });
    });

    group('异常处理测试', () {
      test('应该处理用户仓库异常', () async {
        // 设置用户仓库抛出异常
        when(mockUserRepository.getUserInfo()).thenThrow(Exception('Network error'));
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => _createTestLayoutSettings());
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 验证异常被抛出
        expect(() => accountDataUseCase.call(NoParams()), throwsA(isA<Exception>()));
      });

      test('应该处理用户角色仓库异常', () async {
        // 设置角色仓库抛出异常
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => _createTestUserAccountModel());
        when(mockUserRepository.getUserRole()).thenThrow(Exception('Database error'));
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => _createTestLayoutSettings());
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 验证异常被抛出
        expect(() => accountDataUseCase.call(NoParams()), throwsA(isA<Exception>()));
      });

      test('应该处理布局设置仓库异常', () async {
        // 设置布局设置仓库抛出异常
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => _createTestUserAccountModel());
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenThrow(Exception('Service unavailable'));
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 验证异常被抛出
        expect(() => accountDataUseCase.call(NoParams()), throwsA(isA<Exception>()));
      });

      test('应该处理存储异常', () async {
        // 设置存储抛出异常
        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => _createTestUserAccountModel());
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => _createTestLayoutSettings());
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenThrow(Exception('Storage error'));

        // 验证异常被抛出
        expect(() => accountDataUseCase.call(NoParams()), throwsA(isA<Exception>()));
      });

      test('应该处理网络异常', () async {
        // 模拟网络连接异常
        when(mockUserRepository.getUserInfo()).thenThrow(SocketException('Network unreachable'));
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => _createTestLayoutSettings());
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 验证网络异常被抛出
        expect(() => accountDataUseCase.call(NoParams()), throwsA(isA<SocketException>()));
      });

      test('应该处理超时异常', () async {
        // 模拟请求超时
        when(mockUserRepository.getUserInfo()).thenThrow(TimeoutException('Request timeout', Duration(seconds: 30)));
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => _createTestLayoutSettings());
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 验证超时异常被抛出
        expect(() => accountDataUseCase.call(NoParams()), throwsA(isA<TimeoutException>()));
      });

      test('应该处理多个依赖同时异常', () async {
        // 设置多个依赖都抛出异常
        when(mockUserRepository.getUserInfo()).thenThrow(Exception('User service error'));
        when(mockUserRepository.getUserRole()).thenThrow(Exception('Role service error'));
        when(mockLayoutSettingRepository.getLayoutSetting()).thenThrow(Exception('Layout service error'));
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenThrow(Exception('Storage error'));

        // 验证第一个遇到的异常被抛出（getUserInfo先执行）
        expect(
          () => accountDataUseCase.call(NoParams()),
          throwsA(predicate((e) => e is Exception && e.toString().contains('User service error'))),
        );
      });

      test('应该处理updateUserInfo中的仓库异常', () async {
        // 创建测试状态
        final testState = AccountUIState(123);
        testState.firstName = '太郎';
        testState.lastName = '田中';

        // 设置存储正常但仓库抛出异常
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);
        when(mockUserRepository.updateUserInfo(any)).thenThrow(Exception('Update failed'));

        // 验证异常被抛出
        expect(() => accountDataUseCase.updateUserInfo(testState), throwsA(isA<Exception>()));
      });

      test('应该处理updateUserInfo中的存储异常', () async {
        // 创建测试状态
        final testState = AccountUIState(123);
        testState.firstName = '太郎';
        testState.lastName = '田中';

        // 设置存储抛出异常
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenThrow(Exception('Storage access denied'));

        // 验证异常被抛出
        expect(() => accountDataUseCase.updateUserInfo(testState), throwsA(isA<Exception>()));
      });

      test('应该处理JSON解析异常', () async {
        // 创建包含无效JSON的用户数据
        final userDataWithInvalidJson = SharedUserModel(
          userId: 123,
          tenantId: 'test-tenant',
          userName: '<EMAIL>',
          lastName: '田中',
          firstName: '太郎',
          userText: 'invalid json string', // 无效的JSON
        );

        final userAccountWithInvalidJson = SharedMyAccountModel(
          code: 200,
          msg: 'Success',
          data: userDataWithInvalidJson,
        );

        // 创建需要解析userText的布局设置
        final layoutSettings = [
          SharedLayoutSetting05(itemName: 'customField1', itemType: 'input', mobileFlg: '1', defaultData: null),
        ];

        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => userAccountWithInvalidJson);
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => layoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // JSON解析异常应该被抛出
        expect(() => accountDataUseCase.call(NoParams()), throwsA(isA<FormatException>()));
      });

      test('应该处理类型转换异常', () async {
        // 创建包含错误类型数据的用户数据
        final userDataWithWrongType = SharedUserModel(
          userId: 123,
          tenantId: 'test-tenant',
          userName: '<EMAIL>',
          lastName: '田中',
          firstName: '太郎',
          userText: '{"customField1": 123}', // 数字类型而不是字符串
        );

        final userAccountWithWrongType = SharedMyAccountModel(code: 200, msg: 'Success', data: userDataWithWrongType);

        final layoutSettings = [
          SharedLayoutSetting05(itemName: 'customField1', itemType: 'input', mobileFlg: '1', defaultData: null),
        ];

        when(mockUserRepository.getUserInfo()).thenAnswer((_) async => userAccountWithWrongType);
        when(mockUserRepository.getUserRole()).thenAnswer((_) async => _createTestUserRoles());
        when(mockLayoutSettingRepository.getLayoutSetting()).thenAnswer((_) async => layoutSettings);
        when(mockStorageUtils.getValue<bool>(StorageKeys.enableTwoStep)).thenReturn(false);

        // 类型转换异常应该被抛出
        expect(() => accountDataUseCase.call(NoParams()), throwsA(isA<TypeError>()));
      });
    });
  });
}

// 测试数据工厂方法
SharedMyAccountModel _createTestUserAccountModel() {
  final userData = SharedUserModel(
    userId: 123,
    tenantId: 'test-tenant',
    userName: '<EMAIL>',
    lastName: '田中',
    firstName: '太郎',
    lastNameKana: 'タナカ',
    firstNameKana: 'タロウ',
    nationCode: '+81',
    tel: '***********',
    recoverableLimit: '2024-12-31',
    location: '東京',
    userText: '{"customField1": "value1"}',
  );

  return SharedMyAccountModel(
    code: 200,
    msg: 'Success',
    data: userData,
    hasAssetLocation: true,
    enableTwoStep: false,
    membership: 1,
    tenantName: 'Test Tenant',
    accessKeyKbn: '1',
    mfaType: 'SMS',
  );
}

List<UserRoleResponseData> _createTestUserRoles() {
  return [
    UserRoleResponseData(
      '<EMAIL>', // userName
      123, // userId
      1, // roleId
      'test-tenant', // tenantId
      '1', // mainFlg
      '1', // createdById
      '2024-01-01', // createdDate
      '1', // modifiedById
      '2024-01-01', // modifiedDate
      '管理者', // roleName
      '0', // mfaFlg
    ),
    UserRoleResponseData(
      '<EMAIL>', // userName
      123, // userId
      2, // roleId
      'test-tenant', // tenantId
      '0', // mainFlg
      '1', // createdById
      '2024-01-01', // createdDate
      '1', // modifiedById
      '2024-01-01', // modifiedDate
      '一般ユーザー', // roleName
      '0', // mfaFlg
    ),
  ];
}

List<SharedLayoutSetting05> _createTestLayoutSettings() {
  return [
    SharedLayoutSetting05(itemName: 'ユーザー名', itemType: 'label', mobileFlg: '1', defaultData: null),
    SharedLayoutSetting05(itemName: '処理時間', itemType: 'date', mobileFlg: '1', defaultData: null),
    SharedLayoutSetting05(itemName: 'ロケーション', itemType: 'label', mobileFlg: '1', defaultData: null),
    SharedLayoutSetting05(itemName: 'カスタムフィールド', itemType: 'input', mobileFlg: '1', defaultData: 'test value'),
    SharedLayoutSetting05(
      itemName: 'デスクトップ専用',
      itemType: 'input',
      mobileFlg: '0', // 移动端不显示
      defaultData: 'desktop only',
    ),
  ];
}

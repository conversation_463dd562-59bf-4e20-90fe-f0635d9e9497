import 'package:asset_force_mobile_v2/core/utils/expression_calculator.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ExpressionCalculator Tests', () {
    test('基本运算测试', () {
      expect(ExpressionCalculator.calculate('1 + 2'), '3');
      expect(ExpressionCalculator.calculate('2 - 1'), '1');
      expect(ExpressionCalculator.calculate('2 * 3'), '6');
      expect(ExpressionCalculator.calculate('6 / 2'), '3');
    });

    test('复杂表达式测试', () {
      expect(ExpressionCalculator.calculate('1 + 2 * 3'), '7');
      expect(ExpressionCalculator.calculate('(1 + 2) * 3'), '9');
      expect(ExpressionCalculator.calculate('1 + 2 * 3 - 4 / 2'), '5');
    });

    test('小数精度测试', () {
      expect(ExpressionCalculator.calculate('1.5 * 2.3', 2), '3.45');
      expect(ExpressionCalculator.calculate('10 / 3', 4), '3.3333');
      expect(ExpressionCalculator.calculate('1.23456789', 3), '1.235');
    });

    test('负数测试', () {
      expect(ExpressionCalculator.calculate('-1 + 2'), '1');
      expect(ExpressionCalculator.calculate('2 * (-3)'), '-6');
      expect(ExpressionCalculator.calculate('(-2) * (-3)'), '6');
    });

    test('大数计算测试', () {
      const expression =
          '1+1234567890123456789.01234567890*'
          '(1+1234567890123456789.01234567890*1234567890123456789.01234567890/'
          '(1234567890123456789.01234567890-1))/12345678901234567890.12345678901-'
          '(40-1234567890123456789.01234567890)*1';

      expect(ExpressionCalculator.calculate(expression, 12), '1358024679135802429.113580246790');
    });

    test('较大数值计算测试', () {
      expect(
        ExpressionCalculator.calculate('999999999.999999999 * 999999999.999999999', 12),
        '999999999999999998.000000000000',
      );
    });

    test('异常处理测试', () {
      expect(
        () => ExpressionCalculator.calculate('1 / 0'),
        throwsA(isA<CalculationException>().having((e) => e.message, 'message', '计算错误: 除数不能为零')),
      );

      expect(
        () => ExpressionCalculator.calculate('1 + '),
        throwsA(isA<CalculationException>().having((e) => e.message, 'message', '计算错误: 操作数不足')),
      );

      expect(
        () => ExpressionCalculator.calculate('(1 + 2'),
        throwsA(isA<CalculationException>().having((e) => e.message, 'message', '计算错误: 括号不匹配')),
      );

      expect(
        () => ExpressionCalculator.calculate('1 # 2'),
        throwsA(isA<CalculationException>().having((e) => e.message, 'message', '计算错误: 无效字符: #')),
      );
    });

    test('空白输入测试', () {
      expect(ExpressionCalculator.calculate(''), '0');
      expect(ExpressionCalculator.calculate('  '), '0');
    });

    test('表达式格式化测试', () {
      expect(ExpressionCalculator.calculate(' 1  +  2 '), '3');
      expect(ExpressionCalculator.calculate('1+2+3'), '6');
      expect(ExpressionCalculator.calculate('1 +2+ 3'), '6');
    });
  });
}

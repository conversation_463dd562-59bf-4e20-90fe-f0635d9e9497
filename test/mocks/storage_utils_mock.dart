import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:mockito/mockito.dart';

/// StorageUtils 的模拟实现，用于单元测试
class MockStorageUtils extends Mock implements IStorageUtils {
  final Map<String, dynamic> _storage = {};
  final List<String> _setCalls = [];

  /// 获取存储的调用记录
  List<String> get setCalls => List.unmodifiable(_setCalls);

  /// 清除所有模拟数据
  void reset() {
    _storage.clear();
    _setCalls.clear();
  }

  /// 预设存储数据
  void setupStorage(Map<String, dynamic> initialData) {
    _storage.clear();
    _storage.addAll(initialData);
  }

  @override
  T? getValue<T>(String key, {bool personal = false}) {
    return _storage[key] as T?;
  }

  @override
  Future<void> setValue<T>(String key, T value, {bool personal = false, int? durationInSeconds}) async {
    _storage[key] = value;
    _setCalls.add(key);
  }

  @override
  Future<void> removeValue(String key, {bool personal = false}) async {
    _storage.remove(key);
  }

  @override
  bool containsKey(String key, {bool personal = false}) {
    return _storage.containsKey(key);
  }

  @override
  Future<void> clearAll({bool personal = false}) async {
    _storage.clear();
  }

  @override
  List<String> getAllKeys({bool personal = false}) {
    return _storage.keys.toList();
  }

  @override
  String getAssetScanLocation() {
    final userName = _storage[StorageKeys.userName] as String?;
    if (userName == null || userName.isEmpty) {
      throw Exception('用户名不存在');
    }
    return _storage[userName + StorageKeys.location] as String? ?? '';
  }

  @override
  Future<void> setAssetScanLocation({required String assetLocation}) async {
    final userName = _storage[StorageKeys.userName] as String?;
    if (userName == null || userName.isEmpty) {
      throw Exception('用户名不存在');
    }
    _storage[userName + StorageKeys.location] = assetLocation;
    _setCalls.add(userName + StorageKeys.location);
  }
}
